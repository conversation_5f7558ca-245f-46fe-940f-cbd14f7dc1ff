"""add differentiation_in table

Revision ID: 42d3170e1e1a
Revises: 60effec8ba52
Create Date: 2025-09-10 14:35:01.999998

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '42d3170e1e1a'
down_revision: Union[str, None] = '60effec8ba52'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('differentiation_in',
    sa.Column('request', sa.Integer(), nullable=False),
    sa.<PERSON>umn('max_items', sa.Integer(), nullable=True),
    sa.Column('max_time', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['request'], ['request.id'], ),
    sa.PrimaryKeyConstraint('request')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('differentiation_in')
    # ### end Alembic commands ###
