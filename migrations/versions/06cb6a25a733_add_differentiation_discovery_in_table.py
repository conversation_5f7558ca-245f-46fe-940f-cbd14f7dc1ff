"""add differentiation_discovery_in table

Revision ID: 06cb6a25a733
Revises: b48d52edc8b1
Create Date: 2025-09-13 17:56:59.981145

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '06cb6a25a733'
down_revision: Union[str, None] = 'b48d52edc8b1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('differentiation_discovery_in',
    sa.Column('request', sa.Integer(), nullable=False),
    sa.Column('max_items', sa.Integer(), nullable=True),
    sa.Column('max_time', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['request'], ['request.id'], ),
    sa.<PERSON>KeyConstraint('request')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('differentiation_discovery_in')
    # ### end Alembic commands ###
