"""add image_set column to differentiation_in

Revision ID: b48d52edc8b1
Revises: 42d3170e1e1a
Create Date: 2025-09-13 12:56:03.939382

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'b48d52edc8b1'
down_revision: Union[str, None] = '42d3170e1e1a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('differentiation_in', sa.Column('image_set', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'differentiation_in', 'image_set', ['image_set'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'differentiation_in', type_='foreignkey')
    op.drop_column('differentiation_in', 'image_set')
    # ### end Alembic commands ###
