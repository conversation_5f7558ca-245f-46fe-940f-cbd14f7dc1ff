"""Rename resolution column

Revision ID: 60effec8ba52
Revises: 9421952b2dc0
Create Date: 2025-06-07 19:38:26.313669

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '60effec8ba52'
down_revision: Union[str, None] = '9421952b2dc0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.alter_column('image_set', 'um_per_pixel', nullable=False, new_column_name='pix_per_mm')

def downgrade() -> None:
    op.alter_column('image_set', 'pix_per_mm', nullable=False, new_column_name='um_per_pixel')
