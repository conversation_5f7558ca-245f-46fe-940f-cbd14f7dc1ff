[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
dynamic = ["dependencies"]
name = "mellitos"
version = "1.3"
authors = [
  { name="<PERSON>", email="<EMAIL>" },
  { name="<PERSON>", email="<EMAIL>" },
  { name="<PERSON>hrey<PERSON> Rai", email="<EMAIL>" },
]
description = "The MelliCell Operating System"
readme = "README.md"
requires-python = ">=3.8"
classifiers = [
    "Programming Language :: Python :: 3",
    "Operating System :: OS Independent",
]

[tool.setuptools]
package-dir = {"" = "src"}

[tool.setuptools.dynamic]
dependencies = {file = ["requirements.txt"]}

[tool.ruff.lint]
ignore = [
    "E401",  # Multiple imports on one line
    "E701",  # Multiple statements on one line
]
