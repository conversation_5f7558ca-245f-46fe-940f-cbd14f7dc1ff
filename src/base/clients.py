#
#  A bunch of client singletons for the Google Cloud services
#
from google.cloud import storage as _storage, bigquery as _bigquery

from base.secrets import secrets
from base.logs import make_logger
log = make_logger(__name__)

_storage_clients = {}

def storage(name="development"):
    global _storage_clients
    sc = _storage_clients.get(name)
    if sc is None:
        log(f"Loading storage credentials for {name} from secrets")
        info = secrets().google._asdict()[name]
        sc = _storage.Client.from_service_account_info(info)
        _storage_clients[name] = sc
    return sc

_bigquery_client = None

def bigquery():
    global _bigquery_client
    if _bigquery_client is None:
        log("Loading bigquery credentials from secrets")
        info = secrets().google.development
        _bigquery_client = _bigquery.Client.from_service_account_info(info)
    return _bigquery_client

