import os.path
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

from base.secrets import secrets
from base.logs import make_logger
log = make_logger(__name__)

# If modifying these scopes, delete the file token.json.
SCOPES = ["https://www.googleapis.com/auth/spreadsheets.readonly"]

# The ID and range of a sample spreadsheet.
SAMPLE_SPREADSHEET_ID = "1Bg3ezlSAiyc-SBg9W9IRxyU2aiYys5dXCBa12FfmEJY"
SAMPLE_RANGE_NAME = "Donor Lots!A:L"

def get_creds():
    creds = None
    #cred_file = get_secret("credentials.json")
    token_file = "token.json"
    # The file token.json stores the user's access and refresh tokens, and is
    # created automatically when the authorization flow completes for the first
    # time.
    if os.path.exists(token_file):
        creds = Credentials.from_authorized_user_file(token_file, SCOPES)
    # If there are no (valid) credentials available, let the user log in.
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
        else:
            #flow = InstalledAppFlow.from_client_secrets_file(cred_file, SCOPES)
            flow = InstalledAppFlow.from_client_config(
                secrets().google.development, SCOPES
            )
            creds = flow.run_local_server(port=0)
        # Save the credentials for the next run
        with open(token_file, "w") as token:
            token.write(creds.to_json())
    return creds

def get_sheet_data(sheet_id, range):
    creds = get_creds()
    try:
        service = build("sheets", "v4", credentials=creds)
        sheet = service.spreadsheets()
        result = (
            sheet.values()
            .get(spreadsheetId=sheet_id, range=range)
            .execute()
        )
        values = result.get("values", [])
        if not values:
            log("No data found.")
            return
        log("  Values: ", values)
        #dicts = [dict(zip(values[0], row)) for row in values[1:]]
        return values
    except HttpError as err:
        log(err)
        return None

def head_norm(head):
    """Normalize a header name"""
    return head.lower().replace("_", " ").replace(" #", "no")

def match_heads(heads, props):
    pnorm = [head_norm(p) for p in props]
    hnorm = [head_norm(h) for h in heads]
    log(f"Props: {props} -> {pnorm}")
    log(f"Heads: {heads} -> {hnorm}")
    map = dict()
    missing = []
    for p in props:
        pn = head_norm(p)
        try:
            i = hnorm.index(pn)
            map[p] = heads[i]
        except ValueError:
            missing.append(p)
    if len(missing)>0:
        log(f"Missing: {missing}")
    unused = [h for h in heads if head_norm(h) not in pnorm]
    if len(unused)>0:
        log(f"Unused: {unused}")
    return map, missing, unused
    
    
    """Match the heads with the row"""
    return {head_norm(h):v for h, v in zip(heads, row)}