from typing import <PERSON><PERSON><PERSON><PERSON>
from datetime import datetime
import json

from base.logs import make_logger
from base.util import isNamedTuple
log = make_logger(__name__)
#
#   Read and write JSON files from NamedTuples
#
def json_serial(obj):
    if isinstance(obj, datetime):
        return obj.isoformat()
    raise TypeError(f"Type {type(obj)} not serializable")    


def encode(t: NamedTuple) -> str:
    return json.dumps(encode_tuples(t), default=json_serial, indent=4)

def decode(cls: type, s: str) -> NamedTuple:
    return decode_tuples(cls(**json.loads(s)))

def decode_tuples(x: NamedTuple) -> NamedTuple:
    """Convert dictionaries in typed fields to the corresponding types"""
    if not (isinstance(x, tuple) and hasattr(type(x), "_fields")):
        return x
    values = []
    for f in type(x)._fields:
        value = getattr(x, f)
        field_type = x.__class__.__annotations__[f]
        try:
            if isinstance(value, dict) and isNamedTuple(field_type):
                value = field_type(**value)
        except Exception as e:
            log(f"Error decoding {type(value)} as '{f}' : {field_type}")
            log(f"  Value: {value}")
            raise e
        values.append(decode_tuples(value))
    return x.__class__(*values)

def encode_tuples(x: NamedTuple) -> dict:
    """Convert NamedTuples to dictionaries"""
    if not (isinstance(x, tuple) and hasattr(type(x), "_fields")):
        return x
    return {f: encode_tuples(getattr(x, f)) for f in type(x)._fields}
