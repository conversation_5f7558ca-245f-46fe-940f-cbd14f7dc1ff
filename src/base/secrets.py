#
# Manage secrets for Mellitos
#
import os

from typing import NamedTup<PERSON>
from base.appinfo import root_path
from base.codec import encode, decode
from base.logs import make_logger

log = make_logger(__name__)    

# Define the structure of the secrets
#   This structure must match the secrets.json file format
class DatabaseCredentials(NamedTuple):
    user: str
    password: str
    name: str

class DatabaseSecrets(NamedTuple):
    local: DatabaseCredentials
    development: DatabaseCredentials
    production: DatabaseCredentials

class GoogleSecrets(NamedTuple):
    development: dict  # Google Cloud credentials for development project
    production: dict   # Google Cloud credentials for production
    sheets: dict       # Google Sheets credentials

class AuthSecrets(NamedTuple):
    salt: str          # Salt for hashing portal passwords

class MellitosSecrets(NamedTuple):
    database: DatabaseSecrets
    google: GoogleSecrets
    auth: AuthSecrets


# Singleton for secrets
#   This will load from the secrets.json file only once
_secrets = None
def secrets():
    global _secrets
    if _secrets is None:
        fn0 = os.path.join(root_path, ".secrets", "secrets.json")
        fn = fn0
        if not os.path.exists(fn):
            fn = "/run/secrets/secrets.json"
            if not os.path.exists(fn):
                raise FileNotFoundError(f"Secrets file not found: {fn} or {fn0}")
        with open(fn, "r") as f:
            _secrets = decode(MellitosSecrets, f.read())
    return _secrets


# Test the secrets loading
if __name__ == "__main__":
    # Load secrets and print them
    # print(secrets().to_json())
    log(f"Secrets: {secrets()}")
    log(f"Dev: {secrets().google._asdict()['development']}")
    #log(f"Json: {encode(secrets())}")
