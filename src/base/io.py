import gzip
from io import IOBase, BytesIO
import os
from typing import IO, Callable, List, NamedTuple
import pandas as pd
from datetime import datetime
import requests
import pyarrow as pa 
from pyarrow.parquet import ParquetFile
import imageio
from tqdm import tqdm
from base.logs import make_logger
from base.util import isNamedTuple, mform, perc

log = make_logger(__name__)

class CsvIO:
    def __init__(self, sep: str = ","):
        """Initialize the CsvIO class with a specific separator."""
        self.sep = sep
        self.mode = ""
        
    def writer(self, fd, df):
        df.to_csv(fd, index=False)
        
    def reader(self, fd, **_):
        return pd.read_csv(fd)

class ParquetIO:
    # Methods needed to cache lists using the @cached decorator
    @classmethod
    def writer(cls, fd, df):
        df.to_parquet(fd, index=False)

    @classmethod
    def reader(cls, fd, **_):
        return pd.read_parquet(fd)

    mode = "b"

class ImageIO:
    def __init__(self, image_format: str, seekable: bool = False):
        """Initialize the ImageIO class with a specific image format."""
        self.image_format = image_format
        self.seekable = seekable
        self.mode = "b"
    def writer(self, fd, arr):
        # log(f"WRITE {self.image_format} {self.mode} {self.seekable}: {arr.shape} {arr.dtype}")
        # if self.seekable:
        #     log(f"Seekable {self.image_format}: {fd.seekable()} {fd}")
        if self.seekable and not fd.seekable():
            # Create a in memory IO object to allow seeking
            fdm = BytesIO()
            imageio.v2.imwrite(fdm, arr, format=self.image_format)
            fdm.seek(0)
            chunk_size = 1024 * 1024  # 1 MB
            if len(fdm.getbuffer()) > 20*1024*1024:
                with tqdm(total=fdm.getbuffer().nbytes, unit="B", unit_scale=True) as pbar:
                    while chunk := fdm.read(chunk_size):
                        fd.write(chunk)
                        pbar.update(len(chunk))
            else:
                while chunk := fdm.read(chunk_size):
                    fd.write(chunk)
        else:
            imageio.v2.imwrite(fd, arr, format=self.image_format)

    def reader(self, fd, *, deco=None, owner=None):
        return imageio.v2.imread(fd, format=self.image_format)
    
class NamedTupleIO:
    """IO for NamedTuple objects, using the @cached decorator."""
    def __init__(self, cls: type, ext: str):
        self.cls = cls
        self.io = extension_io(ext)
        self.mode = self.io.mode

    def writer(self, fd, xs: List[NamedTuple]):
        """Write a list of NamedTuples to a file."""
        df = pd.DataFrame([x._asdict() for x in xs])
        self.io.writer(fd, df)

    def reader(self, fd, **_):
        df = self.io.reader(fd)
        return [self.cls(**row._asdict()) for row in df.itertuples(index=False)]

class ListIO:
    # Methods needed to cache lists using the @cached decorator
    @classmethod
    def writer(cls, fd, xs):
        """Write a list to a file, one item per line."""
        for x in xs: fd.write(x+"\n")

    @classmethod
    def reader(cls, fd, **_):
        return [line.strip() for line in fd.readlines()]

    mode = ""

def extension_io(ext: str):
    """Return the appropriate IO class for a given file extension."""
    ext = ext.lower()
    map = {
        "csv": CsvIO(), "tsv": CsvIO("\t"), "parquet": ParquetIO,
        **{f:ImageIO(f) for f in ["png", "jpeg"]},
        **{f:ImageIO(f, seekable=True) for f in ["tiff"]},
        "lst": ListIO,
    }
    # Aliases for some image formats
    map |= {k: map[v] for k,v in {"jpg": "jpeg", "tif": "tiff"}.items()}
    return map[ext]

#
#  A decorator for cached objects
#
class cached_decorator(object):
    """A decorator for cached data frames."""
    def __init__(self, func, fn: str, ext: str = None, cls = None):
        self.__name__ = func.__name__
        self.__module__ = func.__module__
        self.__doc__ = func.__doc__
        self.func = func
        self.fn = fn
        if not ext:
            ext = fn.split(".")[-1]
            if ext in ["Z", "gz"]:
                ext = fn.split(".")[-2]
        if cls is not None:
            self.io = cls
            if isNamedTuple(cls):
                self.io = NamedTupleIO(cls, ext)
        else:
            self.io = extension_io(ext)

    def __get__(self, obj, type=None):
        if obj is None: return self
        if self.__name__ in obj.__dict__:
            return obj.__dict__[self.__name__]
        fn = self.fn
        if "{}" in fn: 
            fn = self.fn.format(obj.file_name)
        if not hasattr(obj, "store"):
            raise ValueError(f"Object {obj} does not have a 'store' attribute needed for caching.")
        # pass environment to the reader for more flexible construction
        def reader(fd):
            return self.io.reader(fd, deco=self, owner=obj)
        x = obj.store.cached(fn, lambda: self.func(obj), self.io.writer, reader, self.io.mode)
        obj.__dict__[self.__name__] = x
        return x

def cached(fn: str, cls = None):
    def decorator(func):
        return cached_decorator(func, fn, cls=cls)
    return decorator

#
#  A decorator for cached dataframes (special case of `cached`, for back compatibility)
#
def cached_df(fn: str):
    def decorator(func):
        return cached_decorator(func, fn, "csv")
    return decorator

#
#   Stream from a URL
#
class open_url:
    def __init__(self, url: str, mode: str = "r", size: int = 0) -> IO:
        self.url = url
        self.mode = mode
        self.size = size
        
        """Open a URL and return a file-like object."""
        st = f"{mform(size)}b" if size else "unknown"
        log(f"Opening {url} in mode {mode} with size {st}")
        response = requests.get(url, stream=True)
        if response.status_code != 200:
            raise ValueError(f"Failed to open URL {url}: {response.status_code}")
        # See if we can get a size estimate
        if 'Content-Length' in response.headers:
            size = int(response.headers.get('Content-Length'))
            if self.size == 0: self.size = size
            if size != self.size:
                log.warning(f"Warning: content mismatch: {size} != {self.size}, using {self.size}")
                log.warning(f"  {url}")
        if self.size > 0:
            log(f"  Expecting {mform(self.size)}b")
        if self.url.endswith(".gz"):
            log(f"  Opening gzipped URL {self.url}")
            self.file = gzip.open(response.raw, mode)
        else:
            self.file = response.raw
        self.input = response.raw
        
    def bytes(self) -> int:
        """Return the number of compressed bytes read so far."""
        return self.input.tell()
    
    def stream_csv(self, 
        select: Callable[[list],bool],
        expect_cols: List[str] = None,
        use_cols: List[str] = None
    ) -> pd.DataFrame:
        """Stream a CSV file from the URL and filter rows based on a condition.
           Returns a DataFrame with the selected rows.
        """
        heads = None
        data = []
        with self.file as f:
            unit = 1000000  # Report progress in Mb of uncompressed data
            with tqdm(unit="mb") as pbar:
                for i, line in enumerate(f):
                    if i % 10000 == 0: 
                        pbar.n = self.bytes() // unit
                        pbar.set_description(f"found {perc(len(data), i)}")
                        pbar.refresh()
                    if line.startswith("#"):
                        if line.startswith("##"):
                            #log(f"  {line.strip()[2:]}")
                            continue
                        if heads is None:
                            heads = line.strip()[1:].split("\t")
                            log(f"  Found headers: {heads}")
                            if expect_cols:
                                for i, c in enumerate(expect_cols):
                                    if heads[i] != c:
                                        raise ValueError(f"Unexpected header {heads[i]} at position {i}, expected {c}")
                            use_ix = range(len(heads)) if use_cols is None else [heads.index(col) for col in use_cols]
                            if any(ix < 0 for ix in use_ix):
                                raise ValueError(f"Missing columns in headers: {use_cols} {use_ix}")
                            if self.size:
                                pbar.total = self.size // unit
                                log(f"  Expect {self.size} bytes in {self.url}")
                            continue
                        else:
                            raise ValueError(f"Unexpected second header line: {line.strip()}")
                    row = line.strip().split("\t")
                    if len(row) != len(heads):
                        log(f"  Unexpected line: {line.strip()}")
                        raise ValueError(f"Unexpected number of columns in line {i}: {len(row)} != {len(heads)}")
                    if select({h:f for h,f in zip(heads, row)}):      
                        data.append([row[ix] for ix in use_ix])
        log(f"Read {len(data)}/{i} rows from {self.url}")
        return pd.DataFrame(data, columns=use_cols)


#
#   Stream and filter a large parquet file
#
def stream_parquet(
    fd: IO, 
    selects: List[Callable[[pd.DataFrame],pd.DataFrame]], 
    size: int = 0,
    description: str = ""
) -> List[pd.DataFrame]:
    """Stream a parquet file and select rows based on a condition.
         returns a DataFrame with the selected rows for each function in `select`.
    """
    pf = ParquetFile(fd)
    nrows = pf.metadata.num_rows
    chunk_size = 65536
    if description: description = f"{description} "
    st = f"{mform(size)}b" if size else "size unknown"
    log(f"  Parquet file opened: {nrows} rows, {pf.metadata.num_columns} columns, {st}.")
    batches = pf.iter_batches(batch_size = chunk_size, use_threads=False)
    sel_dfs = [pd.DataFrame()]*len(selects)
    n = 0
    t0 = datetime.now()
    with tqdm(total=nrows, unit="rows", desc=description) as pbar:
        for ci in range((nrows + chunk_size - 1) // chunk_size):
            chunk = next(batches)
            #log(f"  Chunk {ci}, {len(chunk)} rows")
            df0 = pa.Table.from_batches([chunk]).to_pandas()
            n += len(df0)
            if ci == 0:
                # First chunk, print the DataFrame
                print(df0)
            # select rows where phenotype_id contains the locus name
            for i, select in enumerate(selects):
                df = select(df0)
                if len(df) > 0: 
                    #log(f"  Chunk {ci} selected {len(df)} rows")
                    # If this is the first chunk, initialize the DataFrame
                    if sel_dfs[i].empty:
                        sel_dfs[i] = df
                    else:
                        sel_dfs[i] = pd.concat([sel_dfs[i], df], ignore_index=True)
            nr = sum(len(df) for df in sel_dfs)
            t = f"{100*nr/n:.0f}%" if n > 0 else "rows"
            t += f" ({len(sel_dfs)} dfs)" if len(sel_dfs)>1 else ""
            pbar.set_description(f"{description}{nr} {t}")
            pbar.update(len(chunk))
    rt = f". Rate: {mform(size/(datetime.now() - t0).total_seconds())}b/s." if size else ""
    log(f"  Extracted {len(sel_dfs)} DataFrames{rt}")
    # for sel_df in sel_dfs:
    #     log(f"    {sel_df.shape}")
    if n != nrows:
        raise ValueError(f"Expected {nrows} rows, but got {n} rows.")
    return sel_dfs

class SeekableFileObject(IOBase):
    """A class to handle IO operations with seekable streams."""
    
    def __init__(self, fd: IO, verbose: bool = False):
        self.fd = fd
        self.pos = 0
        self.size = None
        self.verbose = verbose
        if self.verbose:
            log(f"SEEKABLE: {self.fd}")
            
        # Keep a buffer of read or written bytes to support seeking backwards
        self.fd_pos = 0   # Position in the file descriptor
        self.buf_s = 1024*1024
        self.buffer = memoryview(bytearray(self.buf_s))
        self.buf_b = 0   # Start of the buffer

    def _state(self):
        """Summarize the current state of the stream."""
        return f"{self.fd_pos} {self.pos} [{self.buf_b}-{self.buf_b + self.buf_s}]"
    
    def read(self, size: int = -1) -> bytes:
        """Read bytes from the buffer and/or stream."""

        # read until end of file
        if size < 0:
            if self.verbose:
                log(f"READ: {size} (eof) at {self._state()}")
            bs = self.fd.read()
            if self.pos > self.fd_pos:
                # skip some bytes
                bs = bs[self.pos - self.fd_pos:]
            elif self.pos < self.fd_pos:
                # Add any bytes still inthe buffer
                b1 = self.buffer[self.pos - self.buf_b:self.fd_pos - self.buf_b]
                bs = b1 + bs
            s = len(bs)
            self.pos += s
            self.fd_pos = self.pos
            return bs
        
        # read a specific number of bytes
        b = self.pos
        e = self.pos + size
        if self.verbose:
            log(f"READ: {size} () at {self._state()}")
        # Shift the buffer if needed
        if e > self.buf_b + self.buf_s:
            # make a new buffer centered around the current position
            self.buffer = memoryview(bytearray(self.bufsize))
            self.buf_b = e - self.buf_s // 2
            # read the bytes until the start of the buffer
            if self.fd_pos < self.buf_b:
                b1 = self.fd.read(self.buf_b - self.fd_pos)
                self.fd_pos = self.buf_b
        # Catch up reading into the buffer, until e
        if self.fd_pos < b:
            s = self.fd.readinto(self.buffer[self.fd_pos-self.buf_b:e-self.buf_b])
            self.fd_pos = e
        # get the right bytes
        bp = max(0, b - self.buf_b)
        bs = self.buffer[bp:e-self.buf_b]
        if b < self.buf_b:
            # Add any bytes read from before the buffer
            bs = b1[:b - self.buf_b] + bs
        self.pos = e
        if self.verbose:
            log(f"  {len(bs)} {self._state()}")
        if len(bs) != size:
            raise ValueError(f"READ: expected {size} bytes, got {len(bs)} bytes.")
        return bs

    def write(self, bs: bytes) -> int:
        """Write bytes to the buffer, so we can seek back and read them again."""
        b = self.pos
        e = self.pos + len(bs)
        if self.verbose:
            log(f"WRITE: {e-b} ({b}-{e}) at {self._state()}")
        # Shift the buffer if needed
        if e > self.buf_b + self.buf_s:
            # write the bytes left in the old buffer
            self.fd.write(self.buffer[self.fd_pos-self.buf_b:self.pos-self.buf_b])
            self.fd_pos = self.pos
            # make a new buffer centered around the current position
            self.buffer = memoryview(bytearray(self.buf_s))
            self.buf_b = e - self.buf_s // 2
            # write the bytes between buffers, if any
            i = max(self.pos, self.buf_b)
            if self.pos < i:
                self.fd.write(bs[:i-self.pos])
                self.fd_pos = i
            self.buffer[:e-self.buf_b] = bs[i-self.pos:]
            if self.verbose:
                log(f"  shift {self.buf_b} at {self._state()}")
        else:
            self.buffer[b - self.buf_b:e - self.buf_b] = bs
        self.pos = e
        if self.verbose:
            log(f"  {e-b}/{len(bs)} at {self._state()}")
        return e - b

    def _reopen(self, msg: str):
        self.fd.close()
        log.warning(f"Reopening stream to {msg}")
        self.pos = 0
        raise NotImplementedError("Reopening this stream is not supported.")

    def seek(self, offset: int, whence: int = os.SEEK_SET) -> int:
        """Seek to a specific position in the stream."""
        ofs = offset
        if whence == os.SEEK_CUR:
            ofs += self.pos
        elif whence == os.SEEK_END:
            if self.size is None:
                # read chunks until end of file
                while True:
                    chunk = self.fd.read(4096)
                    if not chunk:
                        break
                    self.pos += len(chunk)
                log(f"Determined size: {self.pos}")
                self.size = self.pos
            ofs += self.size
        if self.verbose:
            log(f"SEEK: {offset}({whence}) | {self.pos} -> {ofs} at {self._state()}")
        
        self.pos = ofs
        if self.verbose:
            log(f"  {self._state()}")
        return self.pos

    def tell(self) -> int:
        """Return the current position in the stream."""
        if self.verbose:
            log(f"TELL: {self.pos}")
        return self.pos
    
    def close(self):
        """Close the stream."""
        if self.verbose:
            log(f"CLOSE: {self.pos}")
        if self.fd.writable():
            # Write any remaining buffered bytes
            self.fd.write(self.buffer[self.fd_pos - self.buf_b:self.pos - self.buf_b])
        self.fd.close()

