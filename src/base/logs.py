import threading
from typing import IO
from datetime import datetime
import psutil
import logging
from base.appinfo import appinfo

#
#   Logging
#
#   Usage:
#     from base.util import make_logger
#     log = make_logger()
#
#   Then use log.debug(), log.info(), log.warning(), log.error(), log.critical()
#
class LogFormatter(logging.Formatter):
    
    def __init__(self, color: bool = True):
        super().__init__()
        self._last_time = None
        
        # Do colors
        self.grey = "\x1b[37;20m" if color else ""
        self.dark = "\x1b[38;20m" if color else ""
        self.yellow = "\x1b[33;20m" if color else ""
        self.red = "\x1b[31;20m" if color else ""
        self.bold_red = "\x1b[31;1m" if color else ""
        self.reset = "\x1b[0m" if color else ""
        self.cols = {
            logging.DEBUG: self.grey,
            logging.INFO: self.dark,
            logging.WARNING: self.yellow,
            logging.ERROR: self.red,
            logging.CRITICAL: self.bold_red
        }
 
    def format(self, record):
        if record.levelno == logging.INFO:
            msg = record.getMessage()
        else:
            msg = self.cols[record.levelno]+record.getMessage()+self.reset
       
        # Do process id...
        pid = str(threading.get_native_id()%100)
        
        # Do time...
        if self._last_time is None:
            time = "    "
        else:
            t = record.created - self._last_time
            if t < 10: time = f"{1000*t:4.0f}"
            elif t < 1000: time = f"{t:3.0f}s"
            elif t < 60000: time = f"{t/60:3.0f}m"
            elif t < 3600000: time = f"{t/3600:3.0f}h"
            else: time = f"{t/86400:3.0f}d"
        self._last_time = record.created
        
        #  Do memory...
        mem = psutil.Process().memory_info().rss
        def to_string(mem):
            for u in ['B', 'K', 'M', 'G', 'T']:
                if mem < 10: return f"{mem:3.1f}{u}"
                if mem < 1000: return f"{mem:3.0f}{u}"
                mem /= 1024.0
        mem = to_string(mem)

        #  Do origin...
        width = 8
        lno = f"{record.lineno}"
        w = width - len(lno) - 1
        mod = record.module[:w]
        mod += " "*(w-len(mod)) if len(mod) < w else ""
        ori = f"{mod}:{lno}"

        return self.grey+f"{pid}{ori} {mem}{time}: "+self.reset+msg
_pids = {}

def make_logger(name):
    log = logging.getLogger(name)
    log.setLevel(logging.DEBUG)
    ch = logging.StreamHandler()
    ch.setLevel(logging.DEBUG)
    formatter = LogFormatter()
    ch.setFormatter(formatter)
    log.addHandler(ch)
    # monkey patch the logger to add the shortcut for info
    def info(self, msg, *args, **kwds):
        return self._log(logging.INFO, msg, args, stacklevel=2, **kwds)
    log.__class__.__call__ = info
    def intro():
        log.info(f"{appinfo}")
        log.info(f"  Starting log at {datetime.now()}")
    log.intro = intro
    return log

class redirect:
    def __init__(self, log: logging.Logger, fd: IO, fn: str = None):
        self.log = log
        self.fn = fn or f"{fd}"
        self.fd = fd
        self.handler = logging.StreamHandler(fd)
        self.handler.setLevel(logging.DEBUG)
        self.handler.setFormatter(LogFormatter(color=False))
    
    def __enter__(self):
        self.log.parent.addHandler(self.handler)
        self.log.info(f"{datetime.now()} Logging output to {self.fn}")
        self.log.intro()
        return self
    
    def __exit__(self, type, value, traceback):
        self.log.info(f"{datetime.now()} End of log to {self.fn}.")
        self.log.parent.removeHandler(self.handler)
 