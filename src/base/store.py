import os
import io
import requests
import tarfile
import zipfile
import pandas as pd
from datetime import datetime, timezone, timedelta
from typing import IO, Callable, List

from tqdm import tqdm
from base.gzip import GzipFileStream
from base.logs import make_logger
from app.config import settings
from base.clients import storage

log = make_logger(__name__)

base_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

#
#   Singletons for file storage
#
def cache_store():
    """This is the default store for caching data."""
    return file_store("cache")

def logs_store():
    """This is the default store for storing logs."""
    return file_store("logs")


def file_store(name: str, local: str = None, remote: str = None):
    """Manage a set of stores, local or on GCS depending on environment"""
    global _stores
    if name not in _stores:
        # If running locally, use local storage unless otherwise specified, else use GCS
        if settings.DBHOST == "0.0.0.0" or settings.DBHOST == "localhost":
            if remote:
                _stores[name] = GcsStore("mellitos-"+remote)
            elif local:
                _stores[name] = FileStore(local+"/"+name)
            else:
                _stores[name] = FileStore(name)
        else:
            _stores[name] = GcsStore("mellitos-"+name)
    return _stores[name]
_stores = {}


class File:
    """Convenience class to represent a file in a store"""
    def __init__(self, store, path):
        self.path = path
        self.store = store
    
    def __repr__(self):
        return f"{self.store.tag}{self.path}"
    
    def exists(self):
        return self.store.exists(self.path)
    
    def last_modified(self):
        return self.store.last_modified(self.path)
    
    def open(self, mode="r"):
        return self.store.open(self.path, mode)       
           
    def stream(self):
        return self.store.stream(self.path)       
           
    def delete(self):
        return self.store.delete(self.path)

class Store:
    def __repr__(self):
        return f"{self.tag}"

    def decompress(self, fn: str, mode="r"):
        """Open a compressed file, decompressing it if necessary."""
        if fn.endswith(".gz"):
            # Open a gzip file
            size = self.size(fn)
            log(f"Decompressing {size} {self.tag}{fn}")
            fd = self.open(fn, "rb")
            gz = GzipFileStream(fd, size)
            return gz.file()
        else:
            log(f"Opening {self.tag}{fn}")
            return self.open(fn, mode)
    
    def tar_manifest(self, url: str, manifest: str) -> List[str]:
        """Download a manifest for a tar archive, if it doesn't exist locally."""
        mfn = f"{manifest}-manifest.txt"
        if not self.exists(mfn):
            self.download_tar(url, manifest=manifest, select = lambda f: False)
        with self.open(mfn, "r") as mf:
            fns = [f.strip() for f in mf.readlines()]
        return fns
        
    def download_tar(self,
        url: str, 
        manifest: str = None, 
        select: Callable[[str], bool] = None,
        expect: int = 0,
    ) -> IO:
        """Download files from a remote tar archive, if they don't exist locally."""
        msg = None
        if isinstance(select, str):
            # If select is a string, use it as a regex pattern
            msg = f"files matching {select}"
            import re
            pattern = re.compile(select)
            def select(f): return pattern.match(f)
        elif isinstance(select, list):
            fns = select
            expect = len(fns)
            def select(f): return f in fns
        if not msg:
            nf = "all"
            if select:
                nf = "some"
                if expect > 0:
                    nf = f"{expect}"
            msg = f"{nf} files"
        if manifest:
            msg = f"{msg}, manifest in {manifest}"
        log(f"Extracting {msg}")
        log(f"  {url}")
        
        complete = False
        try:  # make sure to close the manifest
            # If there is a manifest, count the files
            mfd = None
            if manifest:
                mfn = f"{manifest}-manifest.txt"
                if self.exists(mfn):
                    log(f"  {mfn} already exists, expect {expect} files")
                    # Check the manifest for the right number of files
                    c = 0
                    done = True
                    with self.open(mfn, "r") as mf:
                        for f in mf.readlines():
                            f = f.strip()
                            if not select or select(f):
                                if not self.exists(f):
                                    done = False    
                                c += 1
                    if expect and c != expect:
                        raise FileNotFoundError(f"{c}/{expect} files found in {url}")
                    if done:
                        log(f"All {c} files found.")
                        return
                else:
                    mfd = self.open(mfn, "w")
                
            # Stream the tar file
            response = requests.get(url, stream=True)
            response.raise_for_status()  # Raise HTTPError for bad responses (4xx or 5xx)
            with tarfile.open(fileobj=response.raw, mode="r|*") as tar:
                found = 0
                for member in tar:
                    if member.isfile():  # Process only files, skip directories etc.
                        log(f"|{member.size:10d} {member.name}")
                        if mfd:
                            mfd.write(f"{member.name}\n")
                            mfd.flush()
                        if not select or select(member.name):
                            if self.exists(member.name):
                                log("|             found existing.")
                            else:
                                with tar.extractfile(member) as m:
                                    # stream to the local file
                                    with self.open(member.name, "wb") as f:
                                        while chunk := m.read(4192): 
                                            f.write(chunk)
                                    if select:
                                        log("|             extracted and saved.")
                            found += 1
                            if not mfd and expect>0 and found==expect:  
                                # Skip the rest of the tar if we found all files, 
                                # unless we are writing the manifest
                                break
                complete = True
                if expect>0 and found < expect:
                    raise FileNotFoundError(f"{expect-found} files not found in {url}")
        finally:
            # Close the manifest
            if mfd:
                mfd.close()
                log(f"  Closed {mfn}")
                # make sure it's always complete
                if not complete:
                    self.delete(mfn)
                    log(f"  Deleted {mfn}")

    def download(self, url: str, fn: str, mode="r") -> IO:
        """Download a file from a URL if it doesn't exist locally."""
        remote_file = os.path.join(url, fn)
        if not self.exists(fn):
            log(f"Downloading {url+fn} to {self.tag}{fn}")
            from urllib.request import urlopen
            with urlopen(remote_file) as r:
                with self.open(fn, "wb") as f:
                    f.write(r.read())
        log(f"Opening {self.tag}{fn}")
        
        # unzip if necessary
        if fn.endswith(".zip"):
            fd = self.open(fn, "rb")
            archive = zipfile.ZipFile(fd, 'r')
            for tn in archive.namelist():
                log(f"  {tn}")
            fd = archive.open(tn, mode)
        else:
            fd = self.open(fn, mode)
        return fd
    
    def copy_to(self, dest: 'Store', fn: str):
        """Copy files from this store to another."""
        log(f"Copy from {self.tag} to {dest.tag}:") 
        chunk_size = 1024*1024  # 1 MB
        def copy(fn):
            log(f"  {fn}")
            if dest.exists(fn):
                raise FileExistsError(f"File {dest.tag}{fn} already exists.")
            tq = None
            if self.size(fn) > 10000000:
                tq = tqdm(total=self.size(fn), unit='B', unit_scale=True, desc=f"Copying {fn}")
            with dest.open(fn, "wb") as fd:
                with self.open(fn, "rb") as src_fd:
                    while chunk := src_fd.read(chunk_size):
                        fd.write(chunk)
                        if tq:
                            tq.update(len(chunk))
            if tq:
                tq.close()
        if self.exists(fn):
            n = 1
            copy(fn)
        else:
            n = 0
            for sfn in self.list(fn):
                n += 1
                copy(sfn)
        log(f"Copied {n} files.") 
        
    def cached(self, fn: str, make, write, read, mode):
        """Return a cached object, computing it if necessary."""
        def dt(d: timedelta): return f"{1000*d.total_seconds():.2f} ms"
        t0 = datetime.now()
        r = None
        if not self.exists(fn):
            t1 = datetime.now()
            x = make()
            t2 = datetime.now()

            # Write the data to the file
            # log(f"SAVE {self.tag}{fn}")
            write(self.open(fn, "w"+mode), x)
            t = datetime.now()
            r = f"Computed in {dt(t-t0)} (exists: {dt(t1-t0)}, compute: {dt(t2-t1)}, save: {dt(t-t2)}, load: {'{}'}) : {fn}"
        t3 = datetime.now()
        log(f"Loading {self.tag}{fn}")
        x = read(self.open(fn, "r"+mode))
        if not r: 
            r = f"Retrieved in {dt(datetime.now()-t0)} (exists: {dt(t3-t0)}, load: {'{}'}) : {self.tag}{fn}"
        log(r.format(dt(datetime.now()-t3)))
        return x
    
    def cached_df(self, fn: str, make_df):
        """Special case for dataframes"""
        return self.cached(fn, make_df, 
            lambda fd, df: df.to_csv(fd, index=False, sep="\t" if fn.endswith(".tsv") or fn.endswith(".tsv.gz") else ","), 
            lambda fd: pd.read_csv(fd, sep="\t" if fn.endswith(".tsv") or fn.endswith(".tsv.gz") else ","),
            ""
        )

class GcsStore(Store):
    """A class for caching data in Google Cloud Storage."""
    def __init__(self, bucket, project="development", path: str = "", user_project: str = None):
        if isinstance(bucket, str):
            bucket = storage(project).bucket(bucket, user_project=user_project)
        self.bucket = bucket
        self.project = project
        if path:
            path = path + "/"
        self.path = path
        self.name = self.bucket.name.replace("mellitos-", "")
        self.tag = f"gs://{self.bucket.name}/{path}"
    
    def __truediv__(self, sub: str):
        return GcsStore(self.bucket, self.project, self.path + sub)

    def cp_local(self, src: str, dest: str, fake: bool = False):
        """Copy files from the GCS bucket to a local directory."""
        log(f"Copy from {self.tag} to {dest}:") 
        if self.exists(src):
            n = 1
            fn = os.path.join(dest, os.path.basename(src))
            os.makedirs(os.path.dirname(fn), exist_ok=True)
            log(f"  {src} -> {fn}")
            with open(fn, "wb") as fd:
                self.bucket.blob(self.path+src).download_to_file(fd)
        else:
            blobs = self.bucket.list_blobs(prefix=self.path+src)  # Get list of files
            log(f"  {src} -> {dest}")
            n = 0
            for blob in blobs:
                n += 1
                fn = blob.name.replace(src, "")[1:]
                dn = os.path.join(dest, fn)
                log(f"    {n}: {fn} -> {dn}")
                os.makedirs(os.path.dirname(dn), exist_ok=True)
                if fake:
                    with open(dn, "w") as fd:
                        fd.write("Fake file")
                else:
                    blob.download_to_filename(dn)
        log(f"Copied {n} files.") 
                  
    def stream(self, fn: str) -> io.BytesIO:
        s = io.BytesIO()
        self.bucket.blob(self.path+fn).download_to_file(s)
        s.seek(0)
        return s
    
    def signed_url(self, fn: str) -> str:
        url = self.bucket.blob(self.path+fn).generate_signed_url(datetime.max)
        log(f"URL: {url}")
        return url
        
    def list(self, fn: str = "/") -> list[str]:
        if fn.endswith("/"):
            fn = fn[:-1]

        bs = storage().list_blobs(self.bucket, prefix=self.path+fn)
        bns = [b.name for b in bs]
        fns = list(set(bn.replace(self.path+fn, "", 1).lstrip("/").split("/")[0] for bn in bns))
        # log(f"  LIST: {self.tag} {fn} {self.path+fn} -> {bns}")
        return fns

    def exists(self, fn: str) -> bool:
        #log(f"Checking {self.tag}{path}")
        return self.bucket.blob(self.path+fn).exists()
    
    def size(self, fn: str) -> int:
        size = self.bucket.get_blob(self.path + fn).size
        return size
    
    def last_modified(self, fn: str) -> datetime:
        if not self.exists(fn): return None
        blob = self.bucket.blob(self.path+fn)
        blob.reload()
        return blob.updated or blob.time_created
    
    def open(self, fn: str, mode="r"):
        if "w" in mode and "b" in mode:
            # Avoid gcloud.io error on flush
            return self.bucket.blob(self.path+fn).open(mode=mode, ignore_flush=True)
        else:  
            return self.bucket.blob(self.path+fn).open(mode=mode)
           
    def delete(self, fn: str):
        self.bucket.blob(self.path+fn).delete()
        
    def rename(self, src: str, dst: str):
        self.bucket.blob(self.path+src).rename(self.path+dst)


class FileStore(Store):
    """A class for caching data on local disk."""
    def __init__(self, root = "", path = ""):
        if root and not root.endswith("/"): 
            root = root + "/"
        self.root = root
        if path and not path.endswith("/"): 
            path = path + "/"
        self.path = path
        self.name = root.split("/")[-2]
        self.tag = f"file://{root}{path}"
    
    def __truediv__(self, sub: str) -> "FileStore":
        return FileStore(self.root, self.path + sub)

    def _path(self, fn: str) -> str:
        p = self.root + self.path + fn
        if not p.startswith("/"):
            p = base_path + "/" + p
        return p
    
    def list(self, fn: str = "/") -> list[str]:
        fns = os.listdir(self._path(fn))
        return fns

    def exists(self, fn: str) -> bool:
        return os.path.exists(self._path(fn))
        
    def size(self, fn: str) -> int:
        return os.path.getsize(self._path(fn))
        
    def last_modified(self, fn: str) -> datetime:
        if not self.exists(fn): return None
        return datetime.fromtimestamp(os.path.getmtime(self._path(fn)), tz=timezone.utc)
    
    def open(self, fn: str, mode="r") -> IO:
        if "w" in mode:
            os.makedirs(os.path.dirname(self._path(fn)), exist_ok=True)
        return open(self._path(fn), mode)
    
    def delete(self, fn: str):
        os.remove(self._path(fn))
        
    def rename(self, src: str, dst: str):
        os.rename(self._path(src), self._path(dst))

# Make decorator
def make(trg: str, *srcs: str):
    def decorator(func):
        return make_decorator(func, trg, srcs)
    return decorator

class make_decorator(object):
    """A decorator for cached data frames."""
    def __init__(self, func, trg: str, srcs: list[str]):
        self.__name__ = func.__name__
        self.__module__ = func.__module__
        self.__doc__ = func.__doc__
        self.func = func
        self.trg = trg
        self.srcs = srcs

    def __get__(self, obj, type=None):
        if obj is None: return self
        if self.__name__ in obj.__dict__:
            return obj.__dict__[self.__name__]
        trg = File(obj.store, self.trg)
        srcs = [File(obj.store, src) for src in self.srcs]
        # Remove target if out of date
        if any([trg.last_modified() < src.last_modified() for src in srcs]):
            trg.delete()
        # Make target if necessary
        if not obj.store.exists(trg.path):
            self.func(obj, trg, *srcs)
        obj.__dict__[self.__name__] = trg
        return trg