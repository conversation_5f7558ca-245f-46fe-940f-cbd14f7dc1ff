import re
from typing import Iterable
from datetime import datetime
import pandas as pd
import pytz

from base.logs import make_logger
log = make_logger(__name__)

#
#   Convenience formatting
#
def lform(l: Iterable, n=10, of_interest: str = None, sep: str = ", "):
    xs0 = l
    if of_interest is not None:
        xs0 = [x for x in l if of_interest in x]
    xs = []
    i = 0
    for i, x in enumerate(xs0):
        if i > n: break
        xs.append(str(x))
    ret = sep.join(xs)
    if i>n: ret += sep+"..."
    return f"({len(l)})[" + ret + "]"

def sform(s: str, n=75):
    if s is None: return "None"
    if len(s) > n: return s[:n-3]+"..."
    return s

def oform(o: any):
    if o is None: return "None"
    if isinstance(o, str): return f"'{o}'"
    if isinstance(o, datetime): return tform(o)
    if isinstance(o, Iterable): return lform(o)
    return f"{type(o)}({o})'"

def mform(mem: int):
    for u in ['B', 'K', 'M', 'G', 'T']:
        if mem < 10: return f"{mem:3.1f}{u}"
        if mem < 1000: return f"{mem:3.0f}{u}"
        mem /= 1024.0

def perc(i, n):
    if n<=0: return f"{i}/{n}"
    return f"{i}/{n} {100*i/n:.0f}%"

#
#   Time formatting
#
def tform(t):
    if t is None: return None
    if t==datetime.min.replace(tzinfo=pytz.UTC) or t==datetime.min: return "anytime"
    if t==datetime.max.replace(tzinfo=pytz.UTC) or t==datetime.max: return "never"
    t = t.astimezone()
    now = datetime.now()
    if t.year == now.year:
        if t.month == now.month:
            if t.day == now.day: return t.strftime("Today %H:%M")
        return t.strftime("%b%d %H:%M")
    return t.strftime("%Y %b %d")

#
#   Some grammar related functions
#
def word_s(s: str, n: int = 0):
    """Convert word to plural."""
    if n==1: return s
    if s.endswith("um"): return s[:-2] + "a"
    if s.endswith("y"): return s[:-1] + "ies"
    if s.endswith("s") or s.endswith("ch") or s.endswith("sh"): return s + "es"
    return s + "s"

def word_ed(s: str):
    if s == "feed": return "fed"
    if s.endswith("e"):
        return s + "d"
    return s + "ed"

def word_ing(s: str):
    if re.match(r".*.[^aeiou]e$", s): 
        return s[:-1] + "ing"
    if re.match(r".*[^aeiou][aeiou][^aeiouy]$", s): 
        return s + s[-1] + "ing"
    return s + "ing"

def word_list(xs: list[str], item: str = None):
    xs = [str(x) for x in xs if x]
    wlist = xs[0]
    if len(xs) > 1: wlist = ', '.join(xs[:-1]) + " and " + xs[-1]
    if item is None: return wlist
    return word_s(item, len(xs)) + ' ' + wlist


#
#  Some useful conversion functions
#
def camel2snake(s: str):
    return ''.join(['_' + c.lower() if c.isupper() else c for c in s]).lstrip('_')

#
#  Some useful decorators
#
class lazy(object):
    """A decorator for lazy properties."""
    def __init__(self, func, name=None, doc=None):
        self.__name__ = name or func.__name__
        self.__module__ = func.__module__
        self.__doc__ = doc or func.__doc__
        self.func = func

    def __get__(self, obj, type=None):
        if obj is None: return self
        if self.__name__ in obj.__dict__:
            x = obj.__dict__[self.__name__]
        else:
            x = self.func(obj)
            obj.__dict__[self.__name__] = x
        return x

class Singleton(object):
    def __new__(cls, *args, **kwds):
        it = cls.__dict__.get("__it__")
        if it is not None:
            return it
        cls.__it__ = it = object.__new__(cls)
        it.init(*args, **kwds)
        return it
    def init(self, *args, **kwds):
        pass


#
#  Some representation functions for debugging
#
def show_model(x, m = None, out=print):
    if m is None: m = x.__class__
    out(f"{m.__name__}: {m} {m.__table__}")
    for c in m.__table__.columns:
        out(f"  {c.name} = {getattr(x, c.name)}")
    
        
def show_class(c, out=print):
    out(f"{c.__name__}: {c} {c.__annotations__}")
    for k, v in c.__annotations__.items():
        dcf = ""
        if hasattr(c, "__dataclass_fields__"):
            f = c.__dataclass_fields__[k]
            dcf = f"-- Field({f.name}: {f.type})"
        ct = v.__name__ not in ["int", "str", "enum"]
        out(f"  {k} {v} {ct} {dcf}")

#
# Printable summary statistics for a dataframe
#
def col_stats(df: pd.DataFrame, col: str, name_width=24, num_width=5, width=100):
    v = df[col]
    n = len(v)
    nv = n - v.isna().sum()

    # Do the name, type and non-nulls
    if name_width == 0:
        # stats only
        tw = 0
        f1 = ""
        f2 = ""
    else:
        tw = name_width + num_width + 1
        f2 = f"{nv} {v.dtype.kind}"  # count and type
        nw = tw - len(f2)
        if name_width == 0:
            f1 = " " * nw
        elif len(col) <= nw:
            f1 = col + " " * (nw - len(col))  # name
        else:
            f1 = col[: nw - 2] + ".."

    # Do the statistics
    if v.dtype.kind == "b":
        f3 = f"True: {v.sum()}, False: {v.size - v.sum()}"
    elif v.dtype.kind in "iu":
        f3 = f"{v.min():8.0f} - {v.max():8.0f}"
    elif v.dtype.kind == "M":
        f3 = f"{v.min()} - {v.max()}"
    elif v.dtype.kind == "f":
        f3 = f"{v.mean():.3f} +/- {1.96*v.std():.3f} | {v.min():.3f} - {v.max():.3f}"
    elif v.dtype.kind in "SO":
        mw = width - tw - 3
        f3 = ""
        n = 0
        if v.isna().sum() > 0:
            f3 = f"None: {v.isna().sum()}"
            n += 1
        if (v == "nan").sum() > 0:
            if n > 0:
                f3 += ", "
            f3 += f"nan: {(v=='nan').sum()}"
            n += 1
        for k, x in v.value_counts().items():
            if k == "nan":
                continue
            if x > 1:
                dt = f"{k}: {x}"
            else:
                dt = f"{k}"
            if n > 0:
                dt = ", " + dt
            if n > 0 and len(f3) + len(dt) > mw:
                f3 += f" ...{v.nunique()}"
                break
            f3 += dt
            n += 1
        if n == 0:
            f3 += " unique"
    else:
        f3 = f"{v.dtype.type}"
    return f1 + f2 + " " + f3


class ColStats:
    def __init__(self, df: pd.DataFrame, col: str):
        self.total = len(df[col])
        self.not_null = self.total - df[col].isna().sum()
        self.distinct = df[col].nunique()
        self.stats = col_stats(df, col, name_width=0)


def df_stats(df: pd.DataFrame, out=print, indent="  "):
    """Return a summary of the dataframe with column names, types, and statistics."""
    if df is None:
        out("No data")
        return
    out(f"Dataframe: {df.shape}")
    for col in df.columns:
        out(indent + col_stats(df, col))

def set_comp(tag1: str, col1: Iterable, tag2: str, col2: Iterable, out=log):
    xs1 = set(col1)
    xs2 = set(col2)
    if xs1 != xs2:
        only1 = xs1 - xs2
        only2 = xs2 - xs1
        both = xs1 & xs2
        if out:
            out(f"  Mismatch: {tag1} ({len(only1)}({len(both)}){len(only2)}) {tag2}")
            if len(only1) > 0:
                out(f"    {tag1} only: {lform(only1, 6)}")
            if len(only2) > 0:
                out(f"    {tag2} only: {lform(only2, 6)}")
        else:
            print(f"  Mismatch: {tag1} ({len(only1)}({len(both)}){len(only2)}) {tag2}")
            if len(only1) > 0:
                print(f"    {tag1} only: {lform(only1, 6)}")
            if len(only2) > 0:
                print(f"    {tag2} only: {lform(only2, 6)}")

def isNamedTuple(t: type):
    """Check if a type or object is a NamedTuple."""
    if not isinstance(t, type): 
        t = type(t)
    return issubclass(t, tuple) and hasattr(t, "_fields")