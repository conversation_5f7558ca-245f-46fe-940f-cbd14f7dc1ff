#
#  Combinators for managing object representations.
#    Used for forms, table views, and data parsing
#

# Avoid erroneous import of the local `json` module when calling as main
# We don't need local imports.
import os, sys, ast
import typing

from numpy import float64, int64
import pandas as pd
from sqlalchemy import Row
import sqlalchemy
path = os.path.dirname(os.path.abspath(__file__))
if path in sys.path: sys.path.remove(path)

import types, inspect
from typing import List, Callable, NamedTuple
from datetime import datetime
from enum import Enum
from dataclasses import dataclass, is_dataclass
from fastapi import Form
from base.database import Base, UtcDateTime
from base.util import tform
from base.store import File
from base.logs import make_logger
from app.html import Html, ht

log = make_logger(__name__)

nils = [None, "", "NA", "ND"]

def obj_name(o):
    if o is None: return "None"
    if isinstance(o, str): return o
    if hasattr(o, "name"): return o.name
    if hasattr(o, "__name__"): return o.__name__
    return f"{o}"

class Rep:
    """Base class for the Object Rep Combinators"""
    @staticmethod
    def join(*reps): return JointRep.from_reps(*reps)
    
    def repr(self, value): return str(value)
    def __repr__(self): return self.name
    def to_string(self, value): return str(value)
    def html(self, value): 
        if value is None: return None
        return self.repr(value)
    def input(self, **attrs): raise NotImplementedError()
    def parse(self, value): return value
    
    def select_input(self, options, **attrs):
        if hasattr(self, "_options"):
            log(f"Use _options: {self._options}")
            options = self._options
        opts = []
        if hasattr(self, "_default") and self._default is None:
            opts.append(ht.option(value="", selected="selected")("Select..."))
        for o in options:
            n = ht.option(value=obj_name(o))(obj_name(o))
            log(f"  Option: {o} {obj_name(o)}")
            if hasattr(self, "_default") and self._default is not None:
                if obj_name(o) == obj_name(self._default): n = n(selected="selected")
            opts.append(n)
        return ht.select(**attrs)(*opts)


class ValRep(Rep):
    pass

class IntRep(ValRep):
    name: str = "int"
    typ: type = int

    def input(self, **attrs): return ht.input(type="text", **attrs)()

    def parse(self, value):
        if value in nils: return None 
        return int(value)


class StrRep(ValRep):
    name: str = "str"
    typ: type = str

    def input(self, **attrs):
        if "name" in attrs and "password" in attrs["name"]:
            attrs["autocomplete"] = "current-password"
            return ht.input(type="password", **attrs)()
        if "name" in attrs and attrs["name"] == "comment":
            return ht.textarea(**attrs)()
        return ht.input(type="text", **attrs)()

    def parse(self, value):
        if value in nils: return None
        return value
    
class FloatRep(ValRep):
    name: str = "float"
    typ: type = float

    def repr(self, value):
        return f"{value:.5g}" if value is not None else "None"
    
    def input(self, **attrs):
        return ht.input(type="text", **attrs)()

    def parse(self, v):
        if v in nils: return None
        return float(v)
    
class BoolRep(ValRep):
    name: str = "bool"
    typ: type = bool

    def input(self, **attrs):
        # need to add a hidden field to ensure the value is sent
        return (
            ht.input(type="hidden", id=attrs["id"] + ".def", name=attrs["name"], value="off")()
            + ht.input(type="checkbox", **attrs)()
        )

    def parse(self, v):
        if v in nils: return None
        if v in ["on", "true", "True", "T", "1"]: return True
        if v in ["off", "false", "False", "F", "0"]: return False
        raise ValueError(f"Invalid boolean value: {v}")
    
class DateTimeRep(ValRep):
    name: str = "datetime"
    typ: type = datetime
    def to_string(self, value): return str(value)
    def html(self, value):
        return tform(value)

    def input(self, **attrs):
        return ht.input(type="datetime-local", **attrs)()

class FileRep(ValRep):
    name: str = "file"
    typ: type = File
    def input(self, **attrs): return ht.input(type="file", **attrs)()
    
def enum_value(opts, aliases, v):
    """Check for a valid enum value with aliases"""
    def isval(o):
        """Is v a valid value for enum option o?"""
        on = obj_name(o)
        if on==v: return True
        if v in aliases: return on==aliases[v]
        return False
    # get all matching options
    xs = [o for o in opts if isval(o)]
    # Need it to be present and unique
    if len(xs)==0: raise ValueError(f"Invalid enum value: {v}")
    if len(xs)>1: raise ValueError(f"Ambiguous enum value: {v}")
    return xs[0]

class EnumRep(ValRep):
    name: str = "enum"
    def __init__(self, t):
        self.typ = t
        self.name = t.__name__
    def input(self, **attrs):
        return self.select_input([e.name for e in self.typ], **attrs)
    def parse(self, v):
        if v in nils: return None
        als = self._aliases if hasattr(self, "_aliases") else {}
        return enum_value([e.name for e in self.typ], als, v)

class ListRep(ValRep):
    name: str = "list"
    typ: type = list

def get_type_from_name(t: str) -> type:
    """Get a type from its name in the calling module."""
    log(f"Rep: {t} is a string, find the type")
    
    # First, find the module where this type is defined
    i = 1
    while i < len(inspect.stack()):
        caller_frame = inspect.stack()[i]
        caller_module = inspect.getmodule(caller_frame[0])
        if not caller_module.__name__.endswith(".reps"):
            break
        i += 1
    log(f"  Caller module: {caller_module.__name__}")
    
    # Now, we can find the type in that module
    for name, val in caller_module.__dict__.items():
        #log(f"  {name} = {val} ({type(val)})")
        if name == t:
            log(f"    type: {t} = {val}")
            t = val
    log(f"  {t} is now {type(t)}")
    return t


def rep(t: type) -> Rep:
    #log(f"REP: {t} ({type(t)})")
    try:
        if isinstance(t, str):
            # If it's a string, we find the corresponding type
            t = get_type_from_name(t)
        if isinstance(t, typing._GenericAlias):
            log(f"  Generic {t}")
            t = typing.get_args(t)[0]
            log(f"    -> {t}")
        match t:
            case t if isinstance(t, dict): return Rep.join(t)
            case t if t is int: return IntRep()
            case t if t is int64: return IntRep()
            case t if t is str: return StrRep()
            case t if t is float: return FloatRep()
            case t if t is float64: return FloatRep()
            case t if t is bool: return BoolRep()
            case t if t is datetime: return DateTimeRep()
            case t if t is list: return ListRep()
            case t if t is File: return FileRep()
            case t if issubclass(t, Enum): return EnumRep(t)
            case t if t is dict: return ObjRep.from_dict("dict", {})
            case t if issubclass(t, Base): return ObjRep.from_model(t)
            case t: return ObjRep.from_class(t)
    except Exception as e:
        log(f"Error in rep({t}:{type(t)}): {e}")
        raise e
    
def col_type(c):
    fks = c.foreign_keys
    rcl = None
    for fk in fks:
        tn = fk.column.table.name
        for mapper in Base.registry.mappers:
            cl = mapper.class_
            cn = cl.__name__
            if not cn.startswith("_"):
                tblname = cl.__tablename__
                if tblname == tn:
                    rcl = cl
    if rcl is None:
        if isinstance(c.type, UtcDateTime): 
            rcl = datetime
        else: 
            rcl = c.type.python_type
    return rcl


class FieldRep(Rep):
    """
    A field representation. Used for computed fields in object representations.
    Associetes a field with a function to compute its value and a rep to represent that value
    """

    def __init__(self, rep: Rep, get_func: callable):
        self.name = f"->{rep.name}"
        self.typ = rep.typ
        self.rep = rep
        self.get_func = get_func

    def to_string(self, value):
        return self.rep.to_string(value)

    def html(self, value):
        if value is None:
            return None
        # log(f"FieldRep html: {type(value)} {value}")
        return self.rep.html(value)
    
class ObjRep(Rep):
    def __init__(self, name, typ: type, fields: dict):
        self.name = name
        for r in fields.values():
            if not isinstance(r, Rep):
                raise ValueError(f"Invalid field representation: {r}")
        self.fields: dict = fields
        self.typ = typ

    @staticmethod
    def from_dataframe(df: pd.DataFrame):
        """Create an object representation from a pandas DataFrame."""
        fields = {col: rep(df[col].dtype.type) for col in df.columns}
        return ObjRep("DataFrame", dict, fields)
    
    @staticmethod
    def from_obj(obj):
        """Create an object representation from an object."""
        fs = {}
        if type(obj) is Row: 
            for o in obj: fs |= ObjRep.from_obj(o).fields
            return ObjRep("Row", dict, fs)
        if is_dataclass(obj) or isinstance(obj, Base): 
            return ObjRep.from_class(obj.__class__)
        if isinstance(obj, dict):
            fs = {k: rep(type(v)) for k, v in obj.items()}
            return ObjRep("Dict", dict, fs)
        if hasattr(obj, "__dict__"): 
            fs = {k: rep(type(v)) for k, v in obj.__dict__.items() if not k.startswith("_")}
            return ObjRep(obj.__class__.__name__, obj.__class__, fs)
        raise ValueError(f"Cannot create ObjRep from object: {obj} ({type(obj)})")

    @staticmethod
    def from_model(m):
        r = ObjRep(m.__name__, m, {c.name: rep(col_type(c)) for c in m.__table__.columns})
        r.typ = m
        return r

    @staticmethod
    def from_class(cl):
        def dct(cl):
            params = {}
            if not cl or cl is object:
                return {}
            if hasattr(cl, "__annotations__") and cl.__annotations__:
                #log(f"Class {cl.__name__} has annotations: {cl.__annotations__}")
                # for k, v in cl.__annotations__.items():
                #     log(f"  {k}: {v} {type(v)}")
                params = {k: rep(v) for k, v in cl.__annotations__.items()}
            else:
                # Try to get fields from __init__ parameters if __annotations__ is missing
                params = {}
                if hasattr(cl, "__init__"):
                    sig = inspect.signature(cl.__init__)
                    # skip 'self'
                    for name, param in list(sig.parameters.items())[1:]:
                        if name in ["args", "kwargs"]:   # skip variable args
                            continue
                        # log(f"Init param: {name} {param}    {param.annotation}: {type(param.annotation)}")
                        if param.annotation != inspect.Parameter.empty:
                            try:
                                params[name] = rep(param.annotation)
                            except Exception as e:
                                log(f"Error in rep({param.annotation}) for {name}: {e}")
                                params[name] = StrRep()
                        else:
                            params[name] = StrRep()
            return dct(cl.__base__) | params

        orep = ObjRep(cl.__name__, cl, dct(cl))
        orep.typ = cl
        # Set any default values
        def sdf(c):
            if not c or not hasattr(c, "__annotations__"): return
            for k, r in orep.fields.items():
                if hasattr(c, k):
                    v = c.__dict__[k]
                    if isinstance(v, r.typ):
                        r._default = v
            sdf(c.__base__)
        sdf(cl)
        return orep

    @staticmethod
    def from_dict(name: str, **annots: type):
        typ = type(name, (), annots)
        typ.__module__ = Base.__module__
        r = ObjRep(name, typ, {k: rep(v) for k, v in annots.items()})
        return r
        
    @staticmethod
    def pass_through(name: str, values: dict, hide: bool = False):
        annots = {k: type(v) for k, v in values.items()}
        r = ObjRep.from_dict(name, **annots)
        for f in r.fields:
            if hide:
                r.fields[f]._hidden = values[f]
            else:
                r.fields[f]._default = values[f]
        return r

    @staticmethod
    def joint(*reps):
        return ObjRep(
            "-".join([r.name for r in reps]),
            tuple,
            {r.name + "." + k: v for r in reps for k, v in r.fields.items()},
        )

    def __add__(self, other):
        """Combine two object representations into a joint representation."""
        return JointRep.from_reps(self, other)

    def __or__(self, other: Rep):
        """Add additional fields."""
        self.fields.update(other.fields)
        return self

    def __sub__(self, other: str):
        """Remove fields."""
        self.fields.pop(other)
        return self

    def __lt__(self, value: any):
        """Set the defaults from an instance or parse it from a dictionary."""
        if isinstance(value, dict):
            for k, v in value.items():
                if k in self.fields:
                    r = self.fields[k]
                    r._default = r.parse(v)
            return self
        for n, f in self.fields.items():
            f._default = self.get(n, value)
        return self

    def __call__(self, kmap: dict):
        """Return a new object representation with only the specified fields."""
        return MappedRep(self, kmap)
    
    def options(self, field, xs: List):
        """Provide a custom list of options for a field."""
        self.fields[field]._options = xs
        return self

    def hidden(self, field, value):
        """Hide a field in forms."""
        self.fields[field]._hidden = value
        return self

    def omit(self, field):
        """Omit a field from forms."""
        self.fields[field]._omit = True
        return self

    def default(self, field, value):
        """Set a default value for a field."""
        self.fields[field]._default = value
        return self

    def render(self, field, func):
        """Custom render a field."""
        self.fields[field]._render = func
        return self

    def html(self, value):
        if value is None: return None
        elif isinstance(value, str): return value
        elif isinstance(value, int): return str(value)
        elif isinstance(value, float): return f"{value:.5g}"
        elif isinstance(value, Base):
            if hasattr(self.typ, "name"): return value.name
            elif hasattr(self.typ, "code"): return value.code
            else: return value.id
        else: 
            log(f"ObjRep html: {self} ({value}: {type(value)})")
            raise ValueError(f"Invalid model value: {value}")

    def __repr__(self):
        def rstr(r):
            s = ""
            if hasattr(r, "_options"): s += "o"
            if hasattr(r, "_hidden"): s += "h"
            if hasattr(r, "_omit"): s += "x"
            if hasattr(r, "_default"): s += "d"
            if hasattr(r, "_render"): s += "r"
            if s == "": return r.name
            elif hasattr(r, "_default"): return f"{r.name}[{s}]={obj_name(r._default)}"
            elif hasattr(r, "_hidden"): return f"{r.name}[{s}]={r._hidden}"
            else: return f"{r.name}[{s}]"
        return (
            self.name + "(" + ", ".join([f"{k}: {rstr(r)}" for k, r in self.fields.items()]) + ")"
        )

    def __getitem__(self, field: str):
        return self.fields[field]

    def get(self, field: str, value):
        if value is None: return None
        if isinstance(value, dict): return value[field]
        f = self.fields.get(field)
        if f and isinstance(f, FieldRep): return f.get_func(value)
        if not hasattr(value, field): return "n/a"
        return getattr(value, field)

    def repr(self, value):
        # we allow database models to be represented as their ids
        if issubclass(self.typ, Base):
            if isinstance(value, int):
                return self.typ.__name__ + "[" + str(value) + "]"
            if isinstance(value, str):
                return self.typ.__name__ + "[" + value + "]"
        return self.name+"("+",".join([f"{k}: {r.repr(self.get(k, value))}" for k, r in self.fields.items()])+")"

    def htf(self, field: str, value: any):
        """Return an HTML representation of a field value."""
        v = self.get(field, value)
        if isinstance(v, Html):
            return v
        r = self.fields[field]
        if hasattr(r, "_render"):
            return r._render(v)
        return r.html(v)

    def input(self, **attrs):
        """Return an HTML input field for the object representation. Just a text input for now."""
        return ht.input(type="text", **attrs)()

    def parse(self, v):
        if v in nils: return None
        if not hasattr(self.typ, "__enum__"):
            #  pass through id(s) as string:
            if isinstance(v, str): return v
            if isinstance(v, int): return str(v)
            raise ValueError(f"Cannot parse objects: {self.typ} {v}")
        als = self._aliases if hasattr(self, "_aliases") else {}
        log(f"Parse: {self.typ} {v} {hasattr(self, '_options')}")
        return enum_value([o for o in self._options], als, v)

    def form_fields(self, ogen: Callable[[Base], list] = None, default=None):
        """Return the form fields for creating an HTML form."""
        fs = []
        for n, r in self.fields.items():
            if n == "id": continue
            fn = f"{self.name.lower()}.{n}"
            f = None
            if hasattr(r, "_omit"): continue
            if hasattr(r, "_hidden"):
                v = r._hidden
                if isinstance(v, types.FunctionType): v = v()
                f = ht.input(type="hidden", name=n, value=v)()
            elif issubclass(r.typ, Base) and hasattr(r.typ, "__enum__"):
                f = r.select_input(ogen(r.typ), id=fn, name=n, autocomplete=fn)
            else:
                log(f"Form field: {n} {r.typ} {r} {hasattr(r, 'options')}")
                if hasattr(r, "_options"):
                    log(f"Use _options: {r._options}")
                    f = r.select_input(r._options, id=fn, name=n, autocomplete=fn)
                else:
                    f = r.input(id=fn, name=n, autocomplete=fn)
            if hasattr(r, "_default"):
                f = f(value=r._default)
            if default is not None:
                f = f(value=self.get(n, default))
            fs.append((n, f))
        return fs

    def form_parser(self):
        """Return a dataclass suitable for parsing data from an HTML post request."""
        use = {k: r for k, r in self.fields.items() if not hasattr(r, "_omit")}
        cl = type(self.name + "Form", (), {k: Form(...) for k, r in use.items()})
        cl.__module__ = Base.__module__
        annots = {k: r.typ for k, r in use.items() if k != "id"}
        for k, v in annots.items():
            if issubclass(v, Base):
                annots[k] = v.__table__.columns[0].type.python_type
        cl.__annotations__ = annots
        return dataclass(cl)

    def from_parser(self, fp):
        """Return an object representation from a form parser."""
        x = self.typ()
        for k, r in self.fields.items():
            setattr(x, k, getattr(fp, k))
        return x

    def dataclass(self):
        """Return a regular dataclass"""
        cl = type(self.name, (), {})
        cl.__module__ = Base.__module__
        cl.__annotations__ = {k: r.typ for k, r in self.fields.items()}
        return dataclass(cl)

    def instance(self, **kwargs):
        """Return an instance of the object representation."""
        return self.typ(**{k: r.parse(kwargs[k]) for k, r in self.fields.items()})


class JointRep(ObjRep):
    """A joint representation of multiple object representations. Combines all fields."""
    def __init__(self, reps: dict):
        names = []
        fields = dict()
        for rn, r in reps.items():
            #log(f"JointRep: {rn} {r}: {type(r)}")
            if isinstance(r, ObjRep):
                names.append(rn)
                fields.update({rn+"."+k: v for k, v in r.fields.items()})
            else:
                names.append(f"{rn}: {r.name}")
                fields.update({rn: r})
        #for k, r in reps.items(): log(f"  Rep: {k}: {r} {type(r)}")
        super().__init__("-".join(names), tuple, fields)
        self.reps = reps
        self.name = "-".join([r.name for r in reps.values()])

    @staticmethod
    def from_reps(*reps):
        rs = dict()
        for r in reps:
            if isinstance(r, tuple): rs[r[0]] = ObjRep.from_model(r[1])
            elif isinstance(r, Rep): rs[r.name] = r
            elif isinstance(r, dict): rs.update({k: rep(t) for k, t in r.items()})
            elif issubclass(r, Base): rs[r.__name__] = ObjRep.from_model(r)
            else:
                log(f"Invalid representation type: {r}: {type(r)} Base? {isinstance(r, Base)}")
                raise ValueError(f"Invalid representation type: {r}")
        return JointRep(rs)

    @staticmethod
    def from_class(cl):
        def dct(cl):
            if not cl or not hasattr(cl, "__annotations__"): return {}
            return dct(cl.__base__) | {k: rep(v) for k, v in cl.__annotations__.items()}
        return JointRep(dct(cl))
    
    def get(self, item: str, value):
        ns = item.split(".")
        i = list(self.reps.keys()).index(ns[0])
        if i == -1: raise ValueError(f"Invalid field: {item} not in {self}")
        if len(ns) == 1: return value[i]
        elif len(ns) == 2: return self.reps[ns[0]].get(ns[1], value[i])
        else: raise ValueError(f"Invalid field {item}: too many parts")

    def repr(self, value):
        rs = [k + "=" + r.repr(self.get(k, value)) for k, r in self.fields.items()]
        return self.name + "(" + ",".join(rs) + ")"

class MappedRep(ObjRep):
    """
        A mapped representation. Selects and renames fields from another rep.
        This is convenient for creating custom forms or views with a subset of fields.
        Example: (see __main__ for full example)
            rep0 = rep(A) + rep(B)  # joint representation for a tuple (A, B)
            rep = rep0({"id": "A.id", "name": "A.name", "bname": "B.name"}) 
            # This will create a reduced representation of (A, B)
    """

    def __init__(self, org: ObjRep, kmap: dict):
        self.org = org
        # Allow a wild card to include verbatim all fields not otherwise specified
        if "*" in kmap:
            kmap |= {f: f for f in org.fields.keys() if f not in kmap.values()}
            del kmap["*"]
        self.kmap = kmap
        # We are parsing the source names as dot paths so we can flatten object hierarchies.
        def field(rep: Rep, keys: List[str]):
            if len(keys) == 0:
                return rep
            i = len(keys)
            while i>0 and ".".join(keys[:i]) not in rep.fields:
                i -= 1
            if i == 0:
                raise ValueError(f"Invalid field path: {'.'.join(keys)} not in {rep.fields}")
            return field(rep.fields[".".join(keys[:i])], keys[i:])
        try:
            fields = {}
            for  key, rep in kmap.items():
                if isinstance(rep, str):
                    # parse the dot-path
                    rep = field(self.org, rep.split("."))
                elif isinstance(rep, Callable):
                    # Also allow functions for computed properties
                    rep = FieldRep(StrRep(), rep)
                fields[key] = rep
        except KeyError as e:
            log(f"Items:  {kmap}")
            log(f"Fields: {org.fields}")
            raise e
        super().__init__(org.name+"*", org.typ, fields)

    def get(self, item: str, value):
        # Get computed field value if applicable
        if isinstance(self.fields[item], FieldRep):
            return self.fields[item].get_func(value)
        # If not a computed field, parse the dot-path for data access
        keys = self.kmap[item].split(".")
        val = value
        rep = self.org
        i = 0
        while i < len(keys):
            j = len(keys)
            # longest existing leading path
            while j>i:
                k = ".".join(keys[i:j])
                if k in rep.fields:
                    break
                j -= 1
            if i == j:
                raise ValueError(f"Invalid field path: {'.'.join(keys)} not in {rep.fields}")
            # Look up the new value and rep, then apply the rest of the path
            val = rep.get(k, val)
            rep = rep.fields[k]
            i = j
        return val

#
# Usage examples
#
if __name__ == "__main__":
    class A(NamedTuple):
        id: int
        name: str
        value: float
        active: bool
        created: datetime
        status: Enum

    log(rep(A))

    class B(NamedTuple):
        id: int
        name: str
        value: float
        active: bool
        created: datetime
        status: Enum
        other: A

    # Simple rep for B
    log(rep(B))
    # Mapped rep for B, renaming fields
    repBs = rep(B)({"id": "id", "name": "name", "value": "value", "avalue": "other.value"})

    # Joint rep for A and B, represents a tuple (A, B)
    repAB = rep(A) + rep(B)
    log(repAB)
    
    # Mapped rep for A and B, renaming fields
    repABs = repAB({"id": "A.id", "name": "A.name", "bname": "B.name"})
    log(repABs)

    a = A(id=1, name="A", value=1.0, active=True, created=datetime.now(), status=1)
    b = B(id=2, name="B", value=2.0, active=False, created=datetime.now(), status=2, other=a)
    log(rep(A).repr(a))
    log(rep(B).repr(b))
    log(repBs.repr(b))
    log(repAB.repr((a, b)))
    log(repABs.repr((a, b)))
    
    # Test fields for standard classes
    class C:
        def __init__(self, id: int, name: str, value: float):
            self.id = id
            self.name = name
            self.value = value
    log(rep(C))
    
    # Test field reps
    frep = rep(B)({
        "id": lambda x: x.id,
        "name": lambda x: x.name,
        "lower": lambda x: x.name.lower(),
        "aname": lambda x: x.other.name,
    })
    log(frep.repr(b))
