from abc import abstractmethod
import requests
import zlib
from to_file_like_obj import to_file_like_obj

from base.logs import make_logger
log = make_logger(__name__)

#
#  Class for streaming a gzip file from chnunks
#
class GzipStream():
    def __init__(self, size):
        """size is an estimate purely for progress tracking"""
        self.size = size
        self.pos = 0
    
    @abstractmethod
    def compressed(self):
        """Stream the file in chunks of compressed data"""
        raise NotImplementedError("chunks() not implemented")
    
    def chunks(self):
        """Stream the file in chunks of decompressed data"""
        # Thank you https://stackoverflow.com/questions/68899351/opening-a-gzip-file-from-a-url-in-python3-and-using-islice
        #Using zlib.MAX_WBITS|32 apparently forces zlib to detect the appropriate header for the data
        decompressor = zlib.decompressobj(zlib.MAX_WBITS|32)
        #Stream this file - pull the content in just a little at a time
        size = 0
        for chunk in self.compressed():
            #Decompress the current chunk
            log(f"  Decompress {len(chunk)} bytes")
            decompressed_chunk=decompressor.decompress(chunk)
            log(f"    -> {len(decompressed_chunk)} bytes")
            if len(decompressed_chunk) == 0:
                raise ValueError("Decompressed chunk is empty, possibly due to a corrupted gzip file")
            size += len(decompressed_chunk)
            yield decompressed_chunk
            self.pos += len(chunk)
        log(f"  {self.pos} -> {size} bytes ({100*self.pos/size:.2f}%)")
    
    def lines(self):
        """Stream the file in lines of text"""
        # Thank you https://stackoverflow.com/questions/21797753/efficiently-reading-lines-from-compressed-chunked-http-stream-as-they-arrive
        pending = None
        for chunk in self.chunks():

            if pending is not None:
                chunk = pending + chunk
            lines = chunk.splitlines()

            if lines and lines[-1] and chunk and lines[-1][-1] == chunk[-1]:
                pending = lines.pop()
            else:
                pending = None

            for line in lines:
                yield line.decode(self.encoding)

        if pending is not None:
            yield pending.decode(self.encoding)

    def file(self):
        return to_file_like_obj(self.chunks())
    
    def progress(self):
        """Report progress in streaming the file"""
        if self.size == 0: 
            return f"{self.pos:8d}"
        return f"{100*self.pos//self.size:2d}%"
    
class GzipUrlStream(GzipStream):
    """Stream a gzip file from a URL"""
    def __init__(self, url, size: int = 0):
        """size is an estimate purely for progress tracking"""
        self.url = url
        super().__init__(size)

    def compressed(self):
        """Stream the file in chunks of compressed data"""
        #Stream this file in as a request - pull the content in just a little at a time
        response = requests.get(self.url, stream=True)
        # See if we can get a size estimate
        if 'Content-Length' in response.headers:
            size = int(response.headers.get('Content-Length'))
            if self.size == 0:
                self.size = size
            if size != self.size:
                log.warning(f"Warning: content mismatch: {size} != {self.size}, using {self.size}")
                log.warning(f"  {self.url}")
        with response as remote_file:
            self.encoding = remote_file.encoding or 'utf-8'
            log(f"Encoding: {remote_file.encoding} -> {self.encoding}")
            #Chunk size can be adjusted to test performance
            for chunk in remote_file.iter_content(chunk_size=8192):
                yield chunk     
    
class GzipFileStream(GzipStream):
    """Stream a gzip file from a file"""
    def __init__(self, fd, size: int = 0):
        """size is an estimate purely for progress tracking"""
        self.fd = fd
        super().__init__(size)

    def compressed(self):
        """Stream the file in chunks of compressed data"""
        #Stream this file - pull the content in just a little at a time
        while chunk := self.fd.read(8192):
            yield chunk
