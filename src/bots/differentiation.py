import asyncio
import time
from sqlalchemy.orm import Session
from bots.bot import <PERSON><PERSON>
from workflow.culture import DifferentiationStep
from workflow.orm.steps import DifferentiationIn
from workflow.flow import ActionState, ResultSubmit
from workflow.orm.rar import ResultReason
from images.differentiation_utils import run_differentiation, get_pending_imagesets
from base.logs import make_logger
from base.database import get_db


log = make_logger(__name__)

class DifferentiationBot(Bot):
    def __init__(self, db: Session):
        super().__init__(db, DifferentiationStep())
        
    def process(self, state: ActionState) -> ResultSubmit:
        if len(state.requests.items) == 0:
            reason = self.db.get(ResultReason, "fail")
            return ResultSubmit(state.action.id, "No requests found in action", reason)

        req, inp, *_ = state.requests.items[0]

        # Get all eligible ImageSets that need processing
        eligible_imagesets = get_pending_imagesets(self.db, limit=inp.max_items or 20)

        if not eligible_imagesets:
            reason = self.db.get(ResultReason, "pass")
            return ResultSubmit(state.action.id, "No eligible ImageSets found for processing", reason)

        processed_count = 0
        start_time = time.time()
        max_time_seconds = inp.max_time or 28800  # 8 hours default

        log(f"Starting differentiation processing for {len(eligible_imagesets)} ImageSets")

        for image_set in eligible_imagesets:
            # Check timeout
            if time.time() - start_time > max_time_seconds:
                log(f"Timeout reached after {processed_count} ImageSets")
                break

            try:
                log(f"Processing ImageSet {image_set.id}: {image_set.plate.name} day {image_set.day}")
                result = run_differentiation(self.db, image_set, yield_progress=False)

                if result is not None:
                    processed_count += 1
                    log(f"Successfully processed ImageSet {image_set.id}")
                else:
                    log(f"No results generated for ImageSet {image_set.id} - likely missing repo data")

            except Exception as e:
                log(f"Error processing ImageSet {image_set.id}: {e}")
                # Continue with next ImageSet instead of stopping
                continue

        reason = self.db.get(ResultReason, "pass")
        return ResultSubmit(state.action.id, f"Processed {processed_count}/{len(eligible_imagesets)} ImageSets", reason)

if __name__ == '__main__':
    bot = DifferentiationBot(get_db())
    asyncio.run(bot.run(once=False))
