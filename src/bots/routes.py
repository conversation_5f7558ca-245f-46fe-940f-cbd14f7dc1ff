from typing import List
import sys, asyncio
from datetime import datetime
import pytz
from time import sleep
from fastapi import Depends, APIRouter
from fastapi.responses import HTMLResponse

from base.database import get_db, transact
from app.auth import context
from app.layouts import Context
from base.reps import rep
from app.table import table
from base.logs import make_logger

from bots.upload import UploadBot
from bots.pipeline import PipelineBot
from bots.model import Worker
from bots.bot import logs
from bots.differentiation import DifferentiationBot

log = make_logger(__name__)
router = APIRouter(prefix="/bots")

@router.get("/", response_class=HTMLResponse)
async def bots(ht: Context = Depends(context)):
    workers = ht.db.query(Worker).filter(Worker.finished==None).all()    
    log(f"BOTS: Busy workers: {len(workers)}")
    
    ret = ht.page("Bots Home",
        ht.h2("Manage Bots"),
        ht.when(len(workers) > 0)(
            ht.h3("These are the bots currently running:"),
            table(workers, rep(Worker))(id="workers", href=ht.url("bots/worker")),
        ),
        ht.when(len(workers) == 0)(
            ht.h3("No bots are currently running."),
            ht.ul(
                ht.li(ht.link("bots/start")("Start a new Upload bot")),
                #ht.li(ht.link("bots/pipeline")("Start a new Pipeline bot")),
            ),
            "(Reload this page to continue)"
        ),
    )
    return ret.pretty()

async def work():
    db = get_db()
    name = "upload"
    workers = db.query(Worker).\
        filter(Worker.finished==None).\
        filter(Worker.name=="upload").\
        all()
    log(f"START: Busy workers for {name}: {len(workers)}")
    if len(workers) == 0:
        log("  Starting a new worker at {datetime.now()}")
        t0 = datetime.now(pytz.utc)
        w = Worker(name="upload", started=t0, last=t0)
        with transact(db):
            db.add(w)
        fake = False
        if fake:
            fn = f"fake-bot.log"
            log(f"Writing log to {logs}{fn}")
            stdout = sys.stdout
            with logs.open(fn, "w") as sys.stdout:
                log("  Starting work")
                for i in range(5):
                    sleep(10)
                    with transact(db):
                        w.last = datetime.now(pytz.utc)
                        db.flush([w])
                    log(f"  Work iteration {i}")
                sys.stdout = stdout
        else:
            bot = UploadBot(db)
            bot.backfill_requests(db)
            def notify():
                with transact(db):
                    w.last = datetime.now(pytz.utc)
                    db.flush([w])
            await bot.run(wait=False, on_action=notify)
        with transact(db):
            w.finished = datetime.now(pytz.utc)
            db.commit()
        log("  Work completed at {datetime.now()}")

async def pipeline_work():
    db = get_db()
    name = "pipeline"
    workers = db.query(Worker).\
        filter(Worker.finished==None).\
        filter(Worker.name==name).\
        all()
    log(f"START: Busy workers for {name}: {len(workers)}")
    if len(workers) == 0:
        log(f"  Starting a new worker at {datetime.now()}")
        t0 = datetime.now(pytz.utc)
        w = Worker(name=name, started=t0, last=t0)
        with transact(db):
            db.add(w)
        bot = PipelineBot(db)
        await bot.run(wait=False)
        with transact(db):
            w.finished = datetime.now(pytz.utc)
            db.commit()
        log(" Work completed at {datetime.now()}")

@router.get("/pipeline/", response_class=HTMLResponse)
async def start_pipeline(ht: Context = Depends(context)):
    log("Starting pipeline bot")
    await pipeline_work()
    log("Pipeline bot finished")
    return ht.page("Pipeline Bot finished", ht.h2("Bot finished.")).pretty()
    
@router.get("/start/", response_class=HTMLResponse)
async def start(ht: Context = Depends(context)):
    log("Starting bot")
    #asyncio.create_task(work())
    await work()
    log("Bot finished")
    return ht.page("Bot finished", ht.h2("Bot finished.")).pretty()

@router.get("/worker/{id}/", response_class=HTMLResponse)
async def worker(id: str, ht: Context = Depends(context)):
    log(f"Remove worker: {id}")
    w = ht.db.query(Worker).get(id)
    with transact(ht.db):
        w.finished = datetime.now(pytz.utc)
        ht.db.commit()
    return ht.page(f"Worker {id}", ht.h2(f"Worker {w.name} removed.")).pretty()

async def differentiation_work():
    db = get_db()
    name = "differentiation"
    workers = db.query(Worker).\
        filter(Worker.finished==None).\
        filter(Worker.name==name).\
        all()
    log(f"START: Busy workers for {name}: {len(workers)}")
    if len(workers) == 0:
        log(f"Starting a new worker at {datetime.now()}")
        t0 = datetime.now(pytz.utc)
        w = Worker(name=name, started=t0, last=t0)
        with transact(db):
            db.add(w)
        bot = DifferentiationBot(db)
        bot.backfill_requests(db)
        await bot.run(wait=False)
        with transact(db):
            w.finished = datetime.now(pytz.utc)
            db.commit()
        log("Work completed at {datetime.now()}")

@router.get("/differentiation/", response_class=HTMLResponse)
async def start_differentiation(ht: Context = Depends(context)):
    log("Starting differentiation bot")
    await differentiation_work()
    log("Differentiation bot finished")
    return ht.page("Differentiation Bot finished", ht.h2("Bot finished.")).pretty()
   
    

