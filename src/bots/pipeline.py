import asyncio
from bots.bot import Bo<PERSON>
from workflow.pipeline import <PERSON>pelineStep
from workflow.workflow import Flow
from workflow.flow import ActionState
from base.database import get_db, transact, flush
from sqlalchemy.orm import Session
from workflow.pipeline import NewResultSetResult
from images.model import ResultSet, ImageSet, PipelineVersion, ImagePipeline, PipelineRequest
from google.cloud import dataflow_v1beta3
from datetime import datetime
from workflow.orm.rar import Request, Action, Result, ResultReason
from workflow.orm.steps import PipelineIn
from sqlalchemy import text
from base.logs import make_logger
log = make_logger(__name__)

class PipelineBot(Bot):
    def __init__(self, db: Session):
        super().__init__(db, Flow(PipelineStep))

    def process(self, state: ActionState) -> NewResultSetResult:
        req, inp, *_ = state.requests.items[0]
        log(f"INPUT: {inp}")
        
        image_set = self.db.query(ImageSet).filter_by(id=inp.image_set).first()
        log(f"IMAGE SET: {image_set}")
        pipeline_version = self.db.query(PipelineVersion).filter_by(id=inp.pipeline_version).first()
        pipeline = self.db.query(ImagePipeline).filter_by(id=pipeline_version.image_pipeline).first()
        
        ims_query = self.db.execute(
            text(
                (
                    "SELECT distinct(image_channel.channel_name) as channel "
                    + " FROM image_channel JOIN image ON image_channel.image_id"
                    + " = image.id WHERE image.image_set = :ims"
                )
            ),
            {"ims":image_set.id}
        )
        image_set_channels = set()
        for row in ims_query:
            image_set_channels.add(row[0])
        
        pipeline_query = self.db.execute(
            text(
                "SELECT image_channel as channel FROM pipeline_channel WHERE image_pipeline = :pipeline_id"
            ),
            {"pipeline_id":pipeline.id}
        )
        pipeline_channels = set()
        for row in pipeline_query:
            pipeline_channels.add(row[0])
        
        
        log(f"IMAGE SET CHANNELS: {image_set_channels}")
        log(f"PIPELINE CHANNELS: {pipeline_channels}")
        if pipeline_channels != image_set_channels and image_set.device == 'olympus' and pipeline.device == 'olympus':
            raise ValueError(f"image set {image_set.id} channels ({image_set_channels}) do not match pipeline requirements ({pipeline_channels})")
        if image_set.device != pipeline.device:
            raise ValueError(f"image set device ({image_set.device}) does not match pipeline requirements ({pipeline.device})")
        
        log(f"PIPELINE VERSION: {pipeline_version}")
        pipeline_request = self.submit_dataflow_job(pipeline, pipeline_version, image_set)
        
        log(f"PIPELINE REQUEST: {pipeline_request}")
        existing = ResultSet(pipeline_request=pipeline_request.id, image_pipeline_version=pipeline_version.id)
        
        log(f"RESULT SET: {existing}")
        with transact(self.db):
            self.db.add(existing)
            self.db.commit()
        log("INSERTED RESULT")    
        reason = self.db.get(ResultReason, "pass")
        msg = "msg"
        
        return NewResultSetResult(state.action.id, msg, reason, existing)
        
        

    def submit_dataflow_job(self, pipeline, pipeline_version, image_set):

    
        dataflow_client = dataflow_v1beta3.FlexTemplatesServiceClient()
        launch_request = dataflow_v1beta3.LaunchFlexTemplateRequest()
    
        # set up environment
        dataflow_env = dataflow_v1beta3.FlexTemplateRuntimeEnvironment()
        dataflow_env.max_workers = 20
        # dataflow_env.num_workers = 10
        dataflow_env.zone = 'us-east1-b'
        dataflow_env.temp_location = "gs://mellicell_development_bucket/dataflow/temp"
        dataflow_env.service_account_email = "<EMAIL>"
        dataflow_env.machine_type = "n2-custom-2-8192"
        dataflow_env.disk_size_gb = 40
    
        # set up parameters 
        launch_params = dataflow_v1beta3.LaunchFlexTemplateParameter()
        time_now = datetime.now()
        launch_params.job_name = f"{pipeline.name}-{time_now.year}{time_now.month}{time_now.day}{time_now.hour}{time_now.minute}".lower()
        # TODO make this the request for RAR system
        # anticipate pipeline to use ID for results insert
        pipeline_request = PipelineRequest(
            pipeline_version=pipeline_version.id,
            image_set=image_set.id,
            status="started"
        )
        self.db.add(pipeline_request)
        self.db.commit()
        self.db.refresh(pipeline_request)
    
    
        launch_params.parameters = {
            "image_set": str(image_set.bt_key),
            "secret_version": "projects/*************/secrets/mellitos-dev-app-login/versions/4",
            "image_request_id": str(pipeline_request.id),
            "sdk_container_image": str(pipeline_version.container_image),
            "pipeline_version_id": str(pipeline_version.id)
        }
        launch_params.environment = dataflow_env
        launch_params.container_spec_gcs_path =  pipeline_version.template_path #"gs://mellicell_development_bucket/dataflow/templates/olympus-convert-20240612-111330.json"
    
    
        # set up request
        launch_request.project_id = 'development-311316'
        launch_request.location = 'us-east1'
        launch_request.validate_only = False
        launch_request.launch_parameter = launch_params
   
    
        dataflow_response = dataflow_client.launch_flex_template(request=launch_request)
        #dataflow_response = {}
        log("DATAFLOW RESPONSE:")
        log(dataflow_response)
        return pipeline_request
        
if __name__ == '__main__':
    bot = PipelineBot(get_db())
    asyncio.run(bot.run(once=False))

