from typing import Callable, Named<PERSON><PERSON><PERSON>, <PERSON><PERSON>
from fastapi import APIRouter, Depends, Request as HttpRequest
from fastapi.responses import HTMLResponse
from sqlalchemy import func
from sqlalchemy.orm import Session
from app.auth import context
from app.html import Html
from app.layouts import Context
from app.table import table
from base.database import Base, transact
from base.reps import Rep, rep
from plates.model import Dose, MapGroup, PlateFormat, PlateMap, WellPos
from base.util import word_s
from base.logs import make_logger
from base.database import name_val
from plates.render import plate_map, plate_view
from workflow.flow import FlowStep

log = make_logger(__name__)

#
#  Router for creating and mapping plate maps and array items on them
#
router = APIRouter(prefix="/plates/array")

class PlateArrayInput(NamedTuple):
    name: str
    description: str
    format: PlateFormat
    groups: list[str]
    positives: str = []
    negatives: str = []
    
class CreatePlateMapInput(NamedTuple):
    pf: PlateFormat
    ng: int
    
class PlateArray:
    """A router class for managing plate maps and array items."""
    def __init__(self, item: Base, link: Base, type: str):
        self.item = item   # The db model for the items to be placed on the plate
        self.link = link   # The db model linking items to MapGroups
        self.type = type   # The type of plate map to manage (fk PlatMapType)
        self.path = f"plates/array/{self.type}"
        self.irep = rep(self.item)
        self.item_name = self.item.__name__.lower()

    
    def new_plate_map(self, db: Session, input: PlateArrayInput):
        """Create a new plate map for array operations."""
        
        # Instantiate the plate map object
        pm = PlateMap(
            name = input.name,
            description = input.description,
            type = self.type,
            format_name = input.format.name
        )
        log(f"Create plate map: {rep(PlateMap).repr(pm)}")
        
        # Add wells to a group
        wells = {wp.well_name: wp for wp in input.format.wells}
        def add_wells(grp: MapGroup, wns: str):
            log(f"Add group {grp.group_type} with wells {wns}:")
            log(f"  {rep(MapGroup).repr(grp)}")
            for wn in wns.split(","):
                wn = wn.strip()
                if wn not in wells: 
                    raise ValueError(f"Well {wn} not in plate format {input.format.name}")
                grp.doses.append(
                    Dose(concentration__value=0.0, concentration__unit="uM", well_pos=wells[wn])
                )
        
        # Create the groups
        groups = []
        if input.negatives:
            groups.append(MapGroup(group_type="negatives"))
        if input.positives:
            groups.append(MapGroup(group_type="positives"))
        for wns in input.groups:
            groups.append(MapGroup(group_type="experimental"))
        # Add them to the plate map. This must be done before adding wells, for some reason.
        pm.map_groups = groups
        
        # Now add the wells to the groups
        i = 0
        for grp in pm.map_groups:
            if grp.group_type == "negatives":
                add_wells(grp, input.negatives)
            elif grp.group_type == "positives":
                add_wells(grp, input.positives)
            else:
                add_wells(grp, input.groups[i])
                i += 1

        # Store it all...
        with transact(db):
            pm.map_groups = groups
            db.add(pm)
        return pm

    def plate_map_view(self, ht: Context, 
        filter: Callable[[int,int,PlateMap], bool] = None,
        submit: Html = None, ng: int | None = None
    ) -> Html:
        """View all or selected plate maps of this type
              filter: a predicate that takes group count, well count and PlateMap 
              and specifies which to include in the list.
        """
        # Query the plate maps, count groups and wells
        groups = ht.db.query(
            func.count(MapGroup.id).label("group_count"), 
            MapGroup.plate_map_id
        ).group_by(MapGroup.plate_map_id).subquery()
        
        wells = ht.db.query(
            func.count(Dose.id).label("well_count"), 
            MapGroup.plate_map_id
        ).filter(
            MapGroup.id == Dose.map_group_id
        ).group_by(MapGroup.plate_map_id).subquery()

        ts = ht.db.query(groups, wells, PlateMap).\
            filter(PlateMap.type == self.type).\
            filter(PlateMap.id == groups.c.plate_map_id).\
            filter(PlateMap.id == wells.c.plate_map_id).\
        all()
        if filter is not None:
            ts = [t for t in ts if filter(t[0], t[2], t[4])]
        pms = [pm for _, _, _, _, pm in ts]
        # Find the common plate format, if any
        pfn = None
        for pm in pms:
            if pfn is None:
                pfn = pm.format_name
            elif pm.format_name != pfn:
                pfn = None
                break
        log(f"Plate selector for {len(pms)} maps:")
        for pm in pms:
            log(f"  {pm}")
        
        # Columns for the table
        columns = {
            "id": "PlateMap.id",
            "type": "PlateMap.type",
            "name": "PlateMap.name",
            "description": "PlateMap.description",
            "groups": "groups",
            "wells": "wells",
            "format": "PlateMap.format_name",
            "created": "PlateMap.created",
        }
        maprep = Rep.join({"groups": int, "map_id1": int, "wells": int, "map_id2": int}, PlateMap)(columns)

        view = tabs(ht, {pm.id: plate_view(ht, pm) for pm in pms})
        if submit is not None:
            view = submit + view
        crep = rep(CreatePlateMapInput)
        if ng is not None:
            crep.default("ng", ng)
        crep.options("ng", range(1, 13))
        crep.default("pf", pfn)
        form = ht.p("") + ht.rform(f"{self.path}/map", crep, method="get")
        pfand = f" {pfn}" if pfn is not None else "" 
        pfpar = "&pf=" + pfn if pfn is not None else ""
        if ng is not None:
            gl = ht.link(f"{self.path}/map?ng={ng}{pfpar}")(f"Create a new{pfand} map with {ng} groups.")
        else:
            gl = ht.p(f"Create a new{pfand} map:") + form
        return ht.div(
            ht.p("Click on a plate map to view details."),
            table(ts, maprep)(id="plate_map"),
            gl, view,
        )


    #
    #   Overview of all plate maps of this type
    #
    async def view_maps(self, ht: Context = Depends(context)):
        """View all plate maps of this type"""

        return ht.page(f"Plate Maps for {self.type}",
            ht.h2(f"Plate maps for {self.type}"),
            ht.h3(f"The following plate maps are available for {self.type}:"),
            self.plate_map_view(ht)
        ).pretty()
    
    #
    #   Select items to be mapped to a plate
    #
    async def select_items(self, ht: Context = Depends(context)):
        """Select items to be mapped"""
        items = ht.db.query(self.item).all()
        h = ht.h3(f"Select {word_s(self.item_name)} to be mapped to the plate")
        frep = rep({f"{self.item_name}_id": int})
        return ht.page(h,
            ht.h2(h), ht.p(f"Select all {word_s(self.item_name)} in the list and then submit this form:"),
            ht.rform(f"{self.path}/select", frep, tag="submit-form"),
            table(items, self.irep)(id=self.item_name),
    ).pretty()
        
    #
    #   Select mapping options for the given items
    #
    def suitable_plate_maps(self, ht: Context, items: list[Base], pf: PlateFormat = None):
        pms = ht.db.query(PlateMap).filter(PlateMap.type == self.type).all()
        if pf is not None:
            pms = [pm for pm in pms if pm.format_name == pf.name]
        pms = [pm for pm in pms if len(pm.map_groups) >= len(items)]
        pms = sorted(pms, key=lambda pm: len(pm.map_groups))
        log(f"Suitable maps: {len(pms)}")
        return pms

    def select_plate_map(self, 
        ht: Context, items: list[Base], 
        pms: list[PlateMap] = None,
        back: str = None,
        submit: Html = None
    ): # f"{self.path}/select"
        # Get the suitable plate maps
        if pms is None:
            pms = self.suitable_plate_maps(ht, items)

        # Present the selection page
        word_items = f"{len(items)} {word_s(self.item_name)}"
        return ht.div(
            ht.h2(f"You selected these {word_items} for mapping:"),
            table(items, self.irep),
            ht.when(back)( ht.p(ht.link(back)(f"Change the {self.item_name} selection")) ),
            ht.h2(f"Select a plate map for arraying these {word_items} on the plate:"),
            self.plate_map_view(ht, 
                filter = lambda gc, wc, pm: pm in pms, 
                submit = submit, ng = len(items)
            )
        )

    async def select_items_post(self, request: HttpRequest, ht: Context = Depends(context)):
        """Present all existing plate maps for selection, or allow creation of a new one"""
        # Get the request input data
        form = await request.form()
        ids = form[f"{self.item_name}_id"].split(",")
        if len(ids) < 1: 
            raise ValueError(f"Please select one or more {word_s(self.item_name)}")
        items = [ht.db.query(self.item).get(id) for id in ids]   # get items from db
        return self.select_plate_map(ht, items).pretty()
    
    #
    #   New plate map creation
    #
    async def create_map(self, request: HttpRequest, ht: Context = Depends(context)):
        """Create a new plate map"""
        pf = ht.db.query(PlateFormat).get(request.query_params.get("pf", "384-well"))
        if pf is None: return ht.error("Please select a plate format.")
        ng = int(request.query_params.get("ng", "3"))
        info = {"name": str, "description": str, "wells": int}  
        gform = rep(info|{f"group_{i+1}": str for i in range(ng)})
        gform.default("name", f"{self.type}{ng}-{pf.well_count}")
        gform.default("description", f"{self.type.capitalize()} map for {ng} {word_s(self.item_name)}, {pf.well_count} wells")
        gform.hidden("wells", pf.well_count)
        return ht.page("Create Assay Plate Map",
            ht.h2("Create Assay Plate Map"),
            ht.rform(f"{self.path}/map", gform, tag="submit-form"),
            ht.p("""
                 Select each group below, then add wells by clicking or dragging on the plate.
                 Click on the row or column labels to select all wells in that row or column.
                 Use the reset button to clear the plate.
                 """),
            plate_select(ht, pf, ng)
        ).pretty()
    
    async def create_map_post(self, request: HttpRequest, ht: Context = Depends(context)):
        """Create a new plate map"""
        form = await request.form()
        pf = ht.db.query(PlateFormat).get(f"{form['wells']}-well")
        inp = PlateArrayInput(
            name = form["name"],
            description = form["description"],
            format = pf,
            groups = [v for k, v in form.items() if k.startswith("group_")],
            positives = form.get("positives", ""),
            negatives = form.get("negatives", "")
        )
        pm = self.new_plate_map(ht.db, inp)
        return ht.page("Plate Map Created",
            ht.h2("Plate Map Created"),
            ht.p(f"Plate map {pm.name} created with {len(pm.map_groups)} groups."),
            ht.link(f"{self.path}/view")("View all plate maps")
        ).pretty()

    def mapped_requests(self, ht: Context, 
        step: FlowStep, form: dict, back: str,
        xss: list[list[Base]],
        mapped: Tuple[str,Base], items: list[Base], format: PlateFormat
    ):
        """Create requests for the mapped items.
             Called from FlowStep.req_post if an input needs to be arrayed.
        """
        inpi = step.ins.index(mapped[1])
        log(f"Create mapped requests, {mapped[1].__name__} at {inpi}, {len(items)} items.")
        
        # Setup the submit form. 
        #   Contains plate_map id and group assignments.
        #   Also passes through the form data from this request.
        pms = self.suitable_plate_maps(ht, items, format)
        maxng = max([len(pm.map_groups) for pm in pms])
        frep = rep({"plate_map": str} | {name_val(x): int for x in items})
        log(f"FREP: {frep}")
        for i, item in enumerate(items):
            frep.options(name_val(item), range(1, maxng+1))
            frep.default(name_val(item), i+1)
        log(f"Req rep: {step.req_rep}")
        frep |= step.req_rep < dict(form) #ObjRep.pass_through("pass", form)
        frep.name = "mapped"
        s = "s" if len(xss) > len(items) else ""
        submit = ht.div(
            ht.h3("Select a plate map above and adjust group assignments below as needed."),
            ht.rform(back, frep, tag="submit-form", submits=[f"Create Request{s}"])
        )
        
        # Present the selection page
        h = "Plate Mapping"
        return ht.page(h, ht.h2(h),
            self.select_plate_map(ht, items, back=back, submit=submit, pms=pms),
        ).pretty()

    #
    #   All the routes in this GUI section
    #
    def route(self):
        # log(f"ROUTE: {self.path}")
        router.get(f"/{self.type}/view/", response_class=HTMLResponse)(self.view_maps)
        router.get(f"/{self.type}/select/", response_class=HTMLResponse)(self.select_items)
        router.post(f"/{self.type}/select/", response_class=HTMLResponse)(self.select_items_post)
        router.get(f"/{self.type}/map/", response_class=HTMLResponse)(self.create_map)
        router.post(f"/{self.type}/map/", response_class=HTMLResponse)(self.create_map_post)

def plate_select(ht: Context, pf: PlateFormat, ng: int):        
    wps = ht.db.query(WellPos).\
        filter(WellPos.plate_format==pf.name).\
    all()
    print(f"Found {len(wps)} well positions for plate format {pf.name}")
    
    def well(wp: WellPos):
        #t = f"{wp.well_name} ({wp.row} {wp.col})"
        return ht.div(pos=wp.well_name)()
    
    reset = ht.button(id="plate-select-reset")("reset")
    gs = ht.select(id="plate-select-group")(
        *[ht.option(value=i+1)(f"Group {i+1}") for i in range(ng)]
    )
    return ht.div(ht.p(gs, reset), plate_map(ht, pf.name, well)(id="plate-select"))
    
def tabs(ht: Context, xd: dict):
    return ht.div(id="tabs")(
        ht.div(id="notab")(ht.h3("Select a map to show details")),
        *[ht.div(_class="tab", id=f"tab-{id}")(x) for id, x in xd.items()]
    )
