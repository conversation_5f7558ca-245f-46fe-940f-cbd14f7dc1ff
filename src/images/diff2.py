"""
Differentiation analysis utilities for automatic processing
"""
import json
import asyncio
import traceback
from typing import Set, Optional
from sqlalchemy.orm import Session
from sqlalchemy import text
import pandas as pd

from base.logs import make_logger
from images.differentiation import Differentiation, diff_store
from images.model import ImageSet
#from scripts.differentiation import run_differentiation
from images.parse import cleanup_temp_files, java_vm
from images.repo import Repo

log = make_logger(__name__)

def run_differentiation(db: Session, image_set: ImageSet, processed_wells: Set[str] = None, yield_progress: bool = True):
    try:
        # Use repo abstraction - Repo() function handles device mapping
        repo = Repo(image_set.device)
        
        log(f"Processing image set: {image_set.id}, bt_key: {image_set.bt_key}, device: {image_set.device}")
        
        # Check if image data is available in repo
        if not hasattr(image_set, 'bt_key') or not image_set.bt_key:
            log(f"Image set {image_set.id} missing bt_key, skipping")
            if yield_progress:
                yield {"type": "error", "message": "Missing bt_key"}
            return None
            
        # Get repo image set using bt_key_map
        try:
            repo_ims = repo.image_sets.bt_key_map.get(image_set.bt_key)
            if not repo_ims:
                log(f"Image set {image_set.id} with bt_key {image_set.bt_key} not found in repo, skipping")
                if yield_progress:
                    yield {"type": "error", "message": "Not found in repo"}
                return None
        except Exception as e:
            log(f"Error accessing repo for image set {image_set.id}: {e}")
            if yield_progress:
                yield {"type": "error", "message": f"Repo access error: {e}"}
            return None
        
        store = diff_store
        cache_dir = _get_cache_dir(image_set)
        results = []

        if processed_wells is None:
            processed_wells = set()

        # Load existing state
        state_path = f"{cache_dir}/state.json"
        if store.exists(state_path):
            try:
                with store.open(state_path, "r") as f:
                    state_data = json.load(f)
                    processed_wells.update(state_data.get("processed_wells", []))
                    results = state_data.get("results", [])
                    log(f"Loaded existing state: {len(processed_wells)} processed wells, {len(results)} results")
            except Exception as e:
                log(f"Error loading state: {e}")

        try:
            # Get image set from repo using bt_key
            repo_ims = repo.image_sets.bt_key_map.get(image_set.bt_key)
            if not repo_ims:
                log(f"No repo image set found for bt_key: {image_set.bt_key}")
                if yield_progress:
                    yield {"type": "error", "error": f"No repo image set found for bt_key: {image_set.bt_key}"}
                    yield {"type": "finished", "total": 0}
                return None
            
            if len(repo_ims.wells) == 0:
                log(f"No wells found in repo image set: {image_set.bt_key}")
                if yield_progress:
                    yield {"type": "error", "error": f"No wells found in repo image set: {image_set.bt_key}"}
                    yield {"type": "finished", "total": 0}
                return None
        
            # Check if image data is accessible
            try:
                # Test access to first well to ensure data is available
                first_well = next(iter(repo_ims.wells.values()))
                if not first_well.images:
                    log(f"No images found in wells for {image_set.bt_key}")
                    if yield_progress:
                        yield {"type": "error", "error": f"No images found in wells for {image_set.bt_key}"}
                        yield {"type": "finished", "total": 0}
                    return None
            except Exception as e:
                log(f"Error accessing image data for {image_set.bt_key}: {e}")
                if yield_progress:
                    yield {"type": "error", "error": f"Error accessing image data: {str(e)}"}
                    yield {"type": "finished", "total": 0}
                return None
            
            total_wells = len(repo_ims.wells)
            processed_count = len(processed_wells)

            if yield_progress:
                yield {
                    "type": "start", 
                    "total": total_wells,
                    "processed": processed_count,
                    "plate": repo_ims.plate_name,
                    "day": repo_ims.day
                }

            with java_vm():
                log(f"Found repo image set: {repo_ims.plate_name} day {repo_ims.day} with {len(repo_ims.wells)} wells")

                for i, well_name in enumerate(repo_ims.wells.keys()):
                    if well_name in processed_wells:
                        log(f"Skipping already processed well {well_name}")
                        if yield_progress:
                            yield {
                                "type": "complete",
                                "well": well_name,
                                "progress": int((i + 1) / len(repo_ims.wells) * 100),
                                "data": {"Status": "Already Processed"}
                            }
                        continue
                    
                    log(f"=== Processing well {well_name} ===")
                    
                    try:
                        # Create differentiation instance using repo ImageSet
                        diff = create_differentiation_instance_from_repo(db, repo_ims, well_name)
                        
                        log(f"Running measure() for well {well_name}")
                        result = diff.measure()
                        log(f"Measure result for well {well_name}: {result}")

                        # Generate JPEG versions
                        diff.raw_channels_jpeg
                        diff.visualization_jpeg
                        diff.nuclei_mask_jpeg
                        diff.nuclei_intensity_jpeg
                        diff.lipid_mask_jpeg
                        diff.lipid_intensity_jpeg
                        
                        if result is not None:
                            if isinstance(result, dict):
                                if result.get("Status") == "Skipped":
                                    log(f"Well {well_name} was skipped: {result.get('Reason', 'Unknown reason')}")
                                    if yield_progress:
                                        yield {
                                            "type": "skipped",
                                            "well": well_name,
                                            "progress": int((i + 1) / len(repo_ims.wells) * 100),
                                            "data": result
                                        }
                                else:
                                    log(f"Well {well_name} completed successfully")
                                    results.append(result)
                                    if yield_progress:
                                        yield {
                                            "type": "complete",
                                            "well": well_name,
                                            "progress": int((i + 1) / len(repo_ims.wells) * 100),
                                            "data": result
                                        }
                            else:
                                log(f"Well {well_name} completed successfully")
                                results.append(result)
                                if yield_progress:
                                    yield {
                                        "type": "complete",
                                        "well": well_name,
                                        "progress": int((i + 1) / len(repo_ims.wells) * 100),
                                        "data": result.iloc[0].to_dict()
                                    }
                        else:
                            log(f"Well {well_name} was filtered out (returned None)")
                            if yield_progress:
                                yield {
                                    "type": "skipped",
                                    "well": well_name,
                                    "progress": int((i + 1) / len(repo_ims.wells) * 100),
                                    "data": {"Status": "Filtered", "Reason": "No result generated"}
                                }
                    
                        processed_wells.add(well_name)
                    
                    except Exception as e:
                        log(f"Error processing well {well_name}: {e}")
                        import traceback
                        traceback.print_exc()
                        if yield_progress:
                            yield {
                                "type": "error",
                                "well": well_name,
                                "progress": int((i + 1) / len(repo_ims.wells) * 100),
                                "error": str(e)
                            }

        except Exception as e:
            log(f"Error during processing: {e}")
            traceback.print_exc()
            if yield_progress:
                yield {"type": "error", "error": str(e)}
        finally:
            if results:
                with store.open(state_path, "w") as f:
                    state_data = {"processed_wells": list(processed_wells), "results": results}
                    json.dump(state_data, f, indent=4)
            cleanup_temp_files()

        if results:
            df = pd.DataFrame(results)
            output_file = f"{repo_ims.plate_name}_day{repo_ims.day}_differentiation.csv"
            output_path = f"{cache_dir}/{output_file}"
            with store.open(output_path, "w") as f:
                df.to_csv(f)
            log(f"Results saved to {output_file}")
            if yield_progress:
                yield {"type": "finished", "total": len(results)}
            return df
        else:
            log("No results generated")
            if yield_progress:
                yield {"type": "finished", "total": 0}
            return None

    except Exception as e:
        log(f"Error in run_differentiation for image set {image_set.id}: {e}")
        if yield_progress:
            yield {"type": "error", "message": str(e)}
        return None

def should_run_differentiation(image_set: ImageSet) -> bool:
    """Check if differentiation analysis should be run for this image set"""
    return image_set.device == "olympus" and image_set.lighting == "FL"

def has_required_channels(db: Session, image_set: ImageSet) -> bool:
    """Check if image set has required channels for differentiation analysis"""
    try:
        ims_query = db.execute(
            text(
                (
                    "SELECT distinct(image_channel.channel_name) as channel "
                    + " FROM image_channel JOIN image ON image_channel.image_id"
                    + " = image.id WHERE image.image_set = :ims"
                )
            ),
            {"ims": image_set.id}
        )
        
        channels = set()
        for row in ims_query:
            channels.add(row[0])
        
        has_dapi = "DAPI" in channels
        has_lipid = "Cy5" in channels or "TRITC" in channels
        
        log(f"Image set {image_set.id} channels: {channels}, DAPI: {has_dapi}, Lipid: {has_lipid}")
        
        return has_dapi and has_lipid
        
    except Exception as e:
        log(f"Error checking channels for image set {image_set.id}: {e}")
        return False

def differentiation_results_exist(image_set: ImageSet) -> bool:
    """Check if differentiation results already exist for this image set"""
    try:
        store = diff_store
        plate_dir = f"{image_set.plate.name}/Day{image_set.day}"
        output_file = f"{image_set.plate.name}_day{image_set.day}_differentiation.csv"
        output_path = f"{plate_dir}/{output_file}"
        
        return store.exists(output_path)
    except Exception as e:
        log(f"Error checking if differentiation results exist for image set {image_set.id}: {e}")
        return False

def get_differentiation_error_count(image_set: ImageSet) -> int:
    """Get the number of failed attempts for this image set"""
    try:
        store = diff_store
        plate_dir = f"{image_set.plate.name}/Day{image_set.day}"
        error_file = f"{plate_dir}/error_count.json"
        
        if store.exists(error_file):
            with store.open(error_file, "r") as f:
                data = json.load(f)
                return data.get("error_count", 0)
        return 0
    except Exception as e:
        log(f"Error getting error count for image set {image_set.id}: {e}")
        return 0

def increment_differentiation_error_count(image_set: ImageSet) -> int:
    """Increment the error count for this image set"""
    try:
        store = diff_store
        plate_dir = f"{image_set.plate.name}/Day{image_set.day}"
        error_file = f"{plate_dir}/error_count.json"
        
        current_count = get_differentiation_error_count(image_set)
        new_count = current_count + 1
        
        with store.open(error_file, "w") as f:
            json.dump({"error_count": new_count}, f)
        
        log(f"Incremented error count for image set {image_set.id} to {new_count}")
        return new_count
    except Exception as e:
        log(f"Error incrementing error count for image set {image_set.id}: {e}")
        return 0

def clear_differentiation_state(image_set: ImageSet) -> None:
    """Clear all differentiation state files for this image set"""
    try:
        store = diff_store
        plate_dir = f"{image_set.plate.name}/Day{image_set.day}"
        
        files_to_delete = [
            f"{plate_dir}/state.json",
            f"{plate_dir}/{image_set.plate.name}_day{image_set.day}_differentiation.csv",
            f"{plate_dir}/error_count.json"
        ]
        
        for file_path in files_to_delete:
            if store.exists(file_path):
                store.delete(file_path)
                log(f"Deleted {file_path}")
        
        cleanup_temp_files()
        
        log(f"Cleared differentiation state for image set {image_set.id}")
    except Exception as e:
        log(f"Error clearing differentiation state for image set {image_set.id}: {e}")

async def trigger_differentiation_analysis(image_set: ImageSet) -> None:
    """Trigger differentiation analysis for an image set asynchronously"""
    try:
        log(f"Starting automatic differentiation analysis for image set {image_set.id}")
        
        error_count = get_differentiation_error_count(image_set)
        if error_count >= 3:
            log(f"Differentiation analysis for image set {image_set.id} has failed 3 times, skipping")
            return
        
        clear_differentiation_state(image_set)
        
        plate_name = image_set.plate.name
        day = image_set.day
        
        result = run_differentiation(plate_name, day, yield_progress=False)
        
        if result is not None:
            log(f"Successfully completed differentiation analysis for image set {image_set.id}")
        else:
            log(f"Differentiation analysis for image set {image_set.id} completed but no results generated")
            
    except Exception as e:
        log(f"Error in differentiation analysis for image set {image_set.id}: {e}")
        
        new_error_count = increment_differentiation_error_count(image_set)
        
        if new_error_count < 3:
            log(f"Will retry differentiation analysis for image set {image_set.id} (attempt {new_error_count + 1}/3)")
            await asyncio.sleep(60)
            await trigger_differentiation_analysis(image_set)
        else:
            log(f"Differentiation analysis for image set {image_set.id} failed 3 times, giving up") 