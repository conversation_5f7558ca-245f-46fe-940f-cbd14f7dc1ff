from images.analysis import Analysis, Channel
from images.repo import ImageSet, Repo
from base.io import ImageIO, cached
from base.store import cache_store
from base.util import lazy
import numpy as np
import skimage
import skimage.filters
import skimage.measure
import skimage.segmentation
import skimage.morphology
from scipy import ndimage as ndi
import imageio
import pandas as pd
from skimage.transform import resize
from plates.render import plate_map
from plates.model import WellPos
from PIL import Image, ImageDraw, ImageFont
from base.logs import make_logger

log = make_logger(__name__)

diff_store = cache_store() / "differentiation"

class Differentiation(Analysis):
    """Differentiation efficiency for Fluorescent images - now inherits from Analysis"""
    
    def __init__(self, pipeline, image_set: ImageSet, well: str):
        super().__init__("differentiation", pipeline, image_set, well)
        self.store = diff_store / image_set.plate.name / f"Day{image_set.day}" / f"{image_set.id}" / well
        
        # Differentiation-specific attributes
        self.lipid_channels = ["Cy5", "TRITC"]
        self.skip_channels = ["FITC"]  # Extensible list of channels to skip
        
        self.pixels_per_micron = self._get_pixels_per_micron()

    def _get_pixels_per_micron(self):
        """Get pixels per micron conversion factor from image metadata"""
        try:
            # Try to get from image set metadata first
            if hasattr(self.image_set, 'pixelsPerMm') and self.image_set.pixelsPerMm:
                return self.image_set.pixelsPerMm / 1000.0
            
            # Fallback: try to get from repository metadata using bt_key_map
            try:
                bt_key = f"main/{self.image_set.id}"
                if hasattr(self.repo.image_sets, 'bt_key_map'):
                    repo_data = self.repo.image_sets.bt_key_map.get(bt_key)
                elif hasattr(self.repo.image_sets, 'image_sets'):
                    repo_data = self.repo.image_sets.image_sets.get(bt_key)
                else:
                    repo_data = None
                
                if repo_data and hasattr(repo_data, 'pixelsPerMm') and repo_data.pixelsPerMm:
                    return repo_data.pixelsPerMm / 1000.0
            except (AttributeError, KeyError):
                pass
            
            # Default fallback based on typical Olympus settings
            return 2.0
        except Exception as e:
            print(f"Error getting pixels per micron: {e}")
            return 2.0

    # Three input channels - now use Analysis.load() method
    @cached("dapi.tif", Channel)
    def dapi(self):
        log(f"Loading DAPI channel for well {self.well}")
        try:
            result = self.load("DAPI")
            log(f"DAPI channel loaded successfully for well {self.well}: {result}")
            return result
        except Exception as e:
            log(f"Error loading DAPI for well {self.well}: {e}")
            raise
    
    @cached("cy5.tif", Channel)
    def cy5(self):
        log(f"Loading Cy5 channel for well {self.well}")
        try:
            result = self.load("Cy5")
            log(f"Cy5 channel loaded successfully for well {self.well}: {result}")
            return result
        except Exception as e:
            log(f"Cy5 not available for well {self.well}: {e}")
            return None
    
    @cached("tritc.tif", Channel)
    def tritc(self):
        log(f"Loading TRITC channel for well {self.well}")
        try:
            result = self.load("TRITC")
            log(f"TRITC channel loaded successfully for well {self.well}: {result}")
            return result
        except Exception as e:
            log(f"TRITC not available for well {self.well}: {e}")
            return None
    
    @property
    def lipid(self):
        """Get the available lipid channel (Cy5, TRITC, or FITC as fallback)"""
        # Try primary lipid channels first
        for channel_name in self.lipid_channels:
            try:
                if channel_name == "Cy5" and self.cy5:
                    return self.cy5
                elif channel_name == "TRITC" and self.tritc:
                    return self.tritc
            except:
                continue
        
        # Fallback to FITC if no dedicated lipid channel
        try:
            fitc_channel = self.load("FITC")
            if fitc_channel:
                return fitc_channel
        except:
            pass
        
        raise ValueError(f"No lipid channel found for {self.well}. Available channels: {self.lipid_channels}")

    @property
    def lipid_channel_name(self):
        """Get the name of the lipid channel being used"""
        if self.cy5:
            return "Cy5"
        elif self.tritc:
            return "TRITC"
        else:
            # Check if FITC is available as fallback
            try:
                self.load("FITC")
                return "FITC"
            except:
                return None
    
    # All the segmentation and analysis methods remain the same...
    @cached("nuclei_mask.tif")
    def segment_nuclei(self):
        """Segment nuclei from DAPI channel"""
        dapi_arr = self.dapi.img.copy()
        h, w = dapi_arr.shape
        crop_h_start, crop_h_end = 0, h
        crop_w_start, crop_w_end = 0, w
        if "m3" in self.image_set.plate.name.lower():
            crop_h_start, crop_h_end = int(h * 0.15), int(h * 0.85)
            crop_w_start, crop_w_end = int(w * 0.15), int(w * 0.85)
        dapi_cropped = dapi_arr[crop_h_start:crop_h_end, crop_w_start:crop_w_end].copy()
        
        filtered_image = skimage.filters.difference_of_gaussians(dapi_cropped, 1, 12)
        normalized = filtered_image / np.max(filtered_image)
        blurr = skimage.filters.gaussian(normalized, sigma=1)
        thresholds = skimage.filters.threshold_multiotsu(blurr)
        regions = np.digitize(blurr, bins=thresholds)
        binary_arr = regions > 0
        distance = ndi.distance_transform_edt(binary_arr)
        local_max_coords = skimage.feature.peak_local_max(distance, min_distance=7)
        local_max_mask = np.zeros(distance.shape, dtype=bool)
        local_max_mask[tuple(local_max_coords.T)] = True
        markers = skimage.measure.label(local_max_mask)
        segmented_cells = skimage.segmentation.watershed(-distance, markers, mask=binary_arr)
        
        # Filter out odd shapes
        exclude = []
        for region in skimage.measure.regionprops(segmented_cells):
            label = region.label
            circularity = (4*np.pi*region.area)/(region.perimeter**2)
            if region.area < 50 or region.area > 3000 or circularity > 1.2 or circularity < 0.8:
                exclude.append(label)
        segmented_cells = np.where(np.isin(segmented_cells, exclude), 0, segmented_cells)
        
        # Create full-size mask and place cropped result back
        full_mask = np.zeros(dapi_arr.shape, dtype=segmented_cells.dtype)
        full_mask[crop_h_start:crop_h_end, crop_w_start:crop_w_end] = segmented_cells
        
        return full_mask

    @cached("lipid_mask.tif")
    def lipid_foreground(self):
        """Segment lipid droplets with edge cropping to exclude well boundaries"""
        lipid_img = self.lipid.img
        h, w = lipid_img.shape
        crop_h_start, crop_h_end = 0, h
        crop_w_start, crop_w_end = 0, w
        if "m3" in self.image_set.plate.name.lower():
            crop_h_start, crop_h_end = int(h * 0.15), int(h * 0.85)
            crop_w_start, crop_w_end = int(w * 0.15), int(w * 0.85)
        lipid_cropped = lipid_img[crop_h_start:crop_h_end, crop_w_start:crop_w_end].copy()
        
        blurr = skimage.filters.gaussian(lipid_cropped, sigma=1)
        thresh = skimage.filters.threshold_otsu(blurr)
        binary_arr = blurr > thresh
        binary_arr = skimage.morphology.binary_dilation(binary_arr)
        
        # Add noise filtering - remove small objects before creating full mask
        binary_arr = skimage.morphology.remove_small_objects(binary_arr, min_size=20)
        
        full_mask = np.zeros(lipid_img.shape, dtype=np.uint8)
        full_mask[crop_h_start:crop_h_end, crop_w_start:crop_w_end] = (binary_arr * 255).astype(np.uint8)
        
        return full_mask
    
    def _create_intensity_mask(self, img, sigma_bg=5, sigma_blur=1, min_area=50):
        """Create intensity mask that preserves original values"""
        img = img.astype(np.float32)
        h, w = img.shape
        crop_h_start, crop_h_end = 0, h
        crop_w_start, crop_w_end = 0, w
        if "m3" in self.image_set.plate.name.lower():
            crop_h_start, crop_h_end = int(h * 0.15), int(h * 0.85)
            crop_w_start, crop_w_end = int(w * 0.15), int(w * 0.85)
        cropped = img[crop_h_start:crop_h_end, crop_w_start:crop_w_end].copy()
        
        # Background correction (like ImageJ)
        gauss_bg = skimage.filters.gaussian(cropped, sigma=sigma_bg, preserve_range=True)
        corrected = cropped / (gauss_bg + 1)  # Avoid division by zero
        
        # Create binary mask
        blurr = skimage.filters.gaussian(corrected, sigma=sigma_blur)
        thresh = skimage.filters.threshold_otsu(blurr) if np.max(blurr) > 0 else 0
        binary_mask = blurr > thresh
        
        # Morphological operations
        if np.any(binary_mask):
            binary_mask = skimage.morphology.binary_erosion(binary_mask)
            binary_mask = skimage.morphology.binary_dilation(binary_mask)
            binary_mask = skimage.morphology.binary_closing(binary_mask)
            binary_mask = ndi.binary_fill_holes(binary_mask)
            binary_mask = skimage.morphology.remove_small_objects(binary_mask, min_size=min_area)
        
        masked_intensity = np.where(binary_mask, cropped, 0)  # Use original cropped values
        if np.max(masked_intensity) > 0:
            masked_intensity = (masked_intensity / np.max(masked_intensity) * 65535).astype(np.uint16)
        else:
            masked_intensity = masked_intensity.astype(np.uint16)

        full_result = np.zeros(img.shape, dtype=np.uint16)
        full_result[crop_h_start:crop_h_end, crop_w_start:crop_w_end] = masked_intensity
        
        return full_result

    @cached("nuclei_intensity_mask.tif")
    def nuclei_intensity_mask(self):
        """Create 16-bit intensity mask for nuclei"""
        return self._create_intensity_mask(self.dapi.img, sigma_bg=5, sigma_blur=1, min_area=50)

    @cached("lipid_intensity_mask.tif")
    def lipid_intensity_mask(self):
        """Create 16-bit intensity mask for lipids"""
        return self._create_intensity_mask(self.lipid.img, sigma_bg=5, sigma_blur=1, min_area=10)
    
    @cached("visualization_mask.tif")
    def visualization_mask(self):
        """Create visualization mask (red=lipid, blue=nuclei)"""
        nuclear_labels = self.segment_nuclei
        lipid_mask = self.lipid_foreground
        
        h, w = lipid_mask.shape
        mask = np.zeros((h, w, 3), dtype=np.uint8)
        mask[:, :, 0] = lipid_mask  # Red channel
        mask[:, :, 2] = (nuclear_labels > 0).astype(np.uint8) * 255  # Blue channel
        return mask

    @cached("nuclei_mask.png")
    def nuclei_mask_jpeg(self):
        """Pre-generate JPEG version of nuclei mask"""
        mask = self.segment_nuclei
        mask_8bit = (mask > 0).astype(np.uint8) * 255
        return self._save_as_jpeg(mask_8bit, "nuclei_mask.jpeg")

    @cached("lipid_mask.png") 
    def lipid_mask_jpeg(self):
        """Pre-generate JPEG version of lipid mask"""
        mask = self.lipid_foreground
        return self._save_as_jpeg(mask, "lipid_mask.jpeg")

    @cached("nuclei_intensity_mask.png")
    def nuclei_intensity_jpeg(self):
        """Pre-generate JPEG version of nuclei intensity"""
        mask = self.nuclei_intensity_mask
        mask_8bit = scale_to_8bit(mask)
        return self._save_as_jpeg(mask_8bit, "nuclei_intensity_mask.jpeg")

    @cached("lipid_intensity_mask.png")
    def lipid_intensity_jpeg(self):
        """Pre-generate JPEG version of lipid intensity"""
        mask = self.lipid_intensity_mask
        mask_8bit = scale_to_8bit(mask)
        return self._save_as_jpeg(mask_8bit, "lipid_intensity_mask.jpeg")

    @cached("visualization_mask.jpeg")
    def visualization_jpeg(self):
        """Pre-generate JPEG version of visualization"""
        mask = self.visualization_mask
        return self._save_as_jpeg(mask, "visualization.jpeg")

    @cached("raw_channels.jpeg")
    def raw_channels_jpeg(self):
        """Pre-generate JPEG of raw channel composite"""
        h, w = self.dapi.img.shape
        img = np.zeros((h, w, 3), dtype=np.uint16)
        img[:, :, 2] = self.dapi.img
        if self.lipid:
            img[:, :, 0] = self.lipid.img
        img_8bit = scale_to_8bit(img)
        return self._save_as_jpeg(img_8bit, "raw_channels.jpeg")

    def _save_as_jpeg(self, img_array, filename):
        jpeg_path = filename
        with self.store.open(jpeg_path, "wb") as f:
            imageio.v2.imwrite(f, img_array, format="jpeg", quality=85)
        return img_array

    def get_jpeg_path(self, mask_type):
        """Get the pre-generated JPEG path for any mask type"""
        jpeg_map = {
            "main_image": "raw_channels.jpeg",
            "visualization": "visualization.jpeg", 
            "nuclei_mask.png": "nuclei_mask.jpeg",
            "nuclei_intensity_mask.png": "nuclei_intensity_mask.jpeg",
            "lipid_mask.png": "lipid_mask.jpeg",
            "lipid_intensity_mask.png": "lipid_intensity_mask.jpeg"
        }
        return jpeg_map.get(mask_type, mask_type)

    def get_crop_bounds(self):
        """Get the valid crop area bounds (matching mask processing)"""
        h, w = self.dapi.img.shape
        margin = 0.15
        return {
            'left': int(w * margin),
            'top': int(h * margin), 
            'right': int(w * (1 - margin)),
            'bottom': int(h * (1 - margin))
        }

    def _has_skip_channels(self) -> bool:
        """Check if this well has any channels that should be skipped"""
        try:
            rwell = self.repo.get_well(self.image_set.bt_key, self.well)
            rim = rwell.img_for_tag("raw")
            channels = set(rim.info.channels)
            has_skip = bool(channels.intersection(self.skip_channels))
            log(f"Well {self.well}: channels={channels}, skip_channels={self.skip_channels}, has_skip={has_skip}")
            return has_skip
        except Exception as e:
            log(f"Error checking skip channels for well {self.well}: {e}")
            return False

    def measure(self):
        """Measure differentiation by counting nuclei with lipid staining"""
        log(f"Starting measure() for well {self.well}")
        
        # Skip wells with excluded channels
        if self._has_skip_channels():
            # Get the actual channels that are being skipped
            try:
                rwell = self.repo.get_well(self.image_set.bt_key, self.well)
                rim = rwell.img_for_tag("raw")
                channels = set(rim.info.channels)
                skip_channels_found = channels.intersection(self.skip_channels)
                log(f"Well {self.well} skipped due to excluded channels: {skip_channels_found}")
                
                # Return a special result indicating the well was skipped
                return {
                    "Well": self.well,
                    "Positive": 0,
                    "Negative": 0,
                    "Total": 0,
                    "Percent Positive": 0.0,
                    "Status": "Skipped",
                    "Reason": f"Contains excluded channels: {', '.join(skip_channels_found)}"
                }
            except Exception as e:
                log(f"Error getting skip channel details for well {self.well}: {e}")
                return {
                    "Well": self.well,
                    "Positive": 0,
                    "Negative": 0,
                    "Total": 0,
                    "Percent Positive": 0.0,
                    "Status": "Skipped",
                    "Reason": "Error checking channels"
                }
        
        log(f"Well {self.well} passed skip channel check, proceeding with analysis")
        
        try:
            log(f"Getting nuclei segmentation for well {self.well}")
            nuclear_labels = self.segment_nuclei
            log(f"Nuclei segmentation complete for well {self.well}")
            
            log(f"Getting lipid foreground for well {self.well}")
            lipid_mask = self.lipid_foreground
            log(f"Lipid foreground complete for well {self.well}")
            
            positive_count = 0
            negative_count = 0
            tally = []
            
            log(f"Processing {len(skimage.measure.regionprops(nuclear_labels))} nuclei for well {self.well}")
            
            for prop in skimage.measure.regionprops(nuclear_labels):
                x, y = np.transpose(np.array([coord for coord in prop.coords]))
                x = x.astype(np.uint16)
                y = y.astype(np.uint16)
                try:
                    values = lipid_mask[x, y]
                    if np.max(values) > 0:
                        tally.append(1)
                    else:
                        tally.append(0)
                except IndexError:
                    log(f"Index error for well {self.well}, continuing")
                    continue

            values = np.array(tally)
            positive_count = np.sum(values > 0)
            negative_count = np.sum(values == 0)
            
            log(f"Well {self.well}: {positive_count} positive, {negative_count} negative, {len(tally)} total nuclei")

            # Convert numpy types to native Python types for JSON serialization
            total_count = int(positive_count + negative_count)
            percent_positive = float(100 * positive_count / total_count) if total_count > 0 else 0.0

            # Return a dictionary with native Python types
            result = {
                "Well": self.well,
                "Positive": int(positive_count),
                "Negative": int(negative_count),
                "Total": total_count,
                "Percent Positive": percent_positive,
                "Status": "Complete"
            }

            log(f"Well {self.well} analysis complete: {result}")
            return result
            
        except Exception as e:
            log(f"Error in measure() for well {self.well}: {e}")
            import traceback
            traceback.print_exc()
            return {
                "Well": self.well,
                "Positive": 0,
                "Negative": 0,
                "Total": 0,
                "Percent Positive": 0.0,
                "Status": "Error",
                "Reason": str(e)
            }
    
    def add_scale_bar_to_image(self, img_array, scale_length_um=50, debug_image_type=None):
        """Add scale bar to differentiation image"""
        if img_array.dtype != np.uint8:
            if img_array.max() <= 1.0:
                img_array = (img_array * 255).astype(np.uint8)
            else:
                img_array = ((img_array / img_array.max()) * 255).astype(np.uint8)
        
        # Convert to PIL for drawing
        if len(img_array.shape) == 2:
            pil_img = Image.fromarray(img_array, mode='L')
        else:
            pil_img = Image.fromarray(img_array, mode='RGB')
        
        draw = ImageDraw.Draw(pil_img)
        
        # Calculate scale bar dimensions
        scale_length_pixels = int(scale_length_um * self.pixels_per_micron)
        bar_height = max(3, int(img_array.shape[0] * 0.02))
        
        margin = int(min(img_array.shape[:2]) * 0.05)
        x_end = img_array.shape[1] - margin
        x_start = x_end - scale_length_pixels
        y_pos = img_array.shape[0] - margin - bar_height
        
        draw.rectangle([x_start-1, y_pos-1, x_end+1, y_pos+bar_height+1], fill='black')
        draw.rectangle([x_start, y_pos, x_end, y_pos+bar_height], fill='white')
        
        try:
            font_size = max(12, int(img_array.shape[0] * 0.04))
            font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", font_size)
        except:
            font = ImageFont.load_default()
        
        text = f"{scale_length_um} μm"
        text_bbox = draw.textbbox((0, 0), text, font=font)
        text_width = text_bbox[2] - text_bbox[0]
        text_height = text_bbox[3] - text_bbox[1]
        
        text_x = x_start + (scale_length_pixels - text_width) // 2
        text_y = y_pos - text_height - 2
    
        for dx, dy in [(-1,-1), (-1,1), (1,-1), (1,1)]:
            draw.text((text_x+dx, text_y+dy), text, font=font, fill='black')
        draw.text((text_x, text_y), text, font=font, fill='white')
        
        return np.array(pil_img)

def scale_to_8bit(img, low_perc=1, high_perc=99.5):
    if img.ndim == 2:
        # Single channel
        low = np.percentile(img, low_perc)
        high = np.percentile(img, high_perc)
        if high > low:
            scaled = (img - low) / (high - low)
            scaled = np.clip(scaled, 0, 1)
            return (scaled * 255).astype(np.uint8)
        else:
            return np.zeros_like(img, dtype=np.uint8)
    elif img.ndim == 3:
        # Multi-channel
        img8 = np.zeros_like(img, dtype=np.uint8)
        for c in range(img.shape[2]):
            channel = img[:, :, c]
            low = np.percentile(channel, low_perc)
            high = np.percentile(channel, high_perc)
            if high > low:
                scaled = (channel - low) / (high - low)
                scaled = np.clip(scaled, 0, 1)
                img8[:, :, c] = (scaled * 255).astype(np.uint8)
            else:
                img8[:, :, c] = 0
        return img8
    else:
        raise ValueError("Input must be 2D or 3D numpy array")

class DifferentiationPlateMap:
    """Plate map visualization for differentiation percentages"""
    def __init__(self, plate_name: str, plate_format: str, day: int, results_df: pd.DataFrame):
        self.plate_name = plate_name
        self.plate_format = plate_format
        self.day = day
        self.results_df = results_df
        self.caption = f"Differentiation percentage heatmap for {plate_name}, Day {day}"
    
    def html(self, ht, iset_id: int):
        """Generate HTML for the differentiation heatmap"""        
        well_data = {}
        if not self.results_df.empty:
            for _, row in self.results_df.iterrows():
                well_data[row['Well']] = {
                    'percentage': row.get('Percent Positive', 0),
                    'total': row.get('Total', 0),
                    'positive': row.get('Positive', 0),
                    'negative': row.get('Negative', 0)
                }
        
        def well(wp: WellPos):
            """Function that creates the html for a single well"""
            well_name = wp.well_name
            data = well_data.get(well_name, {'percentage': 0, 'total': 0, 'positive': 0, 'negative': 0})
            percentage = data['percentage']
            total_count = data['total']
            
            if total_count < 100:
                color = "#e8e8e8" 
                text_color = "black"
            else:
                if percentage >= 70:
                    color = "#4CAF50" 
                    text_color = "white"
                elif percentage >= 40:
                    color = "#FF9800" 
                    text_color = "white"
                elif percentage > 0:
                    color = "#F44336"  
                    text_color = "white"
                else:
                    color = "#f0f0f0"  
                    text_color = "black"
                
            title = f"{well_name}: {percentage:.1f}% differentiated ({data['positive']}/{total_count} cells)"
            
            return ht.a(
                href=f"/imaging/iset/{iset_id}/differentiation/well/{well_name}"
            )(
                f"{percentage:.0f}%"
            )(
                title=title,
                style=f"background-color: {color}; color: {text_color}; font-size: 0.7vw; text-decoration: none; display: block; padding: 2px;"
            )
        
        return ht.div(
            ht.h3("Differentiation Heatmap"),
            ht.p("Results based on binary nuclei and lipid mask overlap analysis", style="font-size: 0.9em; color: #666; margin-top: 0;"),
            plate_map(ht, self.plate_format, well),
        )