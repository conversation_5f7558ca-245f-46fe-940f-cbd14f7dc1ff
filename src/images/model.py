from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column, relationship
from sqlalchemy import ForeignKey, DateTime, String, Integer, Float, Table, Column
from datetime import datetime
from typing import List

from base.database import Base, UtcDateTime
from plates.model import Plate, WellPos
#
#  Images
#
class Device(Base):
    __tablename__ = "device"
    __enum__ = [("cytosmart", "Cytosmart"), ("olympus", "Olympus IX83")]
    name: Mapped[str] = mapped_column(primary_key=True)
    description: Mapped[str] = mapped_column(nullable=False)

class Lighting(Base):
    __tablename__ = "lighting"
    __enum__ = [("BF", "Bright field"), ("FL", "Flourescent")]
    name: Mapped[str] = mapped_column(primary_key=True)
    description: Mapped[str] = mapped_column(nullable=False)

class ImageSet(Base):
    __tablename__ = "image_set"
    id: Mapped[int] = mapped_column(primary_key=True)
    plate_id: Mapped[int] = mapped_column(ForeignKey("plate.id"), nullable=False)
    day: Mapped[int] = mapped_column(Integer, nullable=False)
    created: Mapped[datetime] = mapped_column(UtcDateTime, nullable=False)
    device: Mapped[str] = mapped_column(ForeignKey("device.name"), nullable=False)
    lighting: Mapped[str] = mapped_column(ForeignKey("lighting.name"), nullable=False)
    pix_per_mm: Mapped[float] = mapped_column(Float, nullable=False)
    size_x: Mapped[int] = mapped_column(Integer, nullable=False)
    size_y: Mapped[int] = mapped_column(Integer, nullable=False)
    bt_key: Mapped[str] = mapped_column(String, nullable=False, unique=True)  # For lookup in BigTable.

    plate: Mapped["Plate"] = relationship(Plate)
    images: Mapped[List["Image"]] = relationship("Image")
    
    def __repr__(self):
        return f"ImageSet({self.id}, {self.plate_id}, {self.day}, {self.device}, {self.lighting}, {self.size_x}x{self.size_y})"

ImageChannel = Table(
    "image_channel",
    Base.metadata,
    Column("image_id", ForeignKey("image.id")),
    Column("channel_name", ForeignKey("channel.name")),
    Column("channel_index", Integer)
)
class Channels(Base):
    __tablename__ = "channel"
    __enum__ = ["BF", "DAPI", "FITC", "TRITC", "Cy5"]
    name: Mapped[str] = mapped_column(primary_key=True)

class OriginalCellsensImage(Base):
    __tablename__ = "original_cellsens_image"
    id: Mapped[int] = mapped_column(primary_key=True)
    image_id: Mapped[int] = mapped_column(ForeignKey("image.id"))
    original_file_uri: Mapped[str] = mapped_column(unique=True)

class Image(Base):
    __tablename__ = "image"
    id: Mapped[int] = mapped_column(primary_key=True)
    image_set: Mapped[int] = mapped_column(ForeignKey("image_set.id"), nullable=False)
    object_uri: Mapped[str] = mapped_column(String, unique=True, nullable=False)
    well_pos: Mapped[int] = mapped_column(Integer, ForeignKey("well_pos.id"), nullable=False)
    size_x: Mapped[int] = mapped_column(Integer, nullable=False)
    size_y: Mapped[int] = mapped_column(Integer, nullable=False)
    created: Mapped[datetime] = mapped_column(UtcDateTime, nullable=False)
    version: Mapped[int]
    
    imageSet: Mapped[ImageSet] = relationship(ImageSet, back_populates="images")
    wellPos: Mapped[WellPos] = relationship(WellPos)
    channels: Mapped[List[Channels]] = relationship(Channels, secondary=ImageChannel)

class PipelineGoal(Base):
    __tablename__ = "pipeline_goal"
    __enum__ = ["lipid morphology", "mitochondrial activity", "viability", "differentiation efficiency"]
    name: Mapped[str] = mapped_column(primary_key=True)


class PipelineVersion(Base):
    __tablename__ = "pipeline_version"
    id: Mapped[int] = mapped_column(primary_key=True)
    image_pipeline: Mapped[int] = mapped_column(ForeignKey("image_pipeline.id"))
    template_path: Mapped[str]
    container_image: Mapped[str]
    version_number: Mapped[str] 
    
PipelineChannel = Table(
    "pipeline_channel",
    Base.metadata,
    Column("image_pipeline", ForeignKey("image_pipeline.id")),
    Column("image_channel", ForeignKey("channel.name"))
)
    
class ImagePipeline(Base):
    __tablename__ = "image_pipeline"
    id: Mapped[int] = mapped_column(primary_key=True)
    name: Mapped[str] = mapped_column(unique=True)
    goal: Mapped[str] = mapped_column(ForeignKey("pipeline_goal.name"))
    device: Mapped[str] = mapped_column(ForeignKey("device.name"))
    active: Mapped[bool] = mapped_column(default=False)
    # scale_factor: Mapped[int] = mapped_column(Float) 
    
    versions: Mapped[List[PipelineVersion]] = relationship(PipelineVersion)
    channels: Mapped[List[Channels]] = relationship(Channels, secondary=PipelineChannel)

class PipelineStatus(Base):
    __tablename__ = "pipeline_status"
    __enum__ = ["started", "failed", "completed"]
    name: Mapped[str] = mapped_column(primary_key=True)
    
class PipelineRequest(Base):
    __tablename__ = "pipeline_request"
    id: Mapped[int] = mapped_column(primary_key=True)
    pipeline_version: Mapped[int] = mapped_column(ForeignKey("pipeline_version.id"))
    image_set: Mapped[int] = mapped_column(ForeignKey("image_set.id"))
    status: Mapped[str] = mapped_column(ForeignKey("pipeline_status.name"))

class ImageMask(Base):
    __tablename__ = "image_mask"
    id: Mapped[int] = mapped_column(primary_key=True)
    image_id: Mapped[int] = mapped_column(ForeignKey("image.id"))
    result_set: Mapped[int] = mapped_column(ForeignKey("result_set.id"))
    image_scale_factor: Mapped[float]
    mask_uri: Mapped[str]
    csv_uri: Mapped[str]
    
class ResultSet(Base): 
    __tablename__ = "result_set"
    id: Mapped[int] = mapped_column(primary_key=True)
    pipeline_request: Mapped[int] = mapped_column(ForeignKey("pipeline_request.id"))
    image_pipeline_version: Mapped[int] = mapped_column(ForeignKey("pipeline_version.id"))
