from matplotlib.gridspec import GridSpec
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import scipy
import skimage
import scipy.ndimage as ndi
from imageio.v2 import imread, imwrite
from base.store import Store, File, cache_store, make
from base.io import cached
from PIL import Image as PilImage, ImageDraw

from images.model import Image, ImageSet
from images.pipeline import Pipeline
from base.util import lazy
from base.logs import make_logger
from images.repo import Repo
log = make_logger(__name__)

class Channel:
    def __init__(self, analysis: 'Analysis', name: str, img: np.ndarray):
        if not analysis:
            raise ValueError("Analysis is required for channel")
        self.analysis = analysis
        self.name = name
        self.img = img
        if img.dtype == np.uint16:
            self.maxv = 2**16-1
        else:
            raise ValueError(f"Unsupported image dtype: {img.dtype}")
        # Set up cache location
        self.store = analysis.store / name
        
    
    def __repr__(self):
        return f"{self.name}({'x'.join([str(n) for n in self.img.shape])} {self.img.dtype})"
    
    # Methods needed to cache channels using the @cached decorator
    @classmethod
    def writer(cls, fd, ch):
        imwrite(fd, ch.img, format="tiff")
        
    @classmethod
    def reader(cls, fd, *, deco = None, owner = None):
        return Channel(owner, deco.func.__name__, imread(fd, format="tiff"))
    
    mode = "b"
    
    # utility functions
    def blur(self, img: np.ndarray, sigma: float):
        imgf = img.astype(np.float32)
        blur = skimage.filters.gaussian(imgf, sigma=sigma, preserve_range=True)
        return np.minimum(blur, self.maxv).astype(self.img.dtype)
    
    @cached("bg.tif")
    def background(self):
        """Determine background"""
        log(f"Computing background for {self}")
        # Apply the rolling ball algorithm on a blurred image
        #   The blur is to avoid noise from low pixels
        background = skimage.restoration.rolling_ball(self.blur(self.img, 20), radius = 100)
        return np.minimum(background, self.img)
    
    @lazy
    def corrected(self):
        """Corrected image"""
        return self.img - self.background
    
    # @lazy
    # def blurred(self):
    #     imgf = self.img.astype(np.float32)
    #     blurr = skimage.filters.gaussian(imgf, sigma=10)
    #     thresh = skimage.filters.threshold_otsu(blurr)
    #     return imgf, blurr * (0.1*self.maxv) / thresh, thresh
    
    @lazy
    def scaled(self):
        imgf = self.img.astype(np.float32)
        blurr = skimage.filters.gaussian(imgf, sigma=10)
        thresh = skimage.filters.threshold_otsu(blurr)
        return np.minimum(self.maxv, imgf * (0.1*self.maxv) / thresh).astype(self.img.dtype)


class Analysis:
    def __init__(self, name: str, pipeline: Pipeline, image_set: ImageSet, well: str):
        self.name = name
        self.pipeline = pipeline
        self.image_set = image_set
        self.well = well
        self.store = ( pipeline.store / 
            name /
            image_set.plate.name /
            f"Day{image_set.day}" / 
            well
        )
        match image_set.device:
            case "cytosmart":
                self.repo = Repo("cytosmart")
            case "olympus":
                self.repo = Repo("olympus")
        self.data = {}

    def __repr__(self):
        return f"{self.name.capitalize()}({self.store.tag})"
    
    def load(self, itag):    
        log(f"Loading {itag} {self}")
        if itag in self.data:
            d = self.data[itag]
            log(f"  Found in cache: {itag} {d.dtype} {d.shape}")
            return Channel(self, itag.lower(), d)
        rwell = self.repo.get_well(self.image_set.bt_key, self.well)
        if self.repo.name.endswith("tiff"):
            rim = rwell.img_for_tag(itag)
            log(f"{rwell}: {len(rwell.images)} images.")
            for im in rwell.images:
                log(f" {'*' if im is rim else ' '}{im}")
            img = imread(self.repo.storage.open(rim.fn, "rb"), format="tiff")
        elif self.repo.name == "cytosmart":
            log(f"Files: {len(rwell.images)}")
            for k, im in rwell.images.items():
                log(f"  {k}: {im}")
            rim = rwell.img_for_tag("BF")
            log(f"Loading from Cytosmart file: {rim.fn}")
            img = imread(self.repo.storage.open(rim.fn, "rb"))
            log(f"  Image: {img.shape} {img.dtype}")
            self.data[itag] = img
        elif self.repo.name == "olympus":
            log(f"Files: {len(rwell.images)}")
            for k, im in rwell.images.items():
                log(f"  {k}: {im}")
            rim = rwell.img_for_tag("raw")
            channels = rim.info.channels
            if itag not in channels:
                rim.data()
                raise ValueError(f"Channel {itag} not found in {channels}")
            log(f"Loading from VSI file: {rim.fn}")
            img = rim.data()
            log(f"  Image: {img.shape} {img.dtype}")
            for i, ch in enumerate(channels):
                log(f"    Channel {i}: {ch}")
                self.data[ch] = img[..., i]
            img = self.data[itag]
        scaled = (255.0 / img.max() * (img - img.min())).astype(np.uint8)
        self.plot(img, f"{itag.lower()}-view.png")
        imwrite(self.store.open(f"{itag.lower()}-scaled.png", "wb"), scaled, format="png")
        return Channel(self, itag.lower(), img)

    def histogram(self, img, fn, range=None):
        plt.figure(figsize=(10, 6))
        plt.hist(img.ravel(), bins=256, color='blue', alpha=0.7, range=range)
        plt.title('Histogram of Pixel Values')
        plt.xlabel('Pixel Value')
        plt.ylabel('Frequency')
        plt.grid(True)
        plt.savefig(self.store.open(fn, "wb"), format="png")
        plt.close()

    def plot(self, img, fn):
        fd = self.store.open(fn, "wb")
        #log(f"Image type: {img.dtype}")
        img0 = img
        if img.dtype == bool:
            img = img.astype(np.uint8) * 255
            plt.figure(figsize=(10, 10))
            plt.imshow(img, cmap='gray')
            plt.axis('off')
            plt.savefig(fd, format="png")
            plt.close()
            return
        # Scale the image to 0-255
        img = (255.0 / img.max() * (img - img.min())).astype(np.uint8)

        # Define the grid
        fig = plt.figure(figsize=(10, 12))
        gs = GridSpec(2, 1, height_ratios=[5, 1])
        
        # Plot the image
        ax1 = fig.add_subplot(gs[0, 0])
        ax1.imshow(img, cmap='gray')

        # Plot the histogram
        ax2 = fig.add_subplot(gs[1, 0])
        ax2.hist(img0.ravel(), bins=256, color='blue')
        ax2.set_yscale('log')

        # Save the plot
        fig.tight_layout()
        fig.savefig(fd, format="png")
