from dataclasses import dataclass
from typing import Optional, Tuple, List, Dict, Any
import numpy as np
import pandas as pd
from cellpose import models
import skimage
import skimage.transform
from base.store import Store, File
from base.util import lazy
from base.io import cached
from base.logs import make_logger
from images.analysis import Analysis
from images.model import Image, ImageSet
from images.pipeline import Pipeline

log = make_logger(__name__)

class BrightfieldAnalysis(Analysis):
    """Repo-aware brightfield analysis for both Cytosmart and Olympus"""
    
    def __init__(self, pipeline: Pipeline, image_set: ImageSet, well: str):
        super().__init__("brightfield", pipeline, image_set, well)
        self.model_name = "MCL_CP_005"

    @lazy
    def load_image(self):
        """Load the brightfield image - returns raw numpy array"""
        log(f"Loading BF image for {self}")
        if "BF" in self.data:
            img = self.data["BF"]
            log(f"  Found in cache: BF {img.dtype} {img.shape}")
            return img
        
        rwell = self.repo.get_well(self.image_set.bt_key, self.well)
        
        if self.repo.name == "cytosmart":
            rim = rwell.img_for_tag("BF")
            log(f"Loading from Cytosmart file: {rim.fn}")
            from imageio.v2 import imread
            img = imread(self.repo.storage.open(rim.fn, "rb"))
            log(f"  Image: {img.shape} {img.dtype}")
            self.data["BF"] = img
            return img
        elif self.repo.name.startswith("olympus"):
            rim = rwell.img_for_tag("raw")
            channels = rim.info.channels
            if "BF" not in channels:
                raise ValueError(f"Channel BF not found in {channels}")
            log(f"Loading from VSI file: {rim.fn}")
            img = rim.data()
            log(f"  Image: {img.shape} {img.dtype}")
            for i, ch in enumerate(channels):
                self.data[ch] = img[..., i]
            return self.data["BF"]
        else:
            raise ValueError(f"Unsupported repo type: {self.repo.name}")

    @lazy
    def cellpose_model(self):
        """Load and cache Cellpose model"""
        return models.CellposeModel(gpu=False, pretrained_model=self.model_name)

    def preprocess_image(self, image: np.ndarray, scale: float = 2.5) -> np.ndarray:
        """Preprocess image for segmentation (used for Olympus images)"""
        if scale != 1.0:
            shape = image.shape
            new_shape = (int(shape[0]//scale), int(shape[1]//scale))
            image = skimage.transform.resize(image, new_shape, preserve_range=True)
        return image.astype(image.dtype)

    def segment(self, image: np.ndarray) -> np.ndarray:
        """Generate cell mask using Cellpose"""
        model = self.cellpose_model
        mask, _, _ = model.eval(image, channels=[0,0])
        return mask

    @staticmethod
    def make_foreground(mask: np.ndarray) -> np.ndarray:
        """Create foreground mask for cluster analysis"""
        mask = mask.astype(bool)
        r0 = skimage.morphology.footprint_rectangle(20, 2)
        r90 = skimage.morphology.footprint_rectangle(2, 20)
        diamond = skimage.morphology.diamond(5)
        mask = skimage.morphology.binary_erosion(
            skimage.morphology.binary_dilation(
                skimage.morphology.binary_dilation(mask, r0),
                r90
            ),
            diamond
        )
        return skimage.measure.label(mask)

    def _get_pixel_size(self, metadata: Optional[Dict] = None) -> float:
        """Get pixel size based on repo type"""
        if self.repo.name == "cytosmart" and metadata:
            return 1000/metadata['pixelsPerMm']
        elif self.repo.name.startswith("olympus"):
            # Get from image metadata through repo
            rwell = self.repo.get_well(self.image_set.bt_key, self.well)
            rim = rwell.img_for_tag("BF")
            return getattr(rim.info, 'pixel_size', 1.0)
        return 1.0

    def _measure_individual_cells(self, mask: np.ndarray, fused_mask: np.ndarray, 
                                pixel_size: float, metadata: Optional[Dict] = None) -> List[Dict]:
        """Measure individual cells - common logic for both repo types"""
        results = []
        regionprops = skimage.measure.regionprops(mask)
        
        for props in regionprops:
            # Get cluster ID from fused mask
            cluster_id = int(fused_mask[int(props.centroid[0]), int(props.centroid[1])])
            
            base_measurement = {
                "label": props.label,
                "area": props.area * pixel_size**2,
                "perimeter": props.perimeter * pixel_size,
                "centroid_0": props.centroid[0],
                "centroid_1": props.centroid[1],
                "circularity": (4*np.pi*props.area)/(props.perimeter**2),
                "cluster": cluster_id
            }
            
            # Add repo-specific fields
            if self.repo.name.startswith("olympus"):
                base_measurement.update({
                    "pixel_area": props.area,
                    "um_area": props.area * pixel_size**2,
                    "axis_major": props.axis_major_length,
                    "axis_minor": props.axis_minor_length,
                    "cluster_id": cluster_id
                })
            elif self.repo.name == "cytosmart" and metadata:
                base_measurement.update({
                    "image": f"{metadata['experimentId']}/{metadata['scanNumber']}"
                })
            
            results.append(base_measurement)
        
        return results

    @staticmethod
    def q25(x): return x.quantile(0.25)
    @staticmethod
    def q50(x): return x.quantile(0.5)
    @staticmethod
    def q75(x): return x.quantile(0.75)
    @staticmethod
    def ratio_big_to_all(x):
        x = x.values
        return float(x.max())/float(x.sum()) if not (pd.isna(x.max()) or pd.isna(x.sum())) else None
    @staticmethod
    def ratio_two_biggest(x):
        x = sorted(x.values, reverse=True)
        return float(x[0])/float(x[1]) if len(x) > 1 else None

    def _measure_aggregated(self, cp_df: pd.DataFrame, metadata: Dict) -> pd.DataFrame:
        """Measure aggregated cluster statistics (Cytosmart only)"""
        if cp_df.empty:
            return pd.DataFrame()
        
        agg_df = cp_df.groupby(['cluster', 'image']).agg({
            'area': ['count', 'mean', 'std', 'min', self.q25, self.q50, self.q75, 
                    'max', self.ratio_two_biggest, self.ratio_big_to_all],
            'circularity': ['mean', 'std', 'min', 'max']
        })

        # Flatten column names
        agg_df_area = agg_df['area']
        agg_df_circ = agg_df['circularity']
        agg_df_area.columns = [f"{cname}_area" for cname in agg_df_area.columns]
        agg_df_circ.columns = [f"{cname}_circ" for cname in agg_df_circ.columns]
        
        agg_df = pd.merge(agg_df_area, agg_df_circ, left_index=True, right_index=True)
        agg_df = agg_df.reset_index()
        
        # Add metadata columns directly
        agg_df['experiment_id'] = metadata['experimentId']
        agg_df['scan_number'] = metadata['scanNumber']
        agg_df['experiment_name'] = metadata['experimentName']
        agg_df['plate_num_wells'] = metadata['numberOfWells']
        agg_df['pixels_per_mm'] = metadata['pixelsPerMm']
        
        return agg_df

    def _process_olympus(self) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """Process Olympus image (individual measurements only)"""
        image = self.load_image
        preprocessed = self.preprocess_image(image, scale=2.5)
        mask = self.segment(preprocessed)
        fused_mask = self.make_foreground(mask)
        
        pixel_size = self._get_pixel_size()
        measurements = self._measure_individual_cells(mask, fused_mask, pixel_size)
        
        return pd.DataFrame(measurements), pd.DataFrame()

    def _process_cytosmart(self, metadata: Dict) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """Process Cytosmart image (individual + aggregated)"""
        image = self.load_image
        mask = self.segment(image)
        fused_mask = self.make_foreground(mask)
        
        pixel_size = self._get_pixel_size(metadata)
        measurements = self._measure_individual_cells(mask, fused_mask, pixel_size, metadata)
        
        cp_df = pd.DataFrame(measurements)
        agg_df = self._measure_aggregated(cp_df, metadata) if not cp_df.empty else pd.DataFrame()
        
        return cp_df, agg_df

    def process(self, bucket_name: Optional[str] = None, object_name: Optional[str] = None, 
                metadata: Optional[Dict] = None) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """Process image - repo-aware version"""
        if self.repo.name == "cytosmart":
            if not metadata:
                raise ValueError("Cytosmart processing requires metadata")
            return self._process_cytosmart(metadata)
        elif self.repo.name.startswith("olympus"):
            return self._process_olympus()
        else:
            raise ValueError(f"Unsupported repo type: {self.repo.name}")
