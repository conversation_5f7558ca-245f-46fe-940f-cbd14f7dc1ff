"""
Differentiation analysis utilities for automatic processing
"""
import json
import traceback
from typing import Set, Optional, List
from sqlalchemy.orm import Session
from sqlalchemy import desc,text
import pandas as pd
from datetime import datetime, timedelta

from base.logs import make_logger
from images.differentiation import Differentiation, diff_store
from images.model import ImageSet
from images.parse import cleanup_temp_files, java_vm
from images.repo import Repo
from images.pipeline import Pipeline
from plates.model import PlateMap, PlateFormat

log = make_logger(__name__)

def differentiation_controls(db: Session) -> PlateMap:
    """Create minimal controls for differentiation analysis (not actually used)"""
    format = db.get(PlateFormat, "384-well")
    pm = PlateMap(
        format_name=format.name,
        name="Differentiation",
        description="Differentiation analysis (no controls needed)",
        type="analysis"
    )
    pm.map_groups = []  # Empty - differentiation doesn't use controls
    return pm

def create_differentiation_instance(db: Session, image_set: ImageSet, well: str) -> Differentiation:
    """Factory function to create properly configured Differentiation instances"""
    controls = differentiation_controls(db)
    pipeline = Pipeline(Differentiation, image_set, controls)
    
    # Find the specific well instance from pipeline.wells
    for well_instance in pipeline.wells:
        if well_instance.well == well:
            return well_instance
    
    # If not found, create manually (shouldn't happen)
    return Differentiation(pipeline, image_set, well)

def create_differentiation_instance_from_repo(db: Session, repo_image_set, well: str) -> Differentiation:
    """Factory function to create properly configured Differentiation instances from repo ImageSet"""
    controls = differentiation_controls(db)
    pipeline = Pipeline(Differentiation, repo_image_set, controls)
    
    # Find the specific well instance from pipeline.wells
    for well_instance in pipeline.wells:
        if well_instance.well == well:
            return well_instance
    
    # If not found, create manually (shouldn't happen)
    return Differentiation(pipeline, repo_image_set, well)

def run_differentiation(db: Session, image_set: ImageSet, yield_progress=False):
    repo = Repo("olympus")
    print("RUN_DIFFERENTIATION WAS CALLED")
    print("RUN_DIFFERENTIATION WAS CALLED")
    print("RUN_DIFFERENTIATION WAS CALLED")
    print("RUN_DIFFERENTIATION WAS CALLED")
    print("RUN_DIFFERENTIATION WAS CALLED")
    print("RUN_DIFFERENTIATION WAS CALLED")
    print("RUN_DIFFERENTIATION WAS CALLED")
    print("RUN_DIFFERENTIATION WAS CALLED")
    print("RUN_DIFFERENTIATION WAS CALLED")
    print("RUN_DIFFERENTIATION WAS CALLED")
    print("RUN_DIFFERENTIATION WAS CALLED")
    print("RUN_DIFFERENTIATION WAS CALLED")
    plate_name = image_set.plate.name
    day = image_set.day
    log(f"Processing plate: {plate_name}, day: {day}")
    store = diff_store

    plate_dir = f"{plate_name}/Day{day}/{image_set.id}"
    results = []

    # Checking for state.json
    state_path = f"{plate_dir}/state.json"
    processed_wells = set()
    if store.exists(state_path):
        try:
            with store.open(state_path, "r") as f:
                state_data = json.load(f)
                processed_wells = {item.get("well") for item in state_data.get("results", [])}
                log(f"Resuming from state file. Already processed: {processed_wells}")
        except json.JSONDecodeError as e:
            log(f"Error loading state file (corrupted JSON): {e}")
            log(f"Deleting corrupted file and starting fresh")
            # Delete the corrupted file
            store.delete(state_path)
            processed_wells = set()
            results = []
        except Exception as e:
            log(f"Error loading state file: {e}")
            processed_wells = set()
            results = []

    try:
        repo_ims = repo.image_sets.bt_key_map.get(image_set.bt_key)
        if not repo_ims:
            log(f"No repo image set found for bt_key: {image_set.bt_key}")
            if yield_progress:
                yield {"type": "error", "error": f"No repo image set found for bt_key: {image_set.bt_key}"}
                yield {"type": "finished", "total": 0}
            return None
            
        total_wells = len(repo_ims.wells)
        processed_count = len(processed_wells)

        if yield_progress:
            yield {
                "type": "start", 
                "total": total_wells,
                "processed": processed_count,
                "plate": plate_name,
                "day": day
            }

        with java_vm():
            log(f"Found image set: {plate_name} day {image_set.day} with {total_wells} wells")

            for i, well_name in enumerate(repo_ims.wells.keys()):
                if well_name in processed_wells:
                    log(f"Skipping already processed well {well_name}")
                    if yield_progress:
                        yield {
                            "type": "skip",
                            "well": well_name,
                            "progress": round(100 * (processed_count + i) / total_wells)
                        }
                    continue
                
                try:
                    if yield_progress:
                        yield {
                            "type": "processing",
                            "well": well_name,
                            "progress": round(100 * (processed_count + i) / total_wells)
                        }

                    diff = create_differentiation_instance(db, image_set, well_name)
                    result = diff.measure()

                    diff.raw_channels_jpeg      # Raw channel composite
                    diff.visualization_jpeg     # Code mask
                    diff.nuclei_mask_jpeg      # Binary nuclei
                    diff.nuclei_intensity_jpeg # Intensity nuclei  
                    diff.lipid_mask_jpeg       # Binary lipid
                    diff.lipid_intensity_jpeg  # Intensity lipid

                    log(f"Pre-generated 6 JPEG versions for {well_name}")

                    # Ensure percentage is calculated
                    if 'Percent Positive' not in result and result.get('Total', 0) > 0:
                        result['Percent Positive'] = (result['Positive'] / result['Total']) * 100
                    
                    results.append(result)
                    log(f"Well {well_name}: {result['Positive']} positive, {result['Negative']} negative, {result.get('Percent Positive', 0):.1f}%")
                    
                    # Update state after each well
                    with store.open(state_path, "w") as f:
                        state_data = {"results": results}
                        json.dump(state_data, f, indent=4)
                        
                    if yield_progress:
                        yield {
                            "type": "complete",
                            "well": well_name,
                            "data": result,
                            "progress": round(100 * (processed_count + i + 1) / total_wells)
                        }

                except Exception as e:
                    log(f"Error processing well {well_name}: {e}")
                    traceback.print_exc()
                    if yield_progress:
                        yield {
                            "type": "error",
                            "well": well_name,
                            "error": str(e),
                            "progress": round(100 * (processed_count + i + 1) / total_wells)
                        }

    except Exception as e:
        log(f"Error during processing: {e}")
        traceback.print_exc()
        if yield_progress:
            yield {"type": "error", "error": str(e)}
    finally:
        # Save final state
        if results:
            with store.open(state_path, "w") as f:
                state_data = {"results": results}
                json.dump(state_data, f, indent=4)
        cleanup_temp_files()

    if results:
        df = pd.DataFrame(results)
        output_file = f"{plate_name}_day{day}_differentiation.csv"
        output_path = f"{plate_dir}/{output_file}"
        with store.open(output_path, "w") as f:
            df.to_csv(f)
        log(f"Results saved to {output_file}")
        if yield_progress:
            yield {"type": "finished", "total": len(results)}
        return df
    else:
        log("No results generated")
        if yield_progress:
            yield {"type": "finished", "total": 0}
        return None

def _get_cache_dir(image_set: ImageSet) -> str:
    """Get standardized cache directory path for image set"""
    return f"{image_set.plate.name}/Day{image_set.day}/{image_set.id}"

def differentiation_results_exist(image_set: ImageSet) -> bool:
    """Check if differentiation results already exist for this image set"""
    try:
        store = diff_store
        plate_dir = _get_cache_dir(image_set)
        output_file = f"{image_set.plate.name}_day{image_set.day}_differentiation.csv"
        output_path = f"{plate_dir}/{output_file}"
        
        return store.exists(output_path)
    except Exception as e:
        log(f"Error checking if differentiation results exist for image set {image_set.id}: {e}")
        return False

def get_differentiation_error_count(image_set: ImageSet) -> int:
    """Get the number of failed attempts for this image set"""
    try:
        store = diff_store
        plate_dir = f"{image_set.plate.name}/Day{image_set.day}"
        error_file = f"{plate_dir}/error_count.json"
        
        if store.exists(error_file):
            with store.open(error_file, "r") as f:
                data = json.load(f)
                return data.get("error_count", 0)
        return 0
    except Exception as e:
        log(f"Error getting error count for image set {image_set.id}: {e}")
        return 0

def get_pending_imagesets(db: Session, limit: int = 50) -> List[ImageSet]:
    """Get list of imagesets that need differentiation analysis"""
    # Simple query for olympus FL imagesets without existing results
    imagesets = db.query(ImageSet).filter(
        ImageSet.device == "olympus",
        ImageSet.lighting == "FL"
    ).order_by(ImageSet.created.desc()).limit(limit * 3).all()  # Get more to account for filtering

    # Initialize repo once
    repo = Repo("olympus")

    pending = []
    for ims in imagesets:
        # Skip if results already exist
        if differentiation_results_exist(ims):
            continue

        # Skip if no repo data available
        repo_ims = repo.image_sets.bt_key_map.get(ims.bt_key)
        if not repo_ims:
            log(f"Skipping ImageSet {ims.id} - no repo data for bt_key: {ims.bt_key}")
            continue

        log(f"Found pending imageset: {ims.id} - {ims.plate.name} day {ims.day}")
        pending.append(ims)

        # Stop when we have enough
        if len(pending) >= limit:
            break

    log(f"Found {len(pending)} pending imagesets out of {len(imagesets)} total")
    return pending

def has_required_channels(db: Session, image_set: ImageSet) -> bool:
    """Check if image set has required channels for differentiation analysis"""
    try:
        ims_query = db.execute(
            text(
                (
                    "SELECT distinct(image_channel.channel_name) as channel "
                    + " FROM image_channel JOIN image ON image_channel.image_id"
                    + " = image.id WHERE image.image_set = :ims"
                )
            ),
            {"ims": image_set.id}
        )
        
        channels = set()
        for row in ims_query:
            channels.add(row[0])
        
        has_dapi = "DAPI" in channels
        has_lipid = "Cy5" in channels or "TRITC" in channels
        
        log(f"Image set {image_set.id} channels: {channels}, DAPI: {has_dapi}, Lipid: {has_lipid}")
        
        return has_dapi and has_lipid
        
    except Exception as e:
        log(f"Error checking channels for image set {image_set.id}: {e}")
        return False

def run_cache_warming_simple(db: Session, max_items: int = 20) -> int:
    """Simplified cache warming - just process pending imagesets in order"""
    log("Starting simplified differentiation cache warming")
    
    pending = get_pending_imagesets(db, limit=max_items)
    if not pending:
        log("No pending imagesets found")
        return 0
    
    processed = 0
    for ims in pending:
        try:
            log(f"Processing imageset {ims.id}: {ims.plate.name} day {ims.day}")
            result = run_differentiation(db, ims, yield_progress=False)
            
            if result is not None:
                processed += 1
                log(f"Successfully processed imageset {ims.id}")
            else:
                log(f"No results generated for imageset {ims.id}")
                
        except Exception as e:
            log(f"Error processing imageset {ims.id}: {e}")
            continue
    
    log(f"Cache warming completed: {processed}/{len(pending)} imagesets processed")
    return processed

def should_run_differentiation(image_set: ImageSet) -> bool:
    """Check if differentiation analysis should be run for this image set"""
    return image_set.device == "olympus" and image_set.lighting == "FL"