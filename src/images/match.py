from typing import NamedTuple, List
from datetime import datetime, timezone
from collections import Counter
from sqlalchemy.exc import NoResultFound
from sqlalchemy.orm.session import Session

from base.util import tform, lform
from base.database import transact, flush
from plates.model import Plate, WellPos
from images.model import ImageSet, Image, Channels
from images.repo import ImageRepo, all_repos, ImageSet as RepoImageSet
from qc.data import parse_plate_no, plate_days

from workflow.orm.rar import Action, Request, Result
from workflow.orm.steps import SeedIn, ImageIn, UploadIn, UploadOut
from base.logs import make_logger
log = make_logger(__name__)

#
#  match image files in the repository with ImageSets in database
#
class ImageAction(NamedTuple):
    plate: Plate
    day: int
    action: Action
    result: Result
    upload: Result

class ImageSetRow(NamedTuple):
    plate: Plate
    day: int
    repo: str
    lighting: str
    wells: int
    images: int
    channels: str
    created: datetime


class ImageMatcher:
    def __init__(self, db, plate: Plate, repos: List[ImageRepo] = all_repos):
        self.db = db
        self.plate = plate
        self.repos = repos
        
        # Load the repos
        for repo in repos:
            repo.image_sets
            
        # find the seeding action. Need this to compute the `day` the image was taken.
        src = "?"
        try:
            _, sreq, sact, sres = self.db.query(SeedIn, Request, Action, Result).\
                filter(SeedIn.plate == plate.id).\
                filter(Request.id == SeedIn.request).\
                filter(Action.id == Request.action).\
                filter(Result.id == Request.action).\
                one()
            self.seed_time = sres.ended
            self.seed_result = sres
            src = "seed action"
        except NoResultFound as x:
            #log(f"  No seeding action for {plate}, looking for seed request...")
            try:
                _, sreq = self.db.query(SeedIn, Request).\
                    filter(SeedIn.plate == plate.id).\
                    filter(Request.id == SeedIn.request).\
                    one()
                self.seed_time = sreq.created
                self.seed_request = sreq
                src = "seed request"
            except NoResultFound as x:
                log(f"    No seeding request for {plate}, either, using plate creation time.")
                if not plate.created:
                    raise ValueError(f"Plate {plate.id}:{plate.name} has no creation time.")
                self.seed_time = plate.created
                src = "plate creation"
        self.seed_time = self.seed_time.replace(tzinfo=timezone.utc)
        log(f"Plate {plate.name}, seed time: {tform(self.seed_time)} from {src}")
        
        # find all completed imaging actions for this plate
        self.iacts = self.db.query(ImageIn, Request, Action, Result).\
            filter(ImageIn.plate == plate.id).\
            filter(Request.id == ImageIn.request).\
            filter(Action.id == Request.action).\
            filter(Result.id == Request.action).\
            filter(Result.result == 'pass').\
            all()
        log(f"  Found {len(self.iacts)} imaging results for {plate}")
        
        self.out_map = {}
        self.actions = []
        for i, (iin, ireq, iact, ires) in enumerate(self.iacts):
            # See if there is an upload result for this image
            ureq = self.db.query(UploadIn, Request).\
                filter(UploadIn.image_req == ireq.id).\
                filter(Request.id == UploadIn.request).\
                one_or_none()
            uact = self.db.query(Action).\
                filter(Action.id == ureq[1].action).\
                one_or_none()
            ures = self.db.query(Result).\
                filter(Result.id == ureq[1].action).\
                one_or_none()
            db_day = "          "
            if ureq:
                uout = self.db.query(UploadOut, ImageSet).\
                    filter(UploadOut.request == ureq[1].id).\
                    filter(ImageSet.id == UploadOut.image_set).\
                one_or_none()
                if uout: 
                    self.out_map[ires.id] = uout
                    db_day = f" {uout[1].day:2d} {uout[1].lighting}{uout[1].device[:1]}{len(uout[1].images):3d}"
            s = "*" if ures else "+" if uact else "-" if ureq else " "
            day = plate_days(ires.ended, self.seed_time)
            s2 = f"{ires.comment}"
            ml = 96
            if len(s2) > ml: s2 = f"{ires.comment[:ml]}..."
            log(f"    {day:2d}{s}{db_day}: {ireq.id} {iin.device[:3]} {iact.station[:3]} {s2}")
            # if ures:
            #     log(f"        [{ures.id}] {ures.comment}")
            self.actions.append(ImageAction(plate, day, iact, ires, ures))
        
        # find all image sets in the database for this plate and flag them for the repo lists
        self.imss = self.db.query(ImageSet).filter(ImageSet.plate_id == plate.id).all()
        self.db_map = {}
        self.untracked = []
        for ims in self.imss:
            self.db_map[ims.bt_key] = ims
            # Find any untracked image sets
            uouts = self.db.query(UploadOut).filter(ims.id == UploadOut.image_set).all()
            if len(uouts) > 1:
                log.warning("MULTIPLE UOUT:")
                for uout in uouts:
                    log(f"  {uout.request} {uout.image_set}")
                # Find all of them
                map = {}
                c = 0
                for uout in self.db.query(UploadOut).all():
                    if uout.image_set not in map:
                        map[uout.image_set] = []
                    else:
                        c += 1
                    map[uout.image_set].append(uout.request)
                log(f"There are {c} such duplicates:")
                for ims, reqs in map.items():
                    if len(reqs)>1:
                        iset = db.get(ImageSet, ims)
                        log(f"  {ims} {iset.plate.name}: {lform(reqs)}")
            if len(uouts)==0:
                self.untracked.append(ims)            

        # find all image sets in the repositories for this plate
        self.repo_map = {}
        self.repo_pims = {}
        self.repo_imss = []
        self.repo_missing = []
        for repo in self.repos:
            pims = repo.plate(plate.name)
            self.repo_pims[repo.name] = pims
            log(f"  Found {len(pims.image_sets)} image sets in {repo}")
            # Load the info ahead of time
            for ims in pims.image_sets:
                ims.info
            for ims in pims.image_sets:
                s = " "
                image_count = 0
                if ims.bt_key in self.db_map:
                    s = "*"
                    image_count = len(self.db_map[ims.bt_key].images)
                chs = ",".join(ims.channels())
                day_min = ((ims.created_min - self.seed_time).total_seconds() / 86400) - ims.day
                day_max = ((ims.created_max - self.seed_time).total_seconds() / 86400) - ims.day
                log(f"    {ims.day:2d}{s} : {ims.lighting} {len(ims.wells):3d} {ims.bt_key} {tform(ims.created_min)} {day_min:4.2f} {day_max:4.2f} {chs}")
                self.repo_map[ims.bt_key] = ims
                rims = ImageSetRow(
                    plate, ims.day, repo.name, ims.lighting, 
                    len(ims.wells.keys()), image_count,
                    chs, ims.created_min
                )
                self.repo_imss.append(rims)
                if ims.bt_key not in self.db_map:
                    self.repo_missing.append(ims)
                                    
                
        # Now list all image sets in the database for this plate
        log(f"  Found {len(self.imss)} image sets for {plate}")
        for ims in self.imss:
            day = plate_days(ims.created, self.seed_time)
            log(f"    {day:2d}{'*' if ims.bt_key in self.repo_map else ' '} : {ims} (day={ims.day})")
    

    def find_match(self, repo: str, iact: Action, ires: Result):
        """Find a matching image set in the named repository for the given imaging action"""
        
        # Get the repository entries for this plate
        pims = self.repo_pims[repo]
        #pims.assign_seed_time(self.seed_time)

        # first, check if the image set is already in the database
        if ires.id in self.out_map:
            uout, db_ims = self.out_map[ires.id]
            #db_ims = self.db.query(ImageSet).get(uout.image_set)
            log(f"Found image set {db_ims} in database")
            ims = self.repo_map.get(db_ims.bt_key)
            if ims:
                if (ims.repo.name != repo):
                    raise ValueError(f"Image set {ims} in {ims.repo.name} does not match {repo}")
                return ims        
        plate_no = parse_plate_no(pims.plate_names[0])
        day = plate_days(ires.ended, self.seed_time)
        log(f"Find GCS file set by dates from {plate_no}-{day}:")
        tb = iact.started.replace(tzinfo=timezone.utc)
        te = ires.ended.replace(tzinfo=timezone.utc)
        log(f"  Wf range: {tform(tb)} - {tform(te)}")
        log(f"  Seed time: {tform(self.seed_time)}")
        exact = None
        close = None
        special = None
        min_dt = None
        idays = []
        for ims in pims.image_sets:
            s = "*" if ims.bt_key in self.db_map else ""
            idays.append(f"{ims.day}{s}")
            db_ims = self.db_map.get(ims.bt_key)
            if db_ims: 
                uout = self.db.query(UploadOut).\
                    filter(db_ims.id == UploadOut.image_set).\
                one_or_none()
                if uout: continue
            if ims.day == day: 
                exact = ims
            # if ims.day == day-1:
            #     if plate_no in range(186,191) and day in [15,26,47]:
            #         special = ims
            #     if plate_no in range(192,198) and day in [13]:
            #         special = ims
            # dt = max(timedelta(0), max(ims.created_min - te, tb - ims.created_max))
            # log(f"  {ims.day} {ims.bt_key} {ims.bt_key in self.db_map}   {tform(ims.created_min)} - {tform(ims.created_max)}")
            # log(f"    {dt}   ({ims.created_min - te}, {tb - ims.created_max})")
            dt = abs(ims.day-day)
            if not min_dt or dt < min_dt:
                min_dt = dt
                close = ims
        if exact: ims = exact
        elif special: ims = special
        elif min_dt and min_dt < 2: ims = close
        else: ims = None
        idays = ",".join(idays)
        
        log(f"Found image set {ims}, exact: {exact}, special: {special}, close: {close} ({min_dt})")
        if not ims:
            log(f"  Closest: {min_dt} {close}")
            raise ValueError(f"No image set found for Plate {plate_no}, day {day} in {repo} {idays}")
        
        # Check the wells
        log(f"Check {len(ims.wells)} wells for {ims}")
        counter = Counter([w.ch_str() for w in ims.wells.values()])
        for s, n in counter.items():
            log(f"  {s}: {n}")
        # for wn, well in ims.wells.items():
        #     log(f"  {wn} {well}")
        #     well.list_images()
        #     pass            
        return ims
        
    def insert_image_set(self, ims: RepoImageSet):
        """Insert an image set into the database"""
        # Enumerate the wells and access the images
        wps = self.db.query(WellPos).filter(WellPos.plate_format==self.plate.format).all()
        existing = self.db.query(ImageSet).filter(ImageSet.bt_key==ims.bt_key).one_or_none()
        s = "Checking" if existing else "Inserting"
        log(f"{s} image set {ims}")
        imgs = []
        x_max = 0
        y_max = 0
        missing = []
        full = True
        created = datetime.min.replace(tzinfo=timezone.utc)
        for wp in wps:
            wn = wp.well_name
            well = ims.wells.get(wn)
            if not well:
                missing.append(wn)
                continue
            #fn = well.images["jpg"]["bf"]
            for itag in well.img_tags():
                wim = well.img_for_tag(itag)
                info = None
                if full or wn in ["A1", "B2", "C3", "D4", "E5", "F6", "G7", "H8"]:
                    # Get image info for inserting or checking
                    info = wim.info
                    # log(f"CREATED: {type(created)} {created} - {type(info.created)} {info.created}")
                    created = max(created, info.created)
                    x_max = max(x_max, info.width)
                    y_max = max(y_max, info.height)
                    # log(f"    {wn}-{itag} {info} - {wim.fn}")
                img = None
                # Get the channels

                def getch(ch: str):
                    channel = self.db.get(Channels, ch)
                    if channel is None:
                        all = self.db.query(Channels).all()
                        raise ValueError(f"Channel {ch} not found in database: {[c.name for c in all]}")
                    return channel
                if itag=='raw':
                    chs = [getch(c) for c in info.channels]
                else:
                    chs = [getch(itag)]
                    
                checked = False
                if existing:
                    # Check existing images
                    xims = self.db.query(Image).\
                        filter(Image.image_set==existing.id).\
                        filter(Image.well_pos==wp.id).\
                        all()
                    for im in xims:
                        chs1 = [c.name for c in chs]
                        chs2 = [c.name for c in im.channels]
                        log(f"  {im.size_x}x{im.size_y} {chs} {im.object_uri}")
                        if chs1 != chs2:
                            raise ValueError(f"Channel mismatch for {wn} in existing set: {chs1} vs {chs2}")
                        x, y = (info.width, info.height) if full else (0, 0)
                        if info is not None and (im.size_x != x or im.size_y != y):
                            raise ValueError(
                                f"Size mismatch for {wn} in existing set: {im.size_x}x{im.size_y} vs {info.x}x{info.y}")
                        if im.object_uri != wim.fn:
                            raise ValueError(f"URI mismatch for {wn} in existing set: {im.object_uri} vs {wim.fn}")
                        img = im
                        checked = True
                    # if not checked:
                    #     log(f"No image found for channel {wn}-{itag} in existing set")              
                    # log(f"    {wn}-{itag} checked")

                # No image exists in db, create a new one
                if not checked:
                    # Create new image
                    img = Image(
                        object_uri = wim.fn,
                        well_pos = wp.id,
                        size_x = info.width if full else 0,
                        size_y = info.height if full else 0,
                        created = info.created,
                        version = 1,
                    )
                    for c in chs:
                        img.channels.append(c)
                    log(f"    {wn}-{','.join([c.name for c in chs])} created for {ims}")
                imgs.append(img)
        if missing:
            log(f"  Missing: {lform(missing)}")
            
        # Insert the image set if not found
        msg = f"Image set {ims}"
        if not existing or len(imgs) > 0:
            with transact(self.db):
                if not existing:
                    # Create new image set
                    existing = ImageSet(
                        plate_id = self.plate.id,
                        day = ims.day,
                        created = created,
                        device = ims.repo.name,
                        lighting = ims.lighting,
                        pix_per_mm = 742,
                        size_x = x_max,
                        size_y = y_max,
                        bt_key = ims.bt_key
                    )
                    log(f"  Inserting {existing}")
                    flush(self.db, existing)
                    msg = msg + " added"
                for img in imgs:
                    img.image_set = existing.id
                    self.db.add(img)
                msg = msg + f", added {len(imgs)} images"
        else:
            msg = msg + " exists"
        msg += f" as {existing.bt_key} with {len(imgs)} wells."
        log(msg)
        return (existing, "pass", msg)

    def db_images(plate: Plate):
        """Find and list all image sets in the database for a plate"""
        
        
