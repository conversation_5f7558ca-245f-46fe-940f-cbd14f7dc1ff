from datetime import datetime
from typing import Callable
from fastapi import APIRouter, Request,  Depends, Query
from imageio.v2 import imread, imwrite
from io import BytesIO
import numpy as np
from fastapi.responses import HTMLResponse, RedirectResponse, StreamingResponse
from sqlalchemy import text
import pandas as pd
import json
from app.html import html

from base.logs import make_logger
from base.store import cache_store
from base.reps import Rep, rep, JointRep
from app.table import table

from images.model import Device, ImageSet, ImagePipeline, PipelineGoal, Channels, PipelineVersion, PipelineRequest, Image
from images.match import ImageMatcher, ImageAction, ImageSetRow
from images.repo import Repo
from images.differentiation import Differentiation, DifferentiationPlateMap, diff_store
from images.differentiation_utils import run_differentiation, _get_cache_dir, create_differentiation_instance
from plates.model import Plate, WellPos
from plates.render import plate_map
from workflow.orm.steps import SeedIn
from app.cruds import crud
from app.layouts import Context
from app.auth import context
from app.store import cache_server
from images.differentiation_utils import get_differentiation_error_count

log = make_logger(__name__)

router = APIRouter(prefix="/imaging")

img_cache = cache_store()

# Some reps
plate_rep = rep(Plate)
iact_sel = {
    "plate": "plate.name",
    "day": "day",
    "action": "action.id",
    "operator": "action.operator",
    "station": "action.station",
    "completed": "result.ended",
    "result": "result.result",
    "comment": "result.comment",
}
iact_sel_all = iact_sel | {
    "upload_comment": "upload.comment"
}
iset_rep = rep(ImageSet) - "bt_key"
iact_rep = JointRep.from_class(ImageAction)
iact_rep_all = iact_rep(iact_sel_all)
iact_rep_sel = iact_rep(iact_sel)
repo_rep = rep(ImageSetRow)

# Some cruds
cruds = [
    crud("devices", Device, router),
    crud("imagesets", ImageSet, router),
    #crud("pipeline", ImagePipeline, router),
    #crud("pipelinegoals", PipelineGoal, router),
]

def ims_repo(ims: ImageSet):
    name = ims.device
    return Repo(name)

@router.get("/plates", response_class=HTMLResponse)
async def plate_list(ht: Context = Depends(context)):
    # subq = ht.db.query(SeedIn).filter(SeedIn.plate == Plate.id).exists()
    subq = ht.db.query(ImageSet).filter(ImageSet.plate_id == Plate.id).exists()
    plates = ht.db.query(Plate).filter(subq).all()
    ret = ht.page("Imaging Home",
        ht.h2("Image processing reports"),
        ht.h3("Pick one or more plates from this list of plates:"),
        table(plates, plate_rep)(id="plates", href=ht.url("imaging/plate")),
    )
    return ret.pretty()

@router.get("/plate/{plate_id}", response_class=HTMLResponse)
async def plate_isets(plate_id: int, ht: Context = Depends(context)):
    log(f"Selected plate: {plate_id}")
    plate = ht.db.get(Plate, plate_id)
    matcher = ImageMatcher(ht.db, plate, repos=[Repo("cytosmart"), Repo("olympus")])
    not_uploaded = [ia for ia in matcher.actions if ia.upload is None]
    
    def ims_list(id: str, rep: Rep, phrase: str, imss):
        if len(imss) >0:
            return ht.h3(f"Image sets {phrase}:") + \
            table(imss, rep)(id=id, href=ht.url("imaging/iset"))
        else:
            return None
        
    ret = ht.page(f"Plate {plate.name}",
        ht.h2(f"Imaging summary for {plate.name}"),
        ims_list("isets", iset_rep, "linked to the plate", matcher.imss),
        # ht.h3("Image sets linked to the plate:"),
        #     table(matcher.imss, iset_rep)(id="isets", href=ht.url("imaging/iset")),
        ims_list("iacts", iact_rep_sel, "that are not yet uploaded", not_uploaded),
        # ht.h3("Image sets that are not yet uploaded:"),
        #     table(not_uploaded, iact_rep_sel)(id="iacts"),
        ims_list("untracked", iset_rep, "that have not been tracked in the workflow", matcher.untracked),
        ht.br, 
        ht.h2("Further details:"), 
        ht.br,
        ht.h3("All tracked imaging operations:"),
            table(matcher.actions, iact_rep_all)(id="iacts"),
        ht.h3(f"All image sets in the repositories ({len(matcher.repo_imss)}):"),
            table(matcher.repo_imss, repo_rep)(id="repo_isets"),
    )
    return ret.pretty()

@router.get("/iset/{iset_id}", response_class=HTMLResponse)
async def iset_images(iset_id: int, ht: Context = Depends(context)):
    ims = ht.db.get(ImageSet, iset_id)
    repo = ims_repo(ims)
    plate = ims.plate
    t = f"Image set {iset_id} for {plate.name} day {ims.day}. Device: {ims.device}, lighting: {ims.lighting}"
    log(t)
    plate = ims.plate
    log(f"Selected image set: {iset_id} -> {plate} {ims.day}")
    wells = ht.db.query(WellPos, Image).\
        filter(Image.image_set==ims.id).\
        filter(WellPos.id==Image.well_pos).\
        all()
    well_map = {wp.id: im for wp, im in wells}
    
    def well(wp: WellPos):
        """Function that creates the html for a single well."""
        link = False
        if wp.id in well_map:
            im = well_map[wp.id]
            t = f"{wp.well_name} {im.object_uri}"
            rwell = repo.get_well(ims.bt_key, wp.well_name)
            rim = rwell.default_image()
            v = cache_server.thumb(ht, rim.cfn, style="width: 90%;") or ""
#            v = ht.img(src=f"/store/cache/bin/{rim.cfn}-thumb.jpg", style="width: 90%;")()
#            v = ht.a(href=f"/imaging/image/{im.id}")(v)
            link = True
        else:
            t = f"{wp.well_name}, Unmapped"
            v = ""
        # Create and return the html node for the well
        n = ht.div(v)
        if link:
            n = ht.a(href=f"/imaging/image/{im.id}")(n)
        return n(title=t)

    ret = ht.page(t, ht.h3(t),
        ht.p("Click on a well to see the image:"),
        plate_map(ht, plate.format, well),
        ht.when(not plate.name.startswith("PD"))(
            ht.link(f"qc/iset/{iset_id}")("Total lipid size data")
        ),
        ht.br(),
        # Add differentiation analysis link for applicable image sets
        ht.when(ims.device == "olympus" and ims.lighting == "FL")(
            ht.link(f"imaging/iset/{iset_id}/differentiation")("Differentiation Analysis")
        )
    )
    return ret.pretty()


@router.get("/image/{img_id}", response_class=HTMLResponse)
async def view_image(img_id: int, ht: Context = Depends(context)):
    log(f"Selected image: {img_id}, user: {ht.user.name}")
    img = ht.db.get(Image, img_id)
    ims = ht.db.get(ImageSet, img.image_set)
    plate = ims.plate
    wp = ht.db.get(WellPos, img.well_pos)
    repo = ims_repo(ims)
    well = repo.get_well(ims.bt_key, wp.well_name)
    t = f"Image {img_id} for {plate.name} day {ims.day} well {wp.well_name}"
    log(t)
    ihs = []
    
    for k, im in well.images.items():
        im.encode(img_cache)
        ihs.append(
            ht.div(
                ht.when(ht.admin)(
                    ht.p(f"Image {k}, URI: {img.object_uri}"),
                    ht.p(f"Image URL: /imaging/image/{repo.name}/{im.fn}"),
                    ht.p(f"JPG URL: /img/{im.cfn}.jpg"),
                    ht.a(href=f"{im.signed_url()}")("See the image, from cloud"),
                    ht.a(href=f"/store/cache/bin/{im.cfn}.jpg")("See the image, streamed"),
                ),
                ht.p(f"{repo.name} {k}   {im.info}"),
                ht.img(src=f"/store/cache/bin/{im.cfn}.jpg", style="width: 100%;")(),
                #ht.img(src=f"/static/cache/{im.cfn}.jpg", style="width: 100%;")(),
            )
        )
    log(f"Found {len(ihs)} images")
    return ht.page(t, ht.h3(t), *ihs).pretty()

@router.get("/image/{repo_name}/{fn:path}", name="path-convertor", response_class=StreamingResponse)
async def serve_image(repo_name: str, fn: str, ht: Context = Depends(context)):
    """Serve an image file from a repository."""
    log(f"Streaming {fn} from {repo_name}")
    repo = Repo(repo_name)
    fobj = repo.storage.open(fn, "rb")
    def stream():
        with fobj as f:
            while chunk := f.read(1048): yield chunk
    return StreamingResponse(stream(), 
        media_type="application/octet-stream", 
        #headers={'Content-Disposition': 'attachment; filename=assay_'+fn.replace("/", "_")}
    )

@router.get("/iset/{iset_id}/differentiation/progress", response_class=StreamingResponse)
async def differentiation_progress(iset_id: int, ht: Context = Depends(context)):
    """Stream progress updates for differentiation analysis"""
    ims = ht.db.get(ImageSet, iset_id)
    if not ims:
        return HTMLResponse("Image set not found", status_code=404)
    
    async def generate_events():
        try:
            for update in run_differentiation(ht.db, ims, yield_progress=True):
                yield f"data: {json.dumps(update)}\n\n"
        except Exception as e:
            log(f"Error in differentiation progress stream: {e}")
            yield f"data: {json.dumps({'type': 'error', 'error': str(e)})}\n\n"
    
    return StreamingResponse(
        generate_events(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
        }
    )

@router.get("/iset/{iset_id}/differentiation", response_class=HTMLResponse)
async def iset_differentiation(iset_id: int, ht: Context = Depends(context)):
    """Differentiation analysis at the ImageSet level with progress tracking"""
    ims = ht.db.get(ImageSet, iset_id)
    if not ims:
        return HTMLResponse("Image set not found", status_code=404)
    
    # Check if this is an applicable image set (Olympus with fluorescent channels)
    if ims.device != "olympus" or ims.lighting != "FL":
        return ht.page(f"Differentiation Analysis Not Applicable",
            ht.h2(f"Differentiation analysis not applicable"),
            ht.p(f"This image set ({ims.device}, {ims.lighting}) does not support differentiation analysis."),
            ht.p(f"Differentiation analysis requires Olympus fluorescent images with DAPI and lipid channels."),
            ht.a(href=f"/imaging/iset/{iset_id}")("Back to image set")
        ).pretty()
    
    error_count = get_differentiation_error_count(ims)
    if error_count >= 3:
        return ht.page(f"Differentiation Analysis Failed",
            ht.h2(f"Differentiation analysis failed"),
            ht.p(f"Differentiation analysis has failed 3 times for this image set."),
            ht.p(f"Please check the logs for more details or contact support."),
            ht.a(href=f"/imaging/iset/{iset_id}")("Back to image set")
        ).pretty()
    
    # Check if results already exist
    store = diff_store
    cache_dir = _get_cache_dir(ims)
    output_file = f"{ims.plate.name}_day{ims.day}_differentiation.csv"
    output_path = f"{cache_dir}/{output_file}"
    log(f"EXISTS: {store.exists(output_path)} {store}{output_path}")
    title = f"Differentiation Analysis: {ims.plate.name} Day {ims.day}"
    if store.exists(output_path):
        try:
            with store.open(output_path, "r") as f:
                df = pd.read_csv(f, index_col=False)
            
            if 'Percent Positive' not in df.columns and 'Total' in df.columns:
                df['Percent Positive'] = (df['Positive'] / df['Total'] * 100).fillna(0)
            
            df = df.reset_index(drop=True)
            results_data = df.to_dict('records')
            
            def make_clickable_well(row):
                well = row.get('Well', '')
                return {
                    **row,
                    'Well': ht.a(href=f"/imaging/iset/{iset_id}/differentiation/well/{well}")(well),
                    'Percent Positive': f"{row.get('Percent Positive', 0):.1f}%"
                }

            clickable_results = [make_clickable_well(row) for row in results_data]
            
            diff_map = DifferentiationPlateMap(ims.plate.name, ims.plate.format, ims.day, df)

            return ht.page(title,
                ht.h1(f"Differentiation Analysis Report"),
                ht.h2(f"Plate {ims.plate.name}, Day {ims.day}"),
                ht.p("Analysis based on binary nuclei and lipid mask overlap detection", style="font-style: italic; color: #666;"),
                
                ht.div(style="display: flex; gap: 20px;")(
                    ht.div(style="flex: 1;")(
                        ht.h3("Results"),
                        ht.p("Click on a well to view detailed images and analysis"),
                        table(clickable_results),
                        ht.div(
                            ht.h3("Data Download"),
                            ht.p(f"Download differentiation data: ") +
                            ht.a(href=f"/imaging/differentiation/download/{ims.plate.name}/{ims.day}/{iset_id}/{output_file}")(
                                f"{output_file}"
                            )
                        ),
                    ),
                    ht.div(style="flex: 0 0 400px;")(
                        diff_map.html(ht, iset_id)
                    )
                ),
                # Navigation
                ht.br(),
                ht.div(
                    ht.a(href=f"/imaging/iset/{iset_id}")("Back to image set")
                )
            ).pretty()
        except Exception as e:
            log(f"Error loading results: {e}")
            # Fall through to progress page
    
    return ht.page(title+" - Processing",
        ht.h2(title),
        ht.div(
            ht.h3("Processing Progress"),
            ht.p("This analysis may take several minutes. You can continue browsing in another tab."),
            ht.p("Keep this tab open for processing to continue."),
            ht.div(id="progress-container", style="width:100%; background-color:#f1f1f1; margin:20px 0;")(
                ht.div(id="progress-bar", style="width:0%; height:30px; background-color:#4CAF50; text-align:center; line-height:30px; color:white;")("0%")
            ),
            ht.div(id="status")("Starting analysis..."),
            ht.div(id="wells-container")(ht.h3("Well Processing Status:")),
        ),
        ht.include_js("differentiation-progress.js"),
        ht.script()(f"initializeDifferentiationProgress({iset_id});")
    ).pretty()

@router.get("/differentiation/download/{plate_name}/{day}/{iset_id}/{filename}", response_class=StreamingResponse)
async def download_differentiation(plate_name: str, day: str, iset_id: int, filename: str, ht: Context = Depends(context)):
    """Download the differentiation data file"""
    try:
        file_path = f"{plate_name}/Day{day}/{iset_id}/{filename}"
        log(f"Download differentiation data for {file_path}")
        store = diff_store
        fobj = store.open(file_path, "rb")
    except Exception as e:
        log(f"Error: {e}")
        return ht.error(f"Error downloading file: {e}")
        
    def stream():
        with fobj as f:
            while chunk := f.read(1048): 
                yield chunk
            
    return StreamingResponse(stream(), 
        media_type="text/csv", 
        headers={'Content-Disposition': f'attachment; filename={filename}'}
    )

@router.get("/iset/{iset_id}/differentiation/well/{well}", response_class=HTMLResponse)
async def well_differentiation(
    iset_id: int, 
    well: str, 
    crop_width: int = Query(300, ge=100, le=2000),
    crop_height: int = Query(300, ge=100, le=2000),
    bounds_enabled: bool = Query(False),
    ht: Context = Depends(context)
):
    ims = ht.db.get(ImageSet, iset_id)
    if not ims:
        return HTMLResponse("Image set not found", status_code=404)

    try:
        diff = create_differentiation_instance(ht.db, ims, well)
    except Exception as e:
        return ht.page(f"Error Creating Differentiation Instance",
            ht.h2(f"Error"),
            ht.p(f"Could not create differentiation instance: {str(e)}"),
            ht.a(href=f"/imaging/iset/{iset_id}/differentiation")("Back to differentiation analysis")
        ).pretty()
    
    plate = ims.plate
    store = diff_store
    cache_dir = _get_cache_dir(ims)
    well_dir = f"{cache_dir}/{well}"
    dapi_file = f"{well_dir}/dapi.tif"
    
    # Check if results exist for this well
    log(f"EXISTS: {store.exists(dapi_file)} {store}{dapi_file}")
    if not store.exists(dapi_file):
        return ht.page(f"Well Differentiation Results Not Found",
            ht.h2(f"No differentiation results found"),
            ht.p(f"No results found for well {well} in plate {plate.name}, day {ims.day}"),
            ht.a(href=f"/imaging/iset/{iset_id}/differentiation")("Back to differentiation analysis")
        ).pretty()
    
    # Get well-specific results if available
    well_data = {}
    output_file = f"{plate.name}_day{ims.day}_differentiation.csv"
    output_path = f"{cache_dir}/{output_file}"
    if store.exists(output_path):
        try:
            with store.open(output_path, "r") as f:
                df = pd.read_csv(f, index_col=False)
                well_row = df[df['Well'] == well]
                if not well_row.empty:
                    well_data = well_row.iloc[0].to_dict()
        except Exception as e:
            log(f"Error loading well results: {e}")

    diff_percent = "N/A"
    if well_data.get('Total', 0) > 0:
        diff_percent = f"{well_data.get('Positive', 0) / well_data.get('Total', 1) * 100:.1f}%"
    
    main_image_url = f"/imaging/differentiation/image/{plate.name}/{ims.day}/{ims.id}/{well}/raw_channels.jpeg"
    
    left_side = ht.div(
        ht.h3("Click on a section to view detailed crops:"),
        ht.div(
            f'<img id="main-image" src="{main_image_url}" style="width: 600px; display: block;">',
            f'<canvas id="grid-overlay" width="600" height="400" style="position: absolute; top: 0; left: 0; pointer-events: none;"></canvas>',
            id="main-image-container",
            style="position: relative; display: inline-block; margin-bottom: 20px;"
        ),
        ht.div(
            ht.label("Crop Width: "),
            ht.input(type="number", id="cropWidth", value=str(crop_width), min="100", max="2000", style="margin-bottom: 10px; display: block;"),
            ht.label("Crop Height: "),
            ht.input(type="number", id="cropHeight", value=str(crop_height), min="100", max="2000", style="margin-bottom: 10px; display: block;"),
            ht.div(
                ht.input(type="checkbox", id="boundsEnabled", checked=bounds_enabled, style="margin-right: 5px;"),
                ht.label("Enable crop boundaries (15% margin)", **{"for": "boundsEnabled"}),
                style="margin-bottom: 10px;"
            ),
            ht.button(id="updateGrid", onclick="updateGridDimensions()")("Update Settings"),
            style="margin-top: 10px;"
        ),
        ht.div(id="crop-info", style="margin-top: 1em; font-weight: bold;"),
        style="display: inline-block; vertical-align: top; margin-right: 40px;"
    )
    
    right_side = ht.div(id="crops-container", style="display: inline-block; vertical-align: top; width: 300px;")
    main_container = ht.div(left_side,right_side,style="display: flex; align-items: flex-start; gap: 40px;")
    
    available_images = [
        ("raw_channels.jpeg", "Raw Channels Composite"),
        ("visualization_mask.jpeg", "Segmentation Overlay"),
        ("nuclei_mask.png", "Binary Nuclei Mask"),
        ("nuclei_intensity_mask.png", "16-bit Gaussian Filter Nuclei Mask"),
        ("lipid_mask.png", "Binary Lipid Mask"), 
        ("lipid_intensity_mask.png", "16-bit Gaussian Filter Lipid Mask"),
        ("nuclei_mask.tif", "Nuclei Mask (TIFF)"),
        ("lipid_mask.tif", "Lipid Mask (TIFF)"),
        ("visualization_mask.tif", "Visualization Mask (TIFF)")
    ]
    
    download_links = ht.div(
        ht.h3("Download Full-Size Images"),
        ht.div(*[
            ht.div(
                ht.span(f"{label}: "),
                ht.a(href=f"/imaging/differentiation/download/{plate.name}/{ims.day}/{well}/{filename}")("Download"),
                style="margin: 5px 0;"
            )
            for filename, label in available_images
        ]),
        style="margin-top: 30px; padding: 20px; border: 1px solid #ddd; border-radius: 5px;"
    )
    
    config = {
        "cropWidth": crop_width,
        "cropHeight": crop_height,
        "boundsEnabled": bounds_enabled,
        "imageTypes": [
            ["main_image", "visualization"],
            ["nuclei_mask.png", "nuclei_intensity_mask.png"], 
            ["lipid_mask.png", "lipid_intensity_mask.png"]
        ],
        "imageLabels": [
            ["Raw Image", "Segmentation Overlay"],
            ["Binary Nuclei Mask", " 16-bit Gaussian Filter Nuclei Mask"],
            ["Binary Lipid Mask", "16-bit Gaussian Filter Lipid Mask"]
        ],
        "plate": plate.name,
        "day": ims.day,
        "well": well,
        "isetId": iset_id
    }

    return ht.page(
        f"Differentiation Analysis: Well {well}",
        ht.h1(f"Well {well} Differentiation Analysis"),
        ht.h2(f"{plate.name}, Day {ims.day}"),
        ht.p(f"Differentiation: {diff_percent}, {well_data.get('Positive', 0)} positive, {well_data.get('Negative', 0)} negative"),
        main_container,
        download_links,  # Add download links section
        ht.include_js("differentiation-viewer.js"),
        ht.script()(f"initializeDifferentiationViewer({json.dumps(config)});"),
        ht.div(
            ht.a(href=f"/imaging/iset/{iset_id}/differentiation")("Back to differentiation analysis"),
            style="margin-top: 20px;"
        )
    ).pretty()

@router.get("/differentiation/image/{plate_name}/{day}/{iset_id}/{well}/{image}", response_class=StreamingResponse)
async def differentiation_image(plate_name: str, day: str, iset_id: int, well: str, image: str, ht: Context = Depends(context)):
    """Serve differentiation analysis images"""
    try:
        file_path = f"{plate_name}/Day{day}/{iset_id}/{well}/{image}"
        log(f"Serving differentiation image: {file_path}")
        store = diff_store
        
        if not store.exists(file_path):
            return HTMLResponse("Image not found", status_code=404)
            
        fobj = store.open(file_path, "rb")
        
        # Determine content type based on file extension
        content_type = "image/jpeg"
        if image.endswith(".png"):
            content_type = "image/png"
        elif image.endswith(".tif") or image.endswith(".tiff"):
            content_type = "image/tiff"
        
        def stream():
            with fobj as f:
                while chunk := f.read(1048): 
                    yield chunk
                    
        return StreamingResponse(
            stream(), 
            media_type=content_type
        )
    except Exception as e:
        log(f"Error serving image: {e}")
        return HTMLResponse(f"Error: {str(e)}", status_code=500)

@router.get("/differentiation/crop/{plate_name}/{day}/{well}/{image}", response_class=StreamingResponse)
async def differentiation_crop(
    plate_name: str, 
    day: str, 
    well: str, 
    image: str,
    iset_id: int = Query(...),
    x: int = Query(0, ge=0),
    y: int = Query(0, ge=0), 
    width: int = Query(256, gt=0),
    height: int = Query(256, gt=0),
    ht: Context = Depends(context),
):
    """Fast cropping using pre-generated JPEG files"""
    try:
        store = diff_store
        ims = ht.db.get(ImageSet, iset_id)
        if ims is None:
            return HTMLResponse("ImageSet not found", status_code=404)
        
        diff = create_differentiation_instance(ht.db, ims, well)
        jpeg_filename = diff.get_jpeg_path(image)
        jpeg_path = f"{plate_name}/Day{day}/{iset_id}/{well}/{jpeg_filename}"
        
        if not store.exists(jpeg_path):
            return HTMLResponse("Pre-generated image not found", status_code=404)

        with store.open(jpeg_path, "rb") as f:
            img = imread(f, format="jpeg")
        
        # Ensure coordinates are within bounds
        img_h, img_w = img.shape[:2]
        x = max(0, min(x, img_w - 1))
        y = max(0, min(y, img_h - 1))
        x2 = min(x + width, img_w)
        y2 = min(y + height, img_h)
        
        cropped = img[y:y2, x:x2]
        cropped_with_scale = diff.add_scale_bar_to_image(cropped)
        
        # Convert back to bytes
        output = BytesIO()
        imwrite(output, cropped_with_scale, format="jpeg", quality=85)
        output.seek(0)
        
        return StreamingResponse(output, media_type="image/jpeg")
        
    except Exception as e:
        log(f"Error in differentiation crop: {e}")
        return HTMLResponse("Error processing crop", status_code=500)


@router.get("/differentiation/dimensions/{plate_name}/{day}/{well}")
async def differentiation_dimensions(
    plate_name: str, 
    day: str, 
    well: str, 
    iset_id: int = Query(...),
    ht: Context = Depends(context)
):
    """Get original image dimensions and valid crop bounds"""
    ims = ht.db.get(ImageSet, iset_id)
    if ims is None:
        return {"error": "ImageSet not found"}
    
    diff = create_differentiation_instance(ht.db, ims, well)
    h, w = diff.dapi.img.shape
    bounds = diff.get_crop_bounds()
    
    return {
        "width": w, 
        "height": h,
        "crop_bounds": bounds
    }

@router.get("/differentiation/download/{plate_name}/{day}/{well}/{image}", response_class=StreamingResponse)
async def download_differentiation_image(plate_name: str, day: str, well: str, image: str, ht: Context = Depends(context)):
    """Download full-sized differentiation mask images"""
    try:
        file_path = f"{plate_name}/Day{day}/{well}/{image}"
        log(f"Download differentiation image: {file_path}")
        store = diff_store()
        
        if not store.exists(file_path):
            return HTMLResponse("Image not found", status_code=404)
            
        fobj = store.open(file_path, "rb")
        
        # Determine content type and filename
        content_type = "image/tiff"
        filename = f"{plate_name}_{day}_{well}_{image}"
        if image.endswith(".jpeg") or image.endswith(".jpg"):
            content_type = "image/jpeg"
        elif image.endswith(".png"):
            content_type = "image/png"
            
        def stream():
            with fobj as f:
                while chunk := f.read(8192):
                    yield chunk
                    
        return StreamingResponse(
            stream(), 
            media_type=content_type,
            headers={'Content-Disposition': f'attachment; filename={filename}'}
        )
        
    except Exception as e:
        log(f"Error downloading image: {e}")
        return HTMLResponse("Error downloading image", status_code=500)
