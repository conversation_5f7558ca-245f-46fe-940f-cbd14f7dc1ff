import os
import dotenv
from datetime import timedelta
dotenv.load_dotenv()

basedir = os.path.abspath(os.path.dirname(__name__))

class Config:
    SQLALCHEMY_COMMIT_ON_TEARDOWN = True
    SQLALCHEMY_TRACK_MODIFICATIONS = False

class DevelopmentConfig(Config):
    def __init__(self, local=False):
        self.DEBUG = True
        self.SESSION_COOKIE_SECURE = True
        self.PERMANENT_SESSION_LIFETIME = timedelta(days=3)
        self.SESSION_COOKIE_HTTPONLY = True
        self.ASSAYS_BUCKET = "mellicell_development_bucket" # uploads to dev bucket not to mess with production
        self.ASSAY_TOPIC_PATH = "dev_assay_created" # create topic for development purposes
        self.PROJECT_ID = "development-311316"
        self.CYTOSMART_BUCKET = "cytosmart_brightfield_images_mellicell"
        self.CYTOSMART_MASK_BUCKET = "cytosmart_masks_mellicell"
        self.REQUEST_MASK_TOPIC = "dev_mask_requested" # create topic for development purposes
        self.VERSION_2_DATASET = "mellicell_image_data_v2"
        self.VERSION_1_DATASET = "mellicell_image_data"
        self.OLYMPUS_LIPID_TABLE = "cellpose-measurments"
        self.OLYMPUS_CLUSTER_TABLE = "cluster-statistics"
        self.CYTOSMART_LIPID_TABLE = "cytosmart_lipid_measurments"
        self.CYTOSMART_CLUSTER_TABLE = "cytosmart_measurements"
        self.RESIZE_IMAGE_SERVICE_URL = "https://olympus-resize-service-gkjofhbn5a-ue.a.run.app"
        self.OLYMPUS_IMAGE_BUCKET = "mellicell_development_bucket" # uploads to dev bucket not to mess with production

class ProductionConfig(Config):

    def __init__(self):
        self.DEBUG = False
        self.SESSION_COOKIE_NAME = "7e23b"
        self.SESSION_COOKIE_SECURE = True
        self.PERMANENT_SESSION_LIFETIME = timedelta(days=3)
        self.SESSION_COOKIE_HTTPONLY = True
        self.ASSAYS_BUCKET = "mellicell_lab_bucket"
        self.ASSAY_TOPIC_PATH = "assay_created"
        self.PROJECT_ID = "development-311316"
        self.CYTOSMART_BUCKET = "cytosmart_brightfield_images_mellicell"
        self.CYTOSMART_MASK_BUCKET = "cytosmart_masks_mellicell"
        self.REQUEST_MASK_TOPIC = "mask_requested"
        self.VERSION_2_DATASET = "mellicell_image_data_v2"
        self.VERSION_1_DATASET = "mellicell_image_data"
        self.OLYMPUS_LIPID_TABLE = "cellpose-measurments"
        self.OLYMPUS_CLUSTER_TABLE = "cluster-statistics"
        self.CYTOSMART_LIPID_TABLE = "cytosmart_lipid_measurments"
        self.CYTOSMART_CLUSTER_TABLE = "cytosmart_measurements"
        self.RESIZE_IMAGE_SERVICE_URL = "https://olympus-resize-service-gkjofhbn5a-ue.a.run.app"
        self.OLYMPUS_IMAGE_BUCKET = "mellicell_raw_images"
