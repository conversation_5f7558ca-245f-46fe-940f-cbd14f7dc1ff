import re, numpy as np, pandas as pd
from typing import Set
from datetime import datetime, timedelta
from sqlalchemy import or_

from base.io import cached_df
from base.store import cache_store
from base.clients import bigquery
from base.logs import make_logger

from images.model import ImageSet
from plates.model import Plate
from workflow.orm.rar import Request
from workflow.orm.steps import SeedIn

from qc.config import DevelopmentConfig

log = make_logger(__name__)
conf = DevelopmentConfig(local=False)

bucket_alias = "cytosmart"

excluded_wells_384 = \
    set([f"{letter}{number}" for letter in 'ABCNOP' for number in range(1, 25)]) | \
    set([f"{letter}{number}" for letter in 'ABCDEFGHIJKLMNOPH' for number in range(1, 4)]) | \
    set([f"{letter}{number}" for letter in 'ABCDEFGHIJKLMNOPH' for number in range(22, 25)])
        
excluded_wells_96 = \
    set([f"{letter}{number}" for letter in 'ABGH' for number in range(1, 13)]) | \
    set([f"{letter}{number}" for letter in 'ABCDEFGH' for number in range(1, 3)]) | \
    set([f"{letter}{number}" for letter in 'ABCDEFGH' for number in range(11, 13)])

def excl(excluded_wells):
    if excluded_wells is None: return "some"
    else: return f"{len(excluded_wells)}"

def excl_clause(excluded_wells):
    if excluded_wells == []: return ""
    else: return f" {excl(excluded_wells).capitalize()} border wells were excluded from the analysis."


class ImageData:
    def __init__(self, plate_no):
        self.plate_no = plate_no

class ImageSetData(ImageData):
    def __init__(self, ims: ImageSet, plate_no: int):
        super().__init__(plate_no)
        self.image_set = ims
        self.plate_no = plate_no
        self.plate_format = ims.plate.format
        self.day = ims.day
        self.store = cache_store() / "qc" / f"plate{plate_no}" / f"day{self.day}"

    def __repr__(self):
        return f"Plate {self.plate_no}, day {self.day}"
    
    def query(self, table, cols: str = "*"):
        log(f"Querying {table} for {self.image_set.bt_key}")
        df = bigquery().query(
            f"""SELECT {cols} FROM `{table}`
            WHERE image LIKE '%{self.image_set.bt_key}%';"""
        ).result().to_dataframe()
        df['well'] = df['image'].apply(lambda x: re.search(r'(?P<well>[A-P]\d+)_export.jpg', x).group('well'))
        df = df.drop(columns=['image'])
        return df
    
    @cached_df("clusters.csv")
    def cluster_df(self):
        return self.query(
            f"{conf.PROJECT_ID}.{conf.VERSION_2_DATASET}.{conf.CYTOSMART_CLUSTER_TABLE}",
            "image, cluster, count_area, mean_area, std_area, min_area, max_area"
        )
    
    @cached_df("lipids.csv")
    def lipid_df(self):
        return self.query(f"{conf.PROJECT_ID}.{conf.VERSION_2_DATASET}.{conf.CYTOSMART_LIPID_TABLE}")

    def size_params(self, excluded_wells: Set[int] = None):
        df0 = self.cluster_df
        df = df0
        if excluded_wells:
            df = df0[~df0['well'].isin(excluded_wells)]
        return {
            "plate": self.plate_no,
            "day": self.day,
            "wells": df0.shape[0],
            "count": df.shape[0],
            "mean": np.mean(df['mean_area']),
            "max":  np.mean(df['max_area']),
            "mean-std": np.std(df['mean_area']),
            "max-std":  np.std(df['max_area'])
        }

class PlateImageData(ImageData):
    def __init__(self, db, plate_no, excluded_wells: Set[int] = None):
        super().__init__(plate_no)
        self.db = db
        self.store = cache_store() / "qc" / f"plate{plate_no}"

        # Query the database for the plate and its image sets
        q = db.query(Plate, Request, SeedIn, ImageSet).\
            filter(or_(Plate.name == str(plate_no), Plate.name == f"Plate{plate_no:04d}")).\
            filter(SeedIn.plate == Plate.id).\
            filter(Request.id == SeedIn.request).\
            filter(ImageSet.plate_id == Plate.id)
        rmsd = dict()
        plate_format = None
        log(f"Image sets for Plate {plate_no}: {q.count()}")
        if q.count() == 0: 
            raise Exception(f"No image sets found for plate {plate_no}")
        for p, req, _, ims in q.all():
            day = ims.day # plate_days(ims.created, req.created)
            if day<0: raise Exception(f"Negative day {day} for {plate_no}")
            if plate_format is None: plate_format = p.format
            if p.format != plate_format: 
                raise Exception(f"Format mismatch for plate {plate_no}: {p.format} != {plate_format}")
            log(f"  Day {day} {ims.bt_key}")
            rmsd[day] = ims
            self.plate_id = p.id
        if plate_format == "96-well": self.excluded_wells = excluded_wells or excluded_wells_96
        elif plate_format == "384-well": self.excluded_wells = excluded_wells or excluded_wells_384
        else: raise Exception(f"Unknown plate format {plate_format}")
        self.plate_format = plate_format
        self.image_sets = {i:ImageSetData(rmsd[i], self.plate_no) for i in sorted(rmsd.keys())}
    
    def __repr__(self):
        return f"Plate {self.plate_no}"
    
    def cluster_df(self, ims: ImageSetData):
        """Return filtered cluster data for an image set"""
        df = ims.cluster_df
        if self.excluded_wells:
            df = df[~df['well'].isin(self.excluded_wells)]
        return df
    
    @cached_df("lipid-size.csv")
    def lipid_size(self):
        ims = self.image_sets.values()
        log(f"Plate {self.plate_no}, {len(ims)} image sets")
        data = []
        for im in ims:
            log(f"  {self.plate_no} {im.day} {im.image_set.bt_key}")
            data.append(im.size_params(self.excluded_wells))
        df = pd.DataFrame(data)
        log(df)
        return df

def parse_plate_no(pn: str):
    """Parse a plate number from plate name"""
    if pn.startswith("Plate"): return int(pn[5:])
    if pn.startswith("PD"): return int(pn[2:])
    return int(pn)

def plate_days(image: datetime, seed: datetime):
    return (image - seed + timedelta(days=0.5)).days

def ready_plates(db):
    subq = db.query(ImageSet).filter(ImageSet.plate_id == Plate.id).exists()
    return db.query(Plate).filter(subq).all()

def ready_plate_nos(db):
    # Query the database for plates that have image sets
    plates = ready_plates(db)
    log(f"Plates with image sets: {len(plates)}")
    return sorted(list(set([parse_plate_no(p.name) for p in plates])))
