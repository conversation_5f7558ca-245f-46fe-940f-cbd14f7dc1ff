#
# This is the authentication module. It is responsible for handling user authentication and authorization.
# It uses the fastapi_login package to handle user authentication and authorization.
#
import os
from datetime import timedelta
from typing import Named<PERSON>uple
from fastapi import Request
from fastapi_login import LoginManager
from passlib.context import Crypt<PERSON>ontext


from base.database import db_conn, Session
from base.secrets import secrets
from app.model import User as DbUser
from app.layouts import Context
from base.logs import make_logger
log = make_logger(__name__)

#
# Set up a LoginManager.
#
class NotAuthenticatedException(Exception):
    pass
        
manager = LoginManager(secrets().auth.salt, '/auth/login', use_cookie=True, 
    custom_exception=NotAuthenticatedException,                   
    default_expiry=timedelta(hours=12)
)
    
# 
# Password hashing
#
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def hash_password(password: str):
    return pwd_context.hash(password)
#
# Context manager for routes, user and database
#
async def context_optional(request: Request):
    """Context manager with authorization optional."""
    user = await manager.optional(request)
    db = db_conn.session()
    try:
        log(f"Context, user: {user} (optional)")
        yield Context(db, user, [])
    finally:
        db.close()

async def context(request: Request):
    """Context manager with authorization required."""
    user = await manager(request)
    db = db_conn.session()
    try:
        log(f"Context, user: {user.name}")
        yield Context(db, user, [])
    finally:
        db.close()

#
# User database
#
class User(NamedTuple):
    name: str
    email: str
    role: str
    password: str

# user_db = None
# def get_user_db():
#     global user_db
#     if user_db is None:
#         user_db = db_conn.session()
#     return user_db

# def query_user(user_id: str):
#     #get_user_db().rollback()
#     users = get_user_db().query(User).all()
#     if len(users) == 0:
#         log("Found no users, making admin:")
#         make_admin()
#         users = get_user_db().query(User).all()
#         if len(users) == 0:
#             raise Exception("No users and could not make admin user")
#     umap = {u.name: u for u in users}
#     return umap.get(user_id)
                 
def query_user(user_id: str):
    global _users
    if _users is None:
        db = db_conn.session()
        with db.begin():
            users = db.query(DbUser).all()
            # log(f"Loading users ({len(_users)}):")
            # for u in users: 
            #     log(f"  {u.name} {u.email} {u.role} {u.password}")
            if len(users) == 0:
                log("Found no users, making admin:")
                make_admin(db)
                users = db.query(DbUser).all()
                if len(_users) == 0:
                    raise Exception("No users and could not make admin user")
            _users = {u.name: User(u.name, u.email, u.role, u.password) for u in users}
    return _users.get(user_id)
_users = None

def touch_users():
    global _users
    _users = None

def make_user(db: Session, name: str, 
    email: str = "<EMAIL>", 
    role: str = "bot", 
    #admin: bool = False, 
    password: str = "changeme"
):
    """Create a user."""
    user = query_user(name)
    if not user:
        log(f"Create user: {name}")
        db.rollback()
        with db.begin():
            user = DbUser(
                name = name,
                email = email,
                role = role,
                password = hash_password(password)
            )
            log(f"Create user: {user}")
            db.add(user)
    return user


def make_admin(db: Session):
    """Create an admin user if none exists."""
    admins = db.query(DbUser).filter_by(role="administrator").all()
    if len(admins) == 0:
        make_user(db, "admin",
            email = "<EMAIL>",
            admin = True,
            password = hash_password("changeme")
        )


@manager.user_loader()
def load_user(user_id: str):
    #log(f"Load_user: {user_id}")
    user = query_user(user_id)
    if user is None: 
        raise Exception(f"User {user_id} not found")
    return user




