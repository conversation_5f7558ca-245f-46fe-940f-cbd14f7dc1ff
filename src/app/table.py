import pandas as pd
from typing import Callable
from dataclasses import dataclass, is_dataclass
from sqlalchemy.engine.row import Row
from typing import List, Any
from dataclasses import fields
from base.database import Base
from app.html import Html, ht
from base.reps import Rep, rep, ObjRep
from base.logs import make_logger
log = make_logger(__name__)


def table(
    data: List[Any] | pd.DataFrame | dict | object, 
    repr: Rep = None,
    cols: dict = None,
    status: Callable[[Any],str] = None
) -> Html:
    """Create an HTML table from a list of objects or a DataFrame."""
    if isinstance(data, list) or isinstance(data, pd.DataFrame):
        # Deal with no data
        if len(data) == 0: return ht.div("No data")
    else:
        # A single dict or object may be passed to create a table listing it's fields
        if not isinstance(data, dict):
            log(f"Data is not a list or dict: {data}")
            dr  = rep(type(data))
            data = {f: dr.htf(f, data) for f in dr.fields.keys()}
        data = [dict(zip(["Field", "Value"], row)) for row in data.items()] 
    if isinstance(data, pd.DataFrame):
        if repr is None: repr = ObjRep.from_dataframe(data)
        data = data.to_dict(orient="records")
    if repr is None: 
        repr = ObjRep.from_obj(data[0])
    if cols is not None:
        repr = repr(cols)
    fs = repr.fields.keys()
    log(f"Rep Table: {repr} {fs}")
    #log(f"Data[0]: {data[0]}")
    def trnode(d): 
        if status is None or status(d) is None: return ht.tr()
        else: return ht.tr(status=status(d))
    header = ht.thead(ht.tr(*[ht.th(f) for f in fs]))
    rows = ht.tbody(*[trnode(d)(*[ht.td(repr.htf(f, d)) for f in fs]) for d in data])
    return ht.table(id="example", _class="stripe")(header, rows) # style="width:100%", 

# editable: https://stackoverflow.com/questions/73082579/editable-table-cells-html
