import os
from pydantic_settings import BaseSettings
from dotenv import load_dotenv
from base.logs import make_logger
import json
log = make_logger(__name__)

load_dotenv()

class Settings(BaseSettings):
    DBHOST: str
    DBPORT: int
    DBUSER: str
    DBPASS: str
    DBNAME: str
    DRIVERNAME: str
    PORT: int
    SECRETS: str = ".secrets"  # Default value to prevent validation errors
    
settings = Settings()

def get_secret(sn):
    verbose = False
    sdir = settings.SECRETS
    if (verbose): log(f"Secrets: {sdir}")
    if sdir=="": 
        sdir = ".secrets"
    
    secrets_json_path = f"{sdir}/secrets.json"
    log(f"Checking secrets.json at {secrets_json_path} -> {os.path.exists(secrets_json_path)}")
    if os.path.exists(secrets_json_path):
        with open(secrets_json_path, 'r') as f:
            secrets_data = json.load(f)
            
        if sn == "salt":
            return secrets_data.get("auth", {}).get("salt", "")
        elif sn.endswith(".json") and "development" in sn:
            # For Google credentials
            return _write_temp_secret(secrets_data.get("google", {}).get("development", {}), f"{sdir}/{sn}")
        elif sn == "token.json":
            # For Google Sheets token
            return _write_temp_secret(secrets_data.get("sheets", {}).get("token", {}), f"{sdir}/{sn}")
    # Fallback to original logic
    else:
        fs = os.listdir(sdir)
        if len(fs)==1 and fs[0]==".secrets":
            sdir = f"{sdir}/.secrets"
            fs = os.listdir(sdir)
            if verbose: log(f"  --> {sdir}")
        if verbose:
            for f in fs:
                log(f"  {f}")
                if os.path.isdir(f"{sdir}/{f}"):
                    for sf in os.listdir(f"{sdir}/{f}"):
                        log(f"  --{sf}")
    fn = f"{sdir}/{sn}"
    if os.path.isdir(fn) or not os.path.exists(fn):
        # Accomodate mount limitations in Cloud Run
        bn, _ = os.path.splitext(sn)
        fn = f"{sdir}/{bn}/{sn}"
    log(f"Secret: {fn} - exists: {os.path.exists(fn)}")
    return fn

def _write_temp_secret(data, path):
    """Write temporary secret file and return the path"""
    if not data:
        return path  # Return original path if no data
        
    os.makedirs(os.path.dirname(path), exist_ok=True)
    with open(path, 'w') as f:
        json.dump(data, f)
    return path
