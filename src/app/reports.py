from app.html import HtmlSource
from app.table import table
from base.logs import make_logger
from base.store import Store

log = make_logger(__name__)

class Figures:
    def __init__(self, ht: HtmlSource):
        self.fig_no = 0
        self.ht = ht
        
    def __call__(self, graph):
        self.fig_no += 1
        log(f"Figure {self.fig_no}")
        graph.plot()
        log(f"Figure {self.fig_no}: {graph.title} -> {graph.url} {graph.fn}")
        return self.ht.div(
            self.ht.img(src=graph.url, style="width: 100%;", alt=graph.title),
            self.ht.p(f"Figure {self.fig_no}: "+graph.caption)
        )

class Tables:
    def __init__(self, ht: HtmlSource):
        self.ht = ht
        self.tab_no = 0
        
    def __call__(self, data, caption: str = None, cols: dict = None, **kwargs):
        self.tab_no += 1
        return self.ht.div(
            table(data, cols=cols)(**kwargs),
            self.ht.when(caption)(self.ht.p(f"Table {self.tab_no}: {caption}"))
        )

class Analysis:
    store: Store
    
class Plot:
    def __init__(self, analysis: Analysis, name: str):
        self.analysis = analysis
        self.name = name
        self.fn = f"{name}.png"
        self.store = analysis.store
        self.url = f"/store/cache/bin/{self.analysis.store.path}{self.fn}"
     
