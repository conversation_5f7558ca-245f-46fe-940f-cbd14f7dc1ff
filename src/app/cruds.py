from dataclasses import dataclass, fields
from datetime import datetime
import pytz
from fastapi import Depends, Request, Request as HttpRequest, APIRouter
from fastapi.responses import HTMLResponse, RedirectResponse
from enum import Enum

from base.util import show_class, word_s
from base.logs import make_logger
from base.reps import rep
from app.auth import context
from app.layouts import Context
from app.table import table
from base.sheets import get_sheet_data, match_heads
from base.database import Base

log = make_logger(__name__)


# Aliases for parsing certain data items
aliases = {
    "Gender": {"M": "male", "F": "female"},
    "Race": {"Black": "African", "White": "Caucasian"}
}

# Hide certain fields in forms
hide = {
    "Plate": ["barcode"],
}

def crud(path: str, model: Base, router: APIRouter):
    """Set up a CRUD for a model. This should work for all database models."""
    
    # Get the model rep
    mrep = rep(model)
    if "created" in mrep.fields: 
        mrep = mrep.hidden("created", lambda: datetime.now(pytz.utc))
    for f in hide.get(model.__name__, []): mrep = mrep.omit(f)
    #log(f"Crud: {model.__name__} -> {mrep}")
    
    # Class for receiving form data
    class Input(mrep.form_parser()): pass

    # plates/crud/plate/
    router_path = "crud/"+path
    path = "crud/"+path if router.prefix == "" else router.prefix[1:] + "/crud/"+path
    

    async def list(ht: Context = Depends(context)):
        """Show a list of items."""
        items = ht.get_items(model)
        n = len(items)
        ret = ht.page(f"List of {word_s(model.__name__)}", 
            ht.h2(f"List of all {n} {word_s(model.__name__, n)}"),
            table(items, mrep), ht.hr, 
            ht.link(f"{path}/create")(f"Add a new {model.__name__}"),
            ht.link(f"{path}/sheet")(f"Load {word_s(model.__name__)} from Google Sheet"),
        )
        return ret.pretty()
    
    async def create(ht: Context = Depends(context)):
        """Create a new item."""
        ret = ht.page(f"Create a {model.__name__}", 
            ht.h2(f"Enter new {model.__name__} information:"),  
            ht.rform(f"{path}/create", mrep)
        )
        return ret.pretty()
    
    async def create_post(input: Input = Depends(), ht: Context = Depends(context)):
        log(f"New {model.__name__}: {input}")
        # check for duplicates
        if hasattr(input, "name") and hasattr(model, "name"):
            item = ht.db.query(model).filter(model.name == input.name).first()
            log(f"Find {model.__name__} {input.name} -> {item}")
            if item is not None:
                msg = ht.page("Error", f"{model.__name__} {input.name} already exists.", ht.link(f"{path}/create")("Try again"))
                return msg.pretty()
        # Add and commit
        ht.db.add(model(**input.__dict__))
        ht.db.commit()
        return RedirectResponse(url=f"/{path}/list/", status_code=302)
    
    # For debugging only
    async def show_data(request: HttpRequest, ht: Context = Depends(context)):
        form = await request.form()
        log(f"FormData: {form}")
        show_class(mrep.form_parser())
        return ht.page("Data", f"Form: {form}").pretty()
        
    #
    #  Load data from a Google Sheet
    #
    
    # convenience defaults for a donor sheet. Must be moved out of here.
    @dataclass
    class SheetIn:
        sheet_id: str = "1Bg3ezlSAiyc-SBg9W9IRxyU2aiYys5dXCBa12FfmEJY"
        range: str = "Donor Lots!A:L"
        
    async def sheet(ht: Context = Depends(context)):
        """Ask for the sheet id and data range."""
        ret = ht.page(f"Sheet", 
            ht.h2(f"Enter sheet information:"),
            ht.rform(f"{path}/sheet", rep(SheetIn))
        )
        return ret.pretty()
    
    async def sheet_post(input: SheetIn = Depends(), ht: Context = Depends(context)):
        """Load the sheet data and match headers."""
        # Get the sheet data and match headers with model
        data = get_sheet_data(input.sheet_id, input.range)
        dicts = [dict(zip(data[0], row)) for row in data[1:]]
        props = [c.name for c in model.__table__.columns if c.name != "id"]
        map, missing, unused = match_heads(data[0], props)
        
        # If there's unmatched properties, ask the user to match them        
        fix_form = None
        if len(missing)>0:
            def inp(prop):
                opts = [ht.option(value=h)(h) for h in unused]
                input = ht.select(id=prop, name=prop)(*opts)
                return ht.tablerow(ht.label(_for = prop)(prop+":"), input)
            ifs = [inp(prop) for prop in missing]
            fix_form = ht.h3("Assign missing headers: ") + \
                ht.form(method="post", action=ht.url(f"{path}/data"))(
                    ht.table(*ifs, ht.submitrow()),
                    ht.input(type="hidden", name="_sheet_id", value=input.sheet_id),
                    ht.input(type="hidden", name="_range", value=input.range),
                )

        # Present header matches and form to fix them
        ret = ht.page(f"Sheet verification for {model.__name__}s", 
            ht.h2(f"Raw sheet data:"),
            table(dicts),
            table(map),
            fix_form,
        )
        return ret.pretty()
    
    async def data_post(request: Request, ht: Context = Depends(context)):
        """Parse the sheet data and add it to the database."""
        form = await request.form()
        log(f"Form:  {form} {form['_sheet_id']}")
        log(f"  FormData: {form.__class__.__dict__}")

        data = get_sheet_data(form['_sheet_id'], form['_range'])
        fs = [(k, r) for k, r in mrep.fields.items() if k != "id"]
        map, missing, unused = match_heads(data[0], [fn for fn, _ in fs])
        
        # fill in the missing headers
        for p in missing:
            map[p] = form[p]
        log(f"Map: {map}")
        
        # set options and aliases for the enum fields
        for _, r in fs:
            if issubclass(r.typ, Base) and hasattr(r.typ, "__enum__"):
                r._options = ht.get_items(r.typ)
            if issubclass(r.typ, Enum) or hasattr(r.typ, "__enum__"):
                if r.typ.__name__ in aliases: r._aliases = aliases[r.typ.__name__]
            
        xs = []
        good = []
        errs = []
        for i, row in enumerate(data):
            if i==0: continue
            while len(row) < len(data[0]): row.append(None)
            rx = {h:row[i] for i, h in enumerate(data[0])}
            vs = {name:r.parse(rx[map[name]]) for name, r in fs}
            item = model(**vs)
            xs.append(item)
            try:
                ht.db.add(item)
                ht.db.commit()
                good.append(item)
            except Exception as e:
                ht.db.rollback()
                errs.append(f"Error adding {item}: {e}")
                pass
        ret = ht.page(f"Data for {model.__name__}", 
            ht.h2(f"Parsed sheet data:"), table(xs),
            ht.h2(f"Good:"), table(good),
            ht.h2(f"Errors:"), table(errs)
        )
        return ret.pretty()
        
    # Add the routes
    router.get(f"/{router_path}/list/", response_class=HTMLResponse)(list)
    router.get(f"/{router_path}/create/", response_class=HTMLResponse)(create)
    router.post(f"/{router_path}/create/", response_class=HTMLResponse)(create_post)
    router.get(f"/{router_path}/sheet/", response_class=HTMLResponse)(sheet)
    router.post(f"/{router_path}/sheet/", response_class=HTMLResponse)(sheet_post)
    router.post(f"/{router_path}/data/", response_class=HTMLResponse)(data_post)
    router.post(f"/{router_path}/show_data/", response_class=HTMLResponse)(show_data) # for debugging onl
    return (path, model)
