from fastapi import APIRouter, Depends, HTTPException, Response
from fastapi.responses import HTMLResponse, StreamingResponse
from base.store import cache_store, logs_store, Store
from base.logs import make_logger
from app.layouts import Context
from app.auth import context

log = make_logger(__name__)

router = APIRouter(prefix="/store")

class StoreServer:
    def __init__(self, store: Store):
        self.store = store
        self.router_path = store.name

    def thumb(self, ht: Context, fn: str, **xargs):
        fn = f"{fn}-thumb.jpg"
        if not self.store.exists(fn): return None
        return ht.img(src=f"/store/cache/bin/{fn}", **xargs)()
    
    async def view(self, path: str, ht: Context = Depends(context)):
        if path.endswith("/"):
            path = path[:-1]
        if path and not path.startswith("/"):
            path = "/" + path
        log(f"GET {self.store.name}/{path} -> {path}")
        if path and self.store.exists(path):
            link = ht.ref(f"store/{self.store.name}/text{path}")(path)
            kind = "File"
        else:
            link = ht.span(path)
            try:
                sfs = self.store.list(path)
                log(f"Store: {self.store}")
                log(f"  List: {sfs}")
                kind = "Directory"
            except Exception as e:
                log(f"Error: {e}")
                sfs = []
            if len(sfs) > 0:
                link += ht.ul(
                    *[ht.li(ht.ref(f"store/{self.store.name}/view{path}/{sf}")(sf)) for sf in sfs]
                )
            else:
                link += ht.p(kind)
            kind = "Path"
        return ht.page(f"Store {self.store.name}", 
            ht.h2(f"Store {self.store.name}"),
            ht.h3(f"{kind}: ", link),
        ).pretty()

    def stream(self, path: str, mode: str, **xargs: str):
        if path.endswith("/"):
            path = path[:-1]
        if not self.store.exists(path):
            raise HTTPException(status_code=404, detail=f"Not found: {self.store} / {path}")
        if mode=="t" or mode=="":
            def iter():
                with self.store.open(path, "r"+mode) as f:
                    yield from f
        elif mode=="b":
            def iter():
                with self.store.open(path, "r"+mode) as f:
                    while chunk := f.read(4192): 
                        yield chunk
        else:
            raise HTTPException(status_code=400, detail=f"Invalid mode: {mode}")
        return StreamingResponse(iter(), **xargs)
    
    async def text(self, path: str, ht: Context = Depends(context)):
        """Stream a plain text file from the store. Used for logs."""
        log(f"STREAM text {self.store.name}/{path}")
        return self.stream(path, "t", media_type="text/plain")

    async def bin(self, path: str, ht: Context = Depends(context)):
        """Stream a binary file from the store. Used for images."""
        log(f"STREAM binary {self.store.name}/{path}")
        return self.stream(path, "b", media_type="application/octet-stream")
        # log(f"BIN {self.store.name}/{path}")
        # data = self.store.open(path, "rb").read()
        # return Response(content=data, media_type="image/png")
        
    async def download(self, path: str, ht: Context = Depends(context)):
        """Stream a plain text file from the store. Used for logs."""
        log(f"DOWNLOAD text {self.store.name}/{path}")
        fn = path.replace("/", "_")
        return self.stream(path, "t", media_type="text/plain", headers={"Content-Disposition": f"attachment; filename={fn}"})

    async def downbin(self, path: str, ht: Context = Depends(context)):
        """Stream a plain text file from the store. Used for logs."""
        log(f"DOWNLOAD binary {self.store.name}/{path}")
        fn = path.replace("/", "_")
        return self.stream(path, "t", media_type="application/octet-stream", headers={"Content-Disposition": f"attachment; filename={fn}"})
    #
    #   All the routes in this GUI section
    #
    def route(self):
        # log(f"ROUTE: store - `{self.router_path}`")
        router.get(f"/{self.router_path}/view/"+"{path:path}", response_class=HTMLResponse)(self.view)
        router.get(f"/{self.router_path}/download/"+"{path:path}", response_class=StreamingResponse)(self.download)
        router.get(f"/{self.router_path}/text/"+"{path:path}", response_class=StreamingResponse)(self.text)
        router.get(f"/{self.router_path}/bin/"+"{path:path}", response_class=StreamingResponse)(self.bin)
        return router

# Data store server for images and data files
cache_server = StoreServer(cache_store())
# Data store server for logs
logs_server = StoreServer(logs_store())

