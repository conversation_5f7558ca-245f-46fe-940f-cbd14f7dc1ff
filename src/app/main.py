import os
import time
from datetime import datetime
from traceback import print_exception
import mat<PERSON><PERSON><PERSON>b

import uvicorn
from fastapi import Depends, FastAPI
from fastapi.staticfiles import StaticFiles
from fastapi.requests import Request
from fastapi.responses import RedirectResponse, HTMLResponse

from app.config import settings

from app.store import StoreServer
from base.appinfo import appinfo
from base.util import word_s, word_ing
from base.logs import make_logger
from base.store import logs_store
from app.auth import context_optional
from app.layouts import Context, navset

from app.auth import context
from images.parse import java_vm
import workflow.reports  # noqa: F401
from workflow.flow import Requests, app as workflow_router
from workflow.workflow import Flow

from plates.router import router as plates_router, cruds as plates_cruds
from plates.array import router as array_router
from treatments.router import crud_path as treatment_crud, orm_model as treatment_orm,  router as treatments_router
from images.router import router as imaging_router, cruds as imaging_cruds
from genetics.pages import router as genetics_router
from app.router import router as security_router
from app.auth import NotAuthenticatedException
from app.store import cache_server
from qc.routes import router as qc_router
from bots.routes import router as bot_router

log = make_logger(__name__)

# from app.cruds import crud

app = FastAPI(debug=True)

#
# Catch exceptions and log them
#
async def catch_exceptions_middleware(request: Request, call_next):
    try:
        with java_vm():
            return await call_next(request)
    except Exception as e:
        #print_exception(e)
        log(f"Exception in {request.url}: {e}")
        ht = await context_optional(request).__anext__()
        store = logs_store() / "crash"
        fn = f"crash-{datetime.now().strftime('%Y%m/%d-%H%M%S')}.log"
        log(f"Logging Exception at {datetime.now()} to {store.tag} {fn}")
        with store.open(fn, "w") as f:
            f.write(f"Exception logged at {datetime.now()}\n")
            f.write(f"{appinfo}\n")
            if ht.user is not None:
                f.write(f"User: {ht.user.name}\n")
            f.write("Request:\n")
            f.write(f"  {request.method} {request.url}\n")
            f.write(f"    from {request.client.host}:{request.client.port}\n")
            f.write(f"\n*** Headers ***\n{request.headers}\n")
            f.write(f"\n*** Cookies ***\n{request.cookies}\n")
            f.write(f"\n\n\nException: {e}\n")
            print_exception(e, file=f)
            f.write(f"End of Exception log at {datetime.now()}\n")
        print_exception(e)  # Print to console for immediate visibility
        return HTMLResponse(ht.error(
            ht.h3("An application error has occurred."),
            ht.p("Please report it to a MellitOS custodian!"),
            ht.br, ht.code(f"Exception: {e}"),
            #ht.br, ht.a(href=f"/static/logs/crash/{fn}")("Error Log"),
            ht.br, ht.a(href=f"/store/logs/text/crash/{fn}")("Error Log"),
        ))

app.middleware('http')(catch_exceptions_middleware)

# Serve static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# time zone...
os.environ['TZ'] = 'America/New_York'
time.tzset()
log(f"Timezone set to {os.environ['TZ']}, current time: {time.asctime()}")

# Base.metadata.create_all(bind=engine)

matplotlib.use('Agg') # This is necessary to avoid a crash when running in headless mode

# List of routers
routers = [
    security_router, treatments_router, plates_router, array_router, 
    imaging_router, genetics_router, qc_router, bot_router, workflow_router
]

cruds = []
for router, router_cruds in [(plates_router, plates_cruds), (imaging_router, imaging_cruds)]:
    for path, model in router_cruds:
        cruds.append((path, model))

cruds.append((treatment_crud, treatment_orm))

# Set up workflow routes. "flows" is imported from workflow/services/workflow.py
for r in routers:
    app.include_router(r)

# Data store server for logs
logs_server = StoreServer(logs_store())
app.include_router(logs_server.route())
    
# Data store server for images and data files
app.include_router(cache_server.route())

# To make up the nav bar
def navlinks(ht):
    links = {
        "Database": [ht.link(f"{path}/list")(f"{word_s(model.__name__)}") for path, model in cruds],
        **{ flow.name: flow.links(ht) for flow in Flow.flows },
        "Dosing": [
            ht.link("plates")("Dosing Layouts"), 
            ht.link("plates/treatment_batches")("plate treatments")
        ],
        "Reports": [
            ht.link("workflow/reports/plates")("Status Reports"),
            ht.link("imaging/plates")("Imaging Reports"),
            ht.link("qc")("QC Reports")
        ],
        "Targets": [
            ht.link("genetics/bmi/hits")("BMI GWAS"),
        ],
        "Bots": [ht.link("bots")("Bot Management")] if ht.admin else [],
    }
    return links

# Set the nav bar
navset(navlinks)
               
# System home page
@app.get("/", response_class=HTMLResponse)
def home(ht: Context = Depends(context)):
    ht.populate_enums()
    ret = ht.page("Home",
        ht.h1(f"Hello, {ht.user.name}!"), ht.br,
        ht.h2("Here's what's up today:"),
        *[ht.link(f"{s.path}/view")(ht.h3(f"{Requests(s, ht.db).n} plates need {word_ing(s.name)}")) for s in Flow.steps],
    )
    return ret.pretty()

@app.exception_handler(NotAuthenticatedException)
def auth_exception_handler(request: Request, exc: NotAuthenticatedException):
    """
    Redirect the user to the login page if not logged in
    """
    log(f"Auth Exception: {exc}")
    return RedirectResponse(url='/auth/login')

if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=settings.PORT, log_level="info")
