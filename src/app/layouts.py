from dataclasses import dataclass, fields
from typing import Any, Callable, List, Tuple

from base.appinfo import appinfo
from base.reps import Rep, ObjRep
from app.html import Html, HtmlString, HtmlSource, ht
from app.table import table
from base.database import Base, transact, flush, Session, populate_enums as db_populate_enums
from app.model import User, UserRole
from plates.model import WellPos, PlateFormat
import string
from base.logs import make_logger
log = make_logger(__name__)

resources = ht(
    ht.link(rel="shortcut icon", type="image/x-icon", href="/static/img/favicon.ico"),
    ht.link(rel="stylesheet", type="text/css", href="/static/css/datatables.min.css"),
    ht.link(rel="stylesheet", type="text/css", href="/static/css/select.dataTables.css"),
    # These are our own styles
    ht.link(rel="stylesheet", type="text/css", href="/static/css/style.css"),

    ht.script(type="text/javascript", src="/static/js/jquery-3.7.1.min.js"),
    ht.script(type="text/javascript", src="/static/js/datatables.js"),
    ht.script(type="text/javascript", src="/static/js/dataTables.select.js"),
    ht.script(type="text/javascript", src="/static/js/select.dataTables.js"),
    # These are our own scripts
    ht.script(type="text/javascript", src="/static/js/core.js")()
)

# Set this to a function that generates a list of links in the main app module (main.py)
navgen: Callable[["Context"], Html] = lambda ht: []
def navset(f: Callable[["Context"], Html]):
    global navgen
    navgen = f

def navbar(ht):
    global navgen
    uname = f" ({ht.user.name})" if ht.authenticated else ""
    links = { f"User{uname}": [
        ht.link("")("Home"),
        ht.when(ht.admin)(ht.link("auth/users")("Users")),
        ht.when(ht.admin)(ht.link("auth/reset")("Reset Password")),
        ht.link("auth/change")("Change Password"),
        ht.link("auth/logout")("Logout"),
    ]} | {k: v for k, v in navgen(ht).items() if len(v) > 0}
    bar = ht.ul(
        *[ht.details(ht.summary(k), *[ht.li(x) for x in xs]) for k, xs in links.items()]
    )
    logo = ht.img(src="/static/img/logo.png", alt="logo", width="30", _class="logo")
    navh = ht.table(style="font-size: 1.5em; text-align:center;")(ht.tr(
        ht.td(logo), ht.td("MellitOS"), 
    ))
    appi = ht.table(style="text-align:left;")(
        ht.tr(ht.td(f"{appinfo.name} {appinfo.version} {'' if appinfo.deployed else 'dev'}")),
        ht.tr(ht.td(f"{appinfo.date}")),
        ht.tr(style="font-size: 0.7em;")(ht.td(appinfo.code_id()))
    )
        
    return ht.div(_class="navbar")(
        navh,
        ht.when(ht.authenticated)(bar),
        ht.when(not ht.authenticated)("Please log in!"),
        ht.when(ht.admin)(appi)
    )

class Context(HtmlSource):
    def __init__(self, db: Session, user: User, messages: dict, **kwargs):
        self.db = db
        self.user = user
        self.admin = user is not None and user.role == "administrator"
        self.messages = messages
        self.__dict__.update(kwargs)

        self.authenticated = user is not None

    def url(self, route: str):
        """Make a correctly routed URL."""
        if route=="": return "/"
        parts = route.split("?")
        body = parts[0]
        pars = "?"+parts[1] if len(parts)>1 else ""
        return f"/{body}/{pars}"
        # if route=="": return f"{self.root}/"
        # return f"{self.root}/{route}/"
    
    # make a correctly routed link
    def link(ht, route: str): return ht.a(href=ht.url(route)) 

    def header(ht, title: str):
        return HtmlString('<!DOCTYPE html>\n<html lang="en">') + \
        ht.head(
            '<meta charset="UTF-8">',
            '<meta http-equiv="X-UA-Compatible" content="IE=edge">',
            '<meta name="viewport" content="width=device-width, initial-scale=1.0">',
            resources,
            ht.title(title)
        )

    
    def page(ht, title: str, *args: Html):
        #msgs = ht.when(len(ht.messages) > 0)(
        #    ht.ulist(*[HtmlString(m) for m in ht.messages])
        #)
        return ht.header(title) + ht.body(navbar(ht), ht.div(_class="main")(*args))
    
    def error(ht, *args: Html):
        return ht.page("Error", ht.h1("Error: "), *args).pretty()
    
    def success(ht, *args: Html):
        return ht.page("Success", ht.h1("Success: "), *args).pretty()
    
    #
    #  Generic GUI forms. Should work for any ORM model.
    #    
    def rform(ht, route: str, rep: Rep, 
        default = None,
        submits: List[str] = ["Submit"], 
        tag: str = None,
        hidden: dict = None,
        method: str = "post"
    ):
        """Create a form for a model class. The form will be a table of labeled input fields."""
        log(f"Form2: {rep}")
        ffs = rep.form_fields(lambda cl: ht.get_items(cl), default=default)
        fs = []
        for name, input in ffs:
            if hasattr(rep[name], "_hidden"):
                fs.append(input)
            else:                                         
                fs.append(ht.tablerow(ht.label(_for=name)(name+":"), input))
        fn = ht.form(method=method, action=ht.url(route))(
            ht.table(*fs, ht.submitrow(names=submits))
        )
        if tag is not None:
            fn = fn(_class=tag)
        return fn

    def get_items(ht, model):
        """Get select options from the database for an enum model. 
           If there are no options for an enum model, add them."""
        xs = ht.db.query(model).all()
        if len(xs)==0 and hasattr(model, "__enum__"):
            raise ValueError(f"No items in enum {model.__name__}")
        return xs

    # transaction utilities...
    def transact(ht): return transact(ht.db)
    def flush(ht, o: Base): return flush(ht.db, o)
    
    def populate_well_pos(ht):
        wells = ht.db.query(WellPos).all()
        if len(wells) != 0:
            return
        for plate_format in ht.db.query(PlateFormat).all():
            for row in range(plate_format.row_count):
                for col in range(plate_format.col_count):
                    well_name = string.ascii_uppercase[row] + str(col +  1)
                    well_pos = WellPos(well_name=well_name, row=row, col=col, plate_format=plate_format.name)
                    ht.db.add(well_pos)
        ht.db.commit()

    # Populate the enum models if needed
    def populate_enums(ht):
        db_populate_enums(ht.db)
        ht.populate_well_pos()
        log(f"Done populating enum models.")

    def include_js(ht, *scripts):
        """Include additional JavaScript files for specific pages"""
        return ht(*[
            ht.script(type="text/javascript", src=f"/static/js/{script}")()
            for script in scripts
        ])
