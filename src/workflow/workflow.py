from typing import Union
from fastapi.responses import HTMLResponse
import workflow.flow
from workflow.flow import FlowStep, Workflow

from workflow.culture import Culture<PERSON>low
from workflow.assay import AssayFlow

from base.logs import make_logger
log = make_logger(__name__)

class EntireWorkflow:
    """The entire workflow."""
    def __init__(self, *flows: Workflow):
        self.flows = list(flows)
        self.steps = [s for f in flows for s in f.steps]
        self.router_path = "workflow"
        
    def __call__(self, inType: type) -> Union[FlowStep, Workflow]:
        """singleton constructor for workflow steps."""
        if isinstance(inType, str):
            for step in self.steps: 
                if step.name == inType: return step
            return None
        if issubclass(inType, Workflow):
            for flow in self.flows: 
                if isinstance(flow, inType): return flow
            return None
        if issubclass(inType, FlowStep):
            for step in self.steps: 
                if isinstance(step, inType): return step
            return None
        raise Exception(f"{inType} is not a Workflow or FlowStep")
    
    def route(self):
        log(f"Routing {self.router_path}")
        for f in self.flows: f.route()


Flow = EntireWorkflow(
    CultureFlow(), AssayFlow() #, PipelineFlow(), #, TreatmentFlow() 
)
Flow.route()
workflow.flow.entire_workflow = Flow


