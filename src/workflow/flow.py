from typing import List, <PERSON><PERSON>
from datetime import datetime, timedelta
import pytz

from dataclasses import dataclass
import sqlalchemy as sa
from sqlalchemy import func, and_
from sqlalchemy.orm import aliased
from sqlalchemy.orm.session import Session
from fastapi import Depends, Form, Request as HttpRequest, APIRouter
from fastapi.responses import HTMLResponse, RedirectResponse

from base.util import word_ing, word_s, word_list, lazy
from base.store import File
from base.database import key_col, key_val, name_col, name_val
from base.reps import Rep, rep, ObjRep
from app.auth import context
from app.layouts import Context
from app.table import table

from base.database import Base, transact, flush
from plates.render import plate_view
from plates.model import MapGroup, Plate, PlateMap
from workflow.orm.rar import Step, Request, Action, Result, Station, Sop, ResultReason, ActionTag
from workflow.util import req_row_status
from base.logs import make_logger
log = make_logger(__name__)

entire_workflow = None
app = APIRouter(prefix="/workflow")
prefix = app.prefix[1:] + "/" if app.prefix != '' else ''

@dataclass
class ActionSelect:
    request_id: str = Form(...)
    station: Station = Form(...)
    
@dataclass
class ResultSubmit:
    action_id: int
    comment: str
    result: ResultReason

class UserError(Exception): pass

class DataTable:
    """Helper class to represent a list of items in a table."""
    
class Requests(DataTable):
    def __init__(self, step: Step, db: Session, action_id: int = None):
        """Get the requests for this step.
            action id: if given, only get requests for this action,
                otherwise get all queued requests (those with no action!).
                Need more filtering.
            Output:
                self.items is an informative but messy list of tuples, paired by
                self.rep to describe its make-up.
            """
        self.step = step
        log(f"Requests for {action_id}")
        # Map the query columns to table columns for the GUI
        self.columns = {
            "id": "req.id", 
            "created":"req.created", 
            "schedule": "req.schedule", 
            "deadline": "req.deadline",
        } | {m.__name__.lower(): name_col(m) for m in step.ins} | step.other_cols
                       
        # Use specific columns if they are defined in the subclass
        if hasattr(step.__class__, "columns_queued"): 
            self.columns = step.__class__.columns_queued
        # Generate the item rep
        self.rep = Rep.join(("req", Request), step.out, *step.ins)(self.columns)
        
        # Query the queued requests for this step.

        # criteria for joining the inputs into the query        
        insq = [getattr(step.out, k) == key_col(r.typ) for k, r in step.inreps.items()]
        
        # Now, the query
        #log(f"Ins: {step.ins}")
        reqt = aliased(Request) # need alias here because inputs could also contain a Request
        self.query = db.query(reqt, step.out, *step.ins).\
            filter(reqt.project == step.project).\
            filter(reqt.action == action_id). \
            filter(reqt.id == step.out.request).\
            filter(reqt.step == step.name).\
            filter(*insq)
        #log(f"Query: {self.query}, action_id: {action_id}")
        self.items = self.query.all()
        self.n = len(self.items)
        #log(f"Requests: {self.n} for {action_id}")
        
        # If this is a request on a request, get additional info
        if len(step.ins)==1 and issubclass(Request, step.ins[0]):
            steps = set([r[2].step for r in self.items])
            if len(steps) == 1:
                other = entire_workflow(steps.pop())
                log(f"Extended queue: {step.ins[0]} -> {steps} -> {other}")
                insq2 = [getattr(other.out, k) == key_col(r.typ) for k, r in other.inreps.items()]
                query = db.query(reqt, step.out, *step.ins, other.out, *other.ins).\
                    filter(reqt.project == step.project).\
                    filter(reqt.action == action_id). \
                    filter(reqt.id == step.out.request).\
                    filter(reqt.step == step.name).\
                    filter(*insq).\
                    filter(other.out.request == step.ins[0].id).\
                    filter(*insq2)
                items = query.all()
                n = len(items)
                log(f"Extended queue: {self.n} -> {n} items")
                
                self.items = items
                self.n = n
                self.columns = self.columns | {m.__name__.lower(): name_col(m) for m in other.ins}
                self.rep = Rep.join(("req", Request), step.out, *step.ins, other.out, *other.ins)(self.columns)
        elif step.array:
            # Include the group assignments
            item_name = step.array.item.__name__.lower()
            item_code = name_col(step.array.item).split(".")[1]
            groups = db.query(
                func.count(step.array.link.group).label("group_count"), 
                step.array.link.request
            ).group_by(step.array.link.request).subquery()
            items = db.query(
                func.aggregate_strings(getattr(step.array.item, item_code), ",").label("items"),
                step.array.link.request
            ).filter(step.array.item.id==getattr(step.array.link,item_name)).\
                group_by(step.array.link.request).subquery()
            reqt = aliased(Request) # need alias here because inputs could also contain a Request
            self.query = db.query(reqt, step.out, *step.ins, groups, items).\
                filter(reqt.project == step.project).\
                filter(reqt.action == action_id). \
                filter(reqt.id == step.out.request).\
                filter(reqt.step == step.name).\
                filter(*insq).\
                    join(groups, step.out.request == groups.c.request, isouter=True).\
                    join(items, step.out.request == items.c.request, isouter=True)
            #log(f"Query: {self.query}, action_id: {action_id}")
            self.items = self.query.all()
            self.n = len(self.items)
            self.columns = self.columns | {
                "mapped": "items",
            }
            #del self.columns[item_name]
            self.rep = Rep.join(("req", Request), step.out, *step.ins, {"groups": int, "req_id": int, "items": str})(self.columns)


    def htable(self, ht: Context, pick: bool = True, filter: callable = None):
        """Compose the HTML table."""
        sym = ":" if self.n > 0 else "."
        stations = ht.db.query(Station).filter(Station.step == self.step.name).all()
        items = self.items
        if filter is not None:
            items = [r for r in items if filter(r)]
        act_rep = rep(ActionSelect).options("station", stations)
        or_more = "" if self.step.single else " or more"
        return ht(
            ht.h2(f"{self.n} {word_s('request', self.n)} queued"+sym),
            ht.p("Double-click on a request for details."),
            ht.when(self.n != 0)(
                table(self.items, self.rep, status=req_row_status)(id="request", href=ht.url(f"{self.step.path}/req")),
                ht.when(pick)(
                    ht.h3(f"Pick one{or_more} requests for {word_ing(self.step.name)}: "+\
                        f"(select in the above table to populate the form)"),
                    ht.rform(f"{self.step.path}/act", act_rep, tag="submit-request"), ht.hr
                ),
                ht.when(self.step.bot)(
                    ht.h3(f"{word_ing(self.step.name).capitalize()} is an automated action. A bot will pick up requests.")
                )
            ),
        )
    
class BusyActions(DataTable):
    # Map the query columns to table columns for the GUI
    columns = {
        "id": "Action.id",
        "requests": "count",
        "operator": "Action.operator",
        "station": "Action.station",
        "started": "Action.started",
    }
    rep = Rep.join({"count": int, "action": int}, Action, {"status": str})(columns | {"status": "status"})
    
    def __init__(self, step: Step, db: Session):
        self.step = step
        
        # Use specific columns if they are defined in the subclass
        if hasattr(self.__class__, "columns_busy"): 
            self.columns = self.__class__.columns_busy
        # Generate the item rep
        self.rep = BusyActions.rep

        # Query the busy requests for this step.
        # subquery so we can have a request count
        action = db.query(func.count(Request.id).label("req_count"), Request.action).group_by(Request.action).subquery()
        # only list requests that have not finished
        subq = ~db.query(Result).filter(Action.id == Result.id).exists()
        # Now, the query
        self.query = db.query(action, Action, ActionTag.payload).\
            join(ActionTag, and_(
                ActionTag.action == Action.id, 
                ActionTag.name == 'status'
            ), isouter= True).\
            filter(action.c.action == Action.id).\
            filter(Action.project == step.project).\
            filter(Action.step == step.name).\
            filter(subq)
        self.items = self.query.all()
        self.n = len(self.items)
    
    def mine(self, ht: Context):
        # Any of these ours?
        return sum([1 for _, _, a, _ in self.items if a.operator == ht.user.name])
    
    def htable(self, ht: Context):
        # Compose the HTML table
        form = None
        if self.n > 0: 
            form = \
                ht.p("Double-click on an action for details.") +\
                table(self.items, self.rep)(id="action", href=ht.url(f"{self.step.path}/act") )
            if self.n == 1: 
                form += self.step.finish_form(ht, ActionState(self.step, ht.db, self.items[0][2]))
        sym = ":" if self.n > 0 else "."
        return ht.h2(f"{self.n} {word_s('action', self.n)} in progress"+sym) + form

class DoneActions(DataTable):
    columns = BusyActions.columns | {
        "finished": "Result.ended",
        "result": "Result.result"
    }
    
    def __init__(self, step: Step, db: Session):
        self.step = step
        missing = 561
        
        # Use specific columns if they are defined in the subclass
        if hasattr(step.__class__, "columns_done"): 
            self.columns = step.__class__.columns_done
        # Generate the item rep
        self.rep = Rep.join({"count": int, "action": int}, Action, Result)(DoneActions.columns)
        
        # subquery so we can have a request count
        action = db.query(func.count(Request.id).label("req_count"), Request.action).group_by(Request.action).subquery()
        if missing:
            log(f"Looking for: {missing}")
            for x in db.query(action).all():
                if x.action == missing:
                    log(f"  Found in action: {x}")
            for x in db.query(Action).all():
                if x.id == missing:
                    log(f"  Found in Action: {x.id}, step={x.step}/{step.name}")
            for x in db.query(Result).all():
                if x.id == missing:
                    log(f"  Found in Result: {x}")
                    
        # query for finished actions
        self.query = db.query(action, Action, Result).\
            filter(action.c.action == Action.id).\
            filter(Action.step == step.name).\
            filter(Result.id == Action.id)
        self.items = self.query.all()
        self.n = len(self.items)
        
        if missing:
            log(f"DoneActions: {self.n} for {step.name}")
            for x in self.items:
                if x[2].id == missing: 
                    log(f"  Found in items: {x}")
    
    def htable(self, ht: Context):
        return ht(
            ht.h2(f"{self.n} {word_s('action', self.n)} completed:"),
            ht.when(self.n>0)(
                ht.p("Double-click on an action for details."),
                table(self.items, self.rep)(id="result", href=ht.url(f"{self.step.path}/act"))
            )
        )

            
class FlowStep:
    """ Baseclass for a step in a workflow. 
        Provides gui and manages the request/action/result cycle. 
    """
    def __init__(self, step: str, out: type, project: str = "production"):
        self.name = step
        self.path = prefix+"rar/"+step
        self.router_path = "rar/"+step
        self.project = project
        self.bot = False
        
        # Get the input types from the fields of the output type
        self.outrep = rep(out)
        #log(f"Rep: {out} -> {self.outrep}")
        rs = list(self.outrep.fields.keys())
        if rs[0] != "request": raise ValueError(f"First field must be 'request', not {rs[0]}")
        #self.ins = [r.typ for r in rs[1:] if issubclass(r.typ, Base)]
        self.inreps = {k: r for k, r in self.outrep.fields.items() 
            if issubclass(r.typ, Base) and k != "request"
        }
        
        self.ins = [r.typ for r in self.inreps.values()]
        # for m in self.ins: 
        #     log(f"  Input: {m}")    
        self.out = out
        
        # Properties to be set in the subclass
        for attr in [
            "multiple", # which inputs allow multiple selections
            "once",     # which inputs can only be acted on once
            "filters",  # filters for eligible inputs
            "barcodes", # barcode modes, "check" or "enter"
        ]: 
            if not hasattr(self, attr): setattr(self, attr, set())
        
        # Other properties to be defaulted
        if not hasattr(self, "ResultSubmit"): self.ResultSubmit = ResultSubmit
        if not hasattr(self, "single"): self.single = False
        if not hasattr(self, "array"): self.array = None

        # Helpful grammar construct
        if self.ins:
            self.what = word_list(
                [word_s(m.__name__.lower(), 1+(m.__name__.lower() in self.multiple)) for m in self.ins]
            )
        else:
            self.what = "request"
        
        # Reps for the request form
        time_rep = ObjRep.from_dict("ReqTimes", schedule=datetime, deadline=datetime)
        out_rep = ObjRep.from_model(out) - "request"
        self.other_cols = {k: out.__name__+"."+k for k in out_rep.fields if k not in self.inreps}
        if self.array:
            # Include an opportunity to force a plate map for single items
            self.req_rep = out_rep | rep({"force_map": bool}) | time_rep
        else:
            self.req_rep = out_rep | time_rep

    def available(self, ht: Context, k:str, m: Base):
        """Get the available items for a model."""
        mn = m.__name__.lower()
        log(f"Available: {k}: {m.__name__} {k in self.once}")
        q = ht.db.query(m)
        if k in self.once:
            # Skip the ones that were already used
            subq = ~ht.db.query(m, self.out).\
                filter(getattr(self.out, k) == key_col(m)).exists()
            log(f"Subquery: {subq}")
            q = q.filter(subq)
        if k in self.filters:
            # apply filter provided by subclass
            q = q.filter(self.filters[k])
        return q
               
    def summary(self, ht: Context):
        """Progress summary"""
        k, m = list(self.inreps.items())[0]
        return {
            "step": ht.link(f"{self.path}/view")(self.name),
            "available": self.available(ht, k, m.typ).count(),
            "queued": Requests(self, ht.db).query.count(),
            "busy": BusyActions(self, ht.db).query.count(),
            "done": DoneActions(self, ht.db).query.count()
        }
    
    def links(self, ht: Context):
        """Additional links for the view page. To be overridden by subclasses."""
        ls = []
        if self.array is not None:
            ls.append(ht.link(f"{self.array.path}/view")("Manage plate maps"))
        return ls
                
    def logs(self) -> List[File]:
        """Logs for this step. To be overridden by subclasses."""
        return []
    
    def collect_data(self, ht: Context, state: 'ActionState'):
        """Collect data for this action. Can be overridden by subclasses.
             returns None if all data is collected, else returns html for a collection form.
        """
        if hasattr(self.ins[0], "barcode") and len(self.barcodes)>0:
            brep = None
            bdone = True
            if state.requests.n > 0:
                # get the input instances (r is a tuple)
                xs = [r[2] for r in state.requests.items]
                
                # Read existing barcodes if we're only filling, not checking
                if "check" not in self.barcodes:
                    for i, x in enumerate(xs):
                        if x.barcode is not None: state.barcodes[i] = str(x.barcode)
                # Get already checked codes from state
                bdone = all([c != "" for c in state.barcodes])
                
                # Build the rep
                d = {f"{name_val(x)}": str for x in xs} | {"action_id": int}
                brep = ObjRep.from_dict("Barcodes", **d).hidden("action_id", state.action.id)
                # Set prefills
                for i, n in enumerate([k for k in brep.fields.keys() if k!="action_id"]):
                    if state.barcodes[i] != "": 
                        brep = brep.default(n, state.barcodes[i])
                log(f"Barcodes: {brep}")
            if not bdone: return ht(
                ht.h3("Please scan the barcodes of the items you are processing:"),
                ht.rform(f"{self.path}/barcodes", brep)
            )
        return None    

    def finish_form(self, ht: Context, state: 'ActionState'):
        """Form to complete the action"""
        # Skip the form for bots
        if self.bot: 
            return ht(
                ht.h3("This is an automated step. No user action required."),
                ht.h3("Hopefully, a bot will get around to it, soon!"),
                ht.bt()
            )
        
        # See if data needs to be collected
        ret = self.collect_data(ht, state)
        # Get barcodes rep, if any
        if ret is not None: 
            return ret
        return ht(
            ht.h3(f"When you have completed the {word_ing(self.name)}, please fill out the below form and submit."),
            ht.rform(f"{self.path}/finish", state.result_rep)(enctype="multipart/form-data")
        )

    #
    #   Overview of all requests for this step in the workflow
    #
    async def view(self, ht: Context = Depends(context)):
        """View outstanding requests"""
        
        # make sure the step is in the database
        self.check_db(ht)
        
        queued = Requests(self, ht.db)
        busy   = BusyActions(self, ht.db)
        done   = DoneActions(self, ht.db)
        
        ret = ht.page(f"Overview of {self.name} Requests",
            # Title
            ht.h1(f"{word_ing(self.name).capitalize()} Workflow"),
            # Links
            *[link+ht.br for link in self.links(ht)],
            ht.link(f"{self.path}/req")(f"Request {self.what} for {word_ing(self.name)}"),
            # All the tables
            queued.htable(ht, pick= busy.mine(ht)==0 and not self.bot), 
            busy.htable(ht), 
            done.htable(ht)
        )
        return ret.pretty()
    
    #
    #   Create a new request
    #
    async def req(self, ht: Context = Depends(context)):
        """Present options to user for requesting a new action."""
        ret = ht.page(f"Request {self.what} for {word_ing(self.name)}", 
            ht.h2(f"Pick {self.what} to be queued for {word_ing(self.name)}:"),
            *[
                table(self.available(ht, k, r.typ).all(), rep(r.typ))(id=k) + ht.br 
                for k, r in self.inreps.items() if not hasattr(r.typ, "__enum__")
            ],
            ht.rform(f"{self.path}/req", self.req_rep, tag="submit-form")
        )
        return ret.pretty()
    
    def input_list(self, ht: Context, req_id, r, k):
        xs = ht.db.query(self.out, r.typ).\
            filter(getattr(self.out, k) == key_col(r.typ)).\
            filter(self.out.request == req_id).\
            all()
        xs = [x[1] for x in xs]
        log(f"  {k}: {r.typ} {len(xs)}")
        log(f"      {r}")
        ret = ht.h3(f"{word_s(r.typ.__name__, len(xs)).capitalize()}:")
        ret += table(xs, r)(id=k)
        return ret
        
    def req_pmap (self, ht: Context, req_id: int, k: str, r: Rep):
        ts = ht.db.query(self.array.link, r.typ).\
            filter(self.array.link.request == req_id).\
            filter(getattr(self.array.link, k) == key_col(r.typ)).\
            all()
        if len(ts) == 0:
            ret = self.input_list(ht, req_id, r, k)
            ret += ht.p(f"  Only one {k} was assigned and no plate map exists.")
        else:
            xs = [t[1] for t in ts]
            ret = ht.h3(f"{word_s(k).capitalize()} should be arrayed on the plate according to the following mapping:")
            ret += table(xs, r)(id=k)
            
            # Get plate map
            pm = ht.db.query(PlateMap, MapGroup, self.array.link).\
                filter(self.array.link.request == req_id).\
                filter(self.array.link.group == MapGroup.id).\
                filter(PlateMap.id == MapGroup.plate_map_id).\
                first()[0]
            log(f"Plate map: {pm}")
            
            ret += ht.h3("Group assignments:")
            gmap = {g.id: i+1  for i, g in enumerate(pm.map_groups)}
            amap = {name_val(t[1]): f"Group {gmap[t[0].group]}" for t in ts}
            ret += table([amap])
            log(f"  Group assignments: {amap}")
            
            ret += plate_view(ht, pm)
        return ret
        
    async def req_id(self, request_id: int, ht: Context = Depends(context)):
        """View a specific request."""
        request = ht.db.get(Request, request_id)
        requests = Requests(self, ht.db, request.action)
        items = [r for r in requests.items if r[0].id == request_id]
        log(f"Request: {request_id}, {request.action}, {len(items)}/{requests.n} in action")
        ireps = list(self.inreps.items())   
        ret = ht.div(
            ht.h2(f"Request {request_id} for {word_ing(self.name)}"),
            table(items, requests.rep),
            ht.h2(f"{word_s('Input', len(ireps))}:"),
        )
        mapi = None
        for i, (k, r) in enumerate(ireps):
            if hasattr(r.typ, "__enum__"): continue
            if self.array and self.array.item is r.typ: 
                mapi = i
                continue
            ret += self.input_list(ht, request_id, r, k)
        if mapi is not None:
            k, r = ireps[mapi]
            ret += self.req_pmap(ht, request_id, k, r)
        return ht.page(f"Request {request_id}", ret).pretty()
    
    def combine_inputs(self, db: Session, form: dict):
        """Combine the input models combinatirially into requests.
            form: the form data
        """
        # Get the input models from the database. 
        # When there's multiple selections, form the outer product
        mapped = None   # if we're mapping one entity multiple to a plate
        xsbuf = []
        def combi(inps: list[Tuple[str,Base]]):
            nonlocal mapped, xsbuf
            """Find all combinations of the input models."""
            if len(inps)==0: return [[]]    # base case for recursion
            name, inp = inps[0]
            if len(form[name]) == 0: 
                raise ValueError(f"Please select a {name}")
            ids = form[name].split(",")
            mpd = False
            force_map = (
                ("force_map" in form and form["force_map"] == "on") or
                ("plate_map" in form and form["plate_map"] != "")
            )
            if self.array and self.array.item is inp and (len(ids)>1 or force_map):
                log(f"Mapping:  {len(ids)} {name}, force {form['force_map']}")
                # We're mapping this entity to a plate
                if Plate not in self.ins:
                    log("Inputs: "+str(self.inreps))
                    for t in self.ins:
                        log(f"  {t}")
                    raise Exception(f"Cannot array multiple {word_s(inp.__name__)}: No plate input")
                if mapped is not None:
                    log(f"Mapped: {mapped}")
                    raise Exception(f"Multiple {word_s(inp.__name__)} selected for mapping.")
                mpd = True
                mapped = (name, inp)
            if len(ids)>1 and name not in self.multiple and not mpd: # not all inputs allow multiples
                # See if we are mapping one set of inputs to a plate
                raise ValueError(f"Multiple {word_s(inp.__name__)} selected for {name}: {ids}")
            # Fill in the list of inputs for this level, but only once
            k = len(self.inreps) - len(inps)
            if len(xsbuf)==k:
                log(f"Getting {name} xs, {len(xsbuf)}<{k}: {ids}")
                xsbuf.append([db.query(inp).get(id) for id in ids])   # get items from db
            # combine with the rest of the inputs
            if mpd:
                # Single request for multiple items
                return [[xsbuf[k][0], *ys] for ys in combi(inps[1:])]
            return [[x, *ys] for ys in combi(inps[1:]) for x in xsbuf[k]]
        
        # Initialize the requests
        krs = [(k, r.typ) for k, r in self.inreps.items()]
        xss = combi(krs)
        items = None
        plates = None
        if mapped is not None: 
            items = xsbuf[self.ins.index(mapped[1])]
            plates = xsbuf[self.ins.index(Plate)]
        # krs: input reps with their names
        # xss: all combinations of mltiply selected input items
        # mapped: if we're mapping one input, its name and rep
        # items: the items for the mapped input
        return krs, xss, mapped, items, plates
        
    async def req_post(self, request: HttpRequest, ht: Context = Depends(context)):
        """Create a new request for action on this step."""
        
        # Get the request input data
        form = await request.form()
        log(f"Request Form: {form}")
        
        # Get the input models from the database. 
        # When there's multiple selections, form the outer product
        try:
            krs, xss, mapped, items, plates = self.combine_inputs(ht.db, form)
            log(f"Requests: {len(xss)}, mapped: {mapped}")
            if mapped:
                pf = None
                for p in plates:
                    if pf is None: pf = p.plate_format
                    elif p.plate_format != pf: 
                        raise ValueError(f"Plates need to all have the same format, seen {p.plate_format.name} and {pf.name}.") 
                if "plate_map" not in form:
                    # Ask user for mapping information, to be reposted here
                    return self.array.mapped_requests(ht, 
                        step= self, form= form, back= f"{self.path}/req", 
                        xss= xss, mapped= mapped, items=items, format=pf
                    )
                # Get the plate map from the form
                plate_map_id = form["plate_map"]
                if plate_map_id == "":
                    raise ValueError("Please select a plate map")
                if "," in plate_map_id:
                    raise ValueError("Please select only one plate map")
                pmap = ht.db.get(PlateMap, plate_map_id)
                if pmap.plate_format != pf:
                    raise ValueError(f"Plate map format must be {pf.name}, not {pmap.plate_format.name}")
                ng = len(pmap.map_groups)
                log(f"Plate map: {pmap}")
                assign = []
                gs = {}
                for x in items:
                    gi = int(form[name_val(x)])
                    if gi < 1 or gi > ng:
                        raise ValueError(f"Group number {gi} out of range 1-{ng} for {name_val(x)}")
                    if gi in gs:
                        raise ValueError(f"Group {gi} assigned multiple times for {name_val(x)} and {gs[gi]}")
                    gs[gi] = name_val(x)
                    g = pmap.map_groups[gi-1]
                    log(f"  group {gi}: {x} -> {g}")
                    assign.append((g, x))
        except ValueError as e:
            return ht.error(ht.p(str(e)))
        # Store requests in the database
        for xs in xss:
            cfs = {k: key_val(x) for k, x in zip([k for k, _ in krs], xs)}
            #cfs = {x.__class__.__name__.lower(): key_val(x) for x in xs}
            ofs = {k: v for k, v in form.items() if k in self.out.__dict__ and k not in cfs}
            log(f"Out: {cfs} {ofs}")
            with ht.transact():
                # do the transaction
                req = self.new_request(ht.db, self.name)
                out = self.out(request=req.id, **cfs, **ofs)
                flush(ht.db, out)
                if mapped:
                    for g, x in assign:
                        ln = self.array.link(request=req.id, **{mapped[0]: x.id, "group": g.id})
                        ht.db.add(ln)
            
        return RedirectResponse(url=f"/{self.path}/view/", status_code=302)        
    
    def resultSubmit(self, ht: Context, result: Result):
        """Override this to provide more results."""
        return ResultSubmit(action_id=result.id, comment=result.comment, result=result.result)
        
        
    #
    #   View status and detail of an action
    #
    def act_view(self, ht: Context, state: 'ActionState'):
        """Show this page when an action is selected."""
        # Get the result, if any
        result = self.resultSubmit(ht, state.result) if state.result is not None else None
        
        # some wordsmithing
        what = f"{word_ing(self.name)} action"
        how = "completed"
        if result is None: how = "in progress"
        who = "you"
        if state.action.operator != ht.user.name: who = state.action.operator
        desc = f"{what.capitalize()} {how} by {who}"
        if result is None and who == "you": desc = f"You are currently working on a {what}"
        
        # Subclass specific results
        extra = None
        if hasattr(self, "act_view_extra"):
            extra = self.act_view_extra(ht, state)
                    
        # Compose the page
        # List action info and requests
        ret = ht.h3(desc+":")
        ret += table([(state.requests.n, 1, state.action, state.tags.get('status'))], BusyActions.rep)
        ret += ht.h3(f"for the following {word_s('request', state.requests.n)}:")
        ret += ht.p("Double-click on a request for details.")
        ret += table(state.requests.items, state.requests.rep)(id="request", href=ht.url(f"{self.path}/req"))
        # Plate map, if any
        if self.array:
            req = state.requests.items[0][0]
            [(k, r)] = [(k, r) for k, r in list(self.inreps.items()) if r.typ is self.array.item]
            ret += self.req_pmap(ht, req.id, k, r)
        # Form to complete the action, if applicable
        if result is None and state.action.operator == ht.user.name:
            ret += self.finish_form(ht, state)
        # Action tags, if any
        if state.tags:
            ret += ht.h3("with tags:") + table(state.tags)
        # Action results, if any
        if result is not None:
            ret += ht.h3("with result:") + table(result)
        # Action logs, if any
        # if self.logs():
        #     ret += ht.h3("with logs:") + table(result)
        if extra is not None:
            ret += extra
        ret += ht.br + ht.link(f"{self.path}/view")(f"Return to {self.name} overview")
        return ht.page(f"{what.capitalize()} {how}", ret)
    
    async def act(self, ht: Context = Depends(context)):
        """Completion form for current action."""
        acts = BusyActions(self, ht.db)
        if acts.n == 0: 
            return ht.page(f"No {self.name} actions in progress").pretty()
        elif acts.n == 1: 
            return self.act_view(ht, ActionState(self, ht.db, acts.items[0][2])).pretty()
        else:
            return ht.page(f"{acts.n} {word_s('action', acts.n)} in progress", 
                acts.htable(ht)
            ).pretty()
            
    async def act_id(self, action_id: int, ht: Context = Depends(context)):
        """Completion form for current action."""
        return self.act_view(ht, ActionState(self, ht.db, action_id)).pretty()
        
    def new_action(self, db: Session, user: str, station: str, reqs: List[Request]):
        """Create a new action for the requests."""
        if self.single and len(reqs) > 1:
            raise ValueError(f"Only one request allowed for {self.name}")
        sop = db.query(Sop).filter(Sop.step == self.name).first()
        if sop is None: 
            # create a dummy SOP if needed
            sop = Sop(
                name=word_ing(self.name),
                description=f"How to {self.name}. Dummy SOP for now.",
                step=self.name,
                bot=False,
                version="1.0.0",
                added=datetime.now(pytz.utc),
                url="TBD"
            )
            with transact(db): db.add(sop)
        st = db.query(Station).filter(Station.name == station).first()
        if st is None:
            # create a dummy station if needed
            st = Station(
                name=station,
                step=self.name,
                description=f"Station for {word_ing(self.name)}",
            )
            log(f"Station: {st.name} {st.step} {st.description}")
            with transact(db): db.add(st)
        log(f"Requests: {reqs}")
        act = Action(
            project = self.project,
            step=self.name,
            operator=user,
            station=st.name,
            sop=sop.name,
            started=datetime.now(pytz.utc)
        )
        log(f"New action with {len(reqs)} requests: {act}")
        with transact(db):
            flush(db, act)
            for req in reqs:
                db.refresh(req)
                if req.action is not None:
                    db.rollback()
                    raise ValueError(f"Request already has action")
                req.action = act.id
        return act

    class ActSel(rep(ActionSelect).form_parser()): pass
    
    async def act_post(self, input: ActSel = Depends(), ht: Context = Depends(context)):
        """Start action on a request."""
        rids = [int(id) for id in input.request_id.split(",")]
        if len(rids) == 0: return ht.error(ht.p("Please select a request."))
        reqs = ht.db.query(Request).filter(Request.id.in_(rids)).all()
        self.new_action(ht.db, ht.user.name, input.station, reqs)
        return RedirectResponse(url=f"/{self.path}/act/", status_code=302)
    
    #
    #   Check barcodes
    #
    async def barcodes_post(self, request: HttpRequest, ht: Context = Depends(context)):
        # Get the request submission data and action state
        form = await request.form()
        log(f"Result: {input}")
        state = ActionState(self, ht.db, int(form["action_id"]))

        # Check and update barcodes
        if state.requests.n > 0 and hasattr(self.ins[0], "barcode"):
            no_bc = []      # no barcode scanned
            bad_bc = []     # malformed barcode (not a number or not divisible by 11)
            wrong_bc = []
            fill = []
            bcs_in = []
            for _, _, inp, *_ in state.requests.items:
                bc_in = form[name_val(inp)]
                bcs_in.append(bc_in)
                log(f"  code: {name_val(inp)} '{inp.barcode}' <- '{bc_in}'")
                
                try: 
                    c = int(bc_in)
                    bad = c<=0 or c % 11 != 0
                except: 
                    bad = True
                
                if bc_in=="": no_bc.append(inp)
                elif bc_in=="no barcode": pass    # Temporary bypass for legacy plates with no barcode
                elif bad: bad_bc.append(inp)
                elif inp.barcode is not None and bc_in != str(inp.barcode): wrong_bc.append(inp)
                else: pass
                
                if inp.barcode is None and not bad: fill.append(inp)
            #log(f"Barcodes: {no_bc} | {bad_bc} | {wrong_bc}")
            
            # Report barcode entry errors
            if len(bad_bc) > 0 or len(wrong_bc) > 0:
                return ht.error(
                    ht.h2("Barcode error"),
                    ht.when(len(bad_bc) > 0)(
                        ht.p("Some barcodes are malformed:"),
                        ht.ul(*[
                            ht.li(f"{name_val(inp)} ({inp.barcode}): {bc_in}") 
                            for inp in bad_bc
                        ])
                    ),
                    ht.when(len(wrong_bc) > 0)(
                        ht.p("Some barcodes don't match the plates:"),
                        ht.ul(*[
                            ht.li(f"{name_val(inp)} ({inp.barcode}): {bc_in}") 
                            for inp in wrong_bc
                        ])
                    )
                )

            # Update action state
            for i, bc in enumerate(bcs_in):
                if bc != state.barcodes[i]:
                    state.barcodes[i] = bc
            state.update_barcodes()
            
            # Fill in missing barcodes
            log(f"Fill: {fill}")
            if len(fill) > 0:
                bad = []
                with ht.transact():
                    for inp in fill:
                        bc = int(form[name_val(inp)])
                        exists = ht.db.query(self.ins[0]).filter(self.ins[0].barcode == bc).all()
                        if len(exists)>0:
                            bad.append((inp, exists[0]))
                        else:
                            inp.barcode = bc
                    if len(bad) > 0: ht.db.rollback()  # Don't update if there are duplicates
                if len(bad) > 0:
                    return ht.error(
                        ht.h2("Wrong Barcode"),
                        ht.p("Some barcodes belong to other plates:"),
                        ht.ul(*[
                            ht.li(f"{name_val(inp)}: {x.barcode} belongs to {name_val(x)}") 
                            for inp, x in bad
                        ])
                    )
        # Go back to the action page
        return RedirectResponse(url=f"/{self.path}/act/{state.action.id}/", status_code=302)

    #
    #   Finish actions
    #    
    async def finish_action(self, db: Session, state: 'ActionState', input: ResultSubmit):
        """Finish the action."""
        if db.query(Result).filter(Result.id == input.action_id).one_or_none():
            raise UserError(f"Action {input.action_id} is already finished.")
        with transact(db):
            res = Result(
                id=input.action_id,
                ended=datetime.now(pytz.utc),
                result=input.result.name,
                comment=input.comment
            )
            db.add(res)
            if res.result=='pass':
                for _, out, *_ in state.requests.items: 
                    await self.next(db, input, out)
        log(f"Finished: {res}")
                                          
    async def finish_post(self, request: HttpRequest, ht: Context = Depends(context)):
        # Get the request submission data and action state
        form = await request.form()
        state = ActionState(self, ht.db, int(form["action_id"]))
        input = state.result_rep.instance(**form)
        log(f"Finish: {state.result_rep} -> {input}")
                   
        # Store results and queue next steps
        await self.finish_action(ht.db, state, input)
        return RedirectResponse(url=f"/{self.path}/act/{input.action_id}/", status_code=302)

    
    def check_db(self, ht: Context):
        """Check if the step is in the database and create it if not."""
        
        # Make step entry
        if ht.db.query(Step).filter(Step.name == self.name).first() is None:
            with ht.transact():
                ht.db.add(Step(name=self.name, description=f"Step {self.name} in the workflow."))

        # Make station
        if ht.db.query(Station).filter(Station.step == self.name).first() is None:
            with ht.transact():
                ht.db.add(Station(
                    name=self.name + " station", 
                    step=self.name,
                    description="Where we do the {word_ing(self.name)}"
                ))

    def check_db_nt(self, db: Session, step: str = None):
        """Check if the step is in the database and create it if not. 
           No transactions, used when we're already in one."""
        
        # Make step entry
        step = step or self.name
        #log(f"CHECK: Checking {step}")
        if db.query(Step).filter(Step.name == step).first() is None:
            log(f"Step {step} not in database, adding.")
            flush(db, Step(name=step, description=f"Step {step} in the workflow."))

        # Make station
        if db.query(Station).filter(Station.step == step).first() is None:
            log(f"No station for {step} in database, adding.")
            flush(db, Station(
                name=step + " station", 
                step=step,
                description="Where we do the {word_ing(step)}"
            ))

    #
    #   Utility functions for subclasses
    #
    def new_request(self, db: Session, 
        step: str, 
        schedule: datetime = datetime.min.replace(tzinfo=pytz.UTC), 
        deadline: datetime = datetime.max.replace(tzinfo=pytz.UTC)
    ):
        """Get a request for next steps."""
        # make sure the step is in the database
        self.check_db_nt(db, step)
        
        # check if step exists in db
        req = Request(
            step=step,
            project=self.project,
            created=datetime.now(pytz.utc), 
            schedule=schedule, 
            deadline=deadline
        )
        flush(db, req)
        return req
    
    def new_request_days(self, db: Session, step: str, days: Tuple[int,int] = None):
        """Get a request for next steps."""
        schedule = datetime.min.replace(tzinfo=pytz.UTC)
        deadline = datetime.max.replace(tzinfo=pytz.UTC)
        if days is not None:
            schedule = datetime.now(pytz.utc) + timedelta(days=days[0])
            deadline = datetime.now(pytz.utc) + timedelta(days=days[1])
        return self.new_request(db, step, schedule, deadline)
    
    def queue_request(self, ht: Context, inp: Base):
        """Queue a request for this step."""
        with ht.transact():
            req = self.new_request(ht.db, self.name)
            inp.request = req.id
            ht.db.add(inp)
        return req
    #
    #   All the routes in this GUI section
    #
    def route(self):
        # log(f"ROUTE: {self.router_path}")
        app.get(f"/{self.router_path}/view/", response_class=HTMLResponse)(self.view)
        app.get(f"/{self.router_path}/req/", response_class=HTMLResponse)(self.req)
        app.post(f"/{self.router_path}/req/", response_class=HTMLResponse)(self.req_post)
        app.get(f"/{self.router_path}/req/"+"{request_id}/", response_class=HTMLResponse)(self.req_id)
        app.get(f"/{self.router_path}/act/", response_class=HTMLResponse)(self.act)
        app.get(f"/{self.router_path}/act/"+"{action_id}/", response_class=HTMLResponse)(self.act_id)
        app.post(f"/{self.router_path}/act/", response_class=HTMLResponse)(self.act_post)
        app.post(f"/{self.router_path}/finish/", response_class=HTMLResponse)(self.finish_post)
        app.post(f"/{self.router_path}/barcodes/", response_class=HTMLResponse)(self.barcodes_post)
        if self.array:
            self.array.route()        


class ActionState:
    """Helper class to manage the state of an action."""
    def __init__(self, step: FlowStep, db: Session, action: Action, requests: Requests = None):
        self.step = step
        self.db = db
        if isinstance(action, int): action = db.query(Action).get(action)
        if action is None: raise ValueError(f"No action {action}")
        if action.id is None: raise ValueError(f"No id for action {action}")
        self.action = action
        if requests is not None: self.requests = requests
    
    @lazy
    def result_rep(self):
        rr = rep(self.step.ResultSubmit).hidden("action_id", self.action.id)
        
        # Get the reasons
        reasons = self.db.query(ResultReason).filter(
            sa.or_(ResultReason.step == self.step.name, ResultReason.step == None)
        ).all()
        rr.options("result", reasons)
        
        # Find defaults for inputs (if input is same in all requests, use that as default)
        for m in self.step.ins:
            k = m.__name__.lower()
            if k in rr.fields:
                s = set()
                for _, inp, *_ in self.requests.items:
                    s.add(getattr(inp, k))
                log(f"  Values: {k} {s}")
                if len(s) == 1: rr.default(k, s.pop())
        log("Result rep: ", rr)
        for k, r in rr.fields.items():
            if issubclass(r.typ, Base) and hasattr(r.typ, "__enum__") and not hasattr(r, "_options"):
                rr.options(k, self.db.query(r.typ).all())
        return rr
    
    @lazy
    def requests(self):
        if self.action.id is None: raise ValueError(f"No action id for {self.action}")
        return Requests(self.step, self.db, self.action.id)
    
    @lazy
    def result(self):
        return self.db.query(Result).filter(Result.id == self.action.id).first()
    
    @lazy
    def tags(self):
        atags = self.db.query(ActionTag).filter(ActionTag.action == self.action.id).all()
        return {a.name: a.payload for a in atags}

    @lazy
    def barcodes(self):
        bcs = [""]*self.requests.n
        if self.tags.get("barcodes") is not None:
            bcs = self.tags["barcodes"].split(",")
        return bcs
    
    def update_tag(self, name: str, value: str):
        if name in self.tags and self.tags[name] == value: 
            return  # no change
        atag = self.db.query(ActionTag).\
            filter(ActionTag.action == self.action.id).\
            filter(ActionTag.name == name).\
            all()
        if len(atag) > 1: raise ValueError(f"Multiple tags {name} for action {self.action.id}")
        with transact(self.db):
            if len(atag) == 0: 
                tag = ActionTag(action=self.action.id, name=name, payload=value)
                self.db.add(tag)
            else: 
                atag[0].payload = value
                
    def update_barcodes(self):
        v = ",".join(self.barcodes)
        log(f"Update barcodes: {v}")
        self.update_tag("barcodes", v)

class Workflow:
    """A collection of steps that make up a workflow."""
    def __init__(self, name: str, *steps: FlowStep):
        self.name = name
        self.steps = list(steps)
        self.router_path = "rar/"+name.lower()
        self.path = prefix+"rar/"+name.lower()
        
    def links(self, ht: Context):
        """Links for the nav bar."""
        return [
            ht.link(f"{self.path}")(f"Overview"),
            *[ht.link(f"{step.path}/view")(f"{word_ing(step.name).capitalize()}") for step in self.steps]
        ]
        
    async def overview(self, ht: Context = Depends(context)):
        return ht.page(f"{self.name} Workflow Overview",
            ht.h2(f"{self.name} Workflow Overview"),
            ht.p(f"The {self.name.lower()} workflow consists of {len(self.steps)} steps:"),
            ht.ul(*[ht.li(step.description) for step in self.steps]),
            table([step.summary(ht) for step in self.steps]),
        ).pretty()
        
    def route(self):
        app.get(f"/{self.router_path}/", response_class=HTMLResponse)(self.overview)        
        for step in self.steps: step.route()
        


