from enum import Enum
from sqlalchemy import ForeignKey, Float
from sqlalchemy.orm import Mapped, mapped_column
from base.database import Base


#
# Link tables for step specific request parameters.
#

#
#  Culture Workflow
#
class SeedIn(Base):
    """Inputs  for seeding a plate."""
    __tablename__ = "seed_in"
    request: Mapped[int] = mapped_column(ForeignKey("request.id"), primary_key=True)
    plate:   Mapped[int] = mapped_column(ForeignKey("plate.id"), nullable=False)
    donor:   Mapped[int] = mapped_column(ForeignKey("donor.id"), nullable=False)

class SeedDonor(Base):
    """Link table for donors to seeding requests."""
    __tablename__ = "seed_donor"
    request: Mapped[int] = mapped_column(ForeignKey("seed_in.request"), primary_key=True)
    donor:   Mapped[int] = mapped_column(ForeignKey("donor.id"), primary_key=True)
    group:   Mapped[int] = mapped_column(ForeignKey("map_group.id"), nullable=False)
                                                    
class FeedIn(Base):
    """Inputs for feeding cells."""
    __tablename__ = "feed_in"
    request: Mapped[int] = mapped_column(ForeignKey("request.id"), primary_key=True)
    plate:   Mapped[int] = mapped_column(ForeignKey("plate.id"), nullable=False)
    medium:  Mapped[str] = mapped_column(ForeignKey("medium.name"), nullable=False)
    
class ImageIn(Base):
    """Inputs for imaging cells."""
    __tablename__ = "image_in"
    request: Mapped[int] = mapped_column(ForeignKey("request.id"), primary_key=True)
    plate:   Mapped[int] = mapped_column(ForeignKey("plate.id"), nullable=False)
    device:  Mapped[str] = mapped_column(ForeignKey("device.name"), nullable=False)
    #culture_day: Mapped[int]

class UploadIn(Base):
    """Inputs for image uploading."""
    __tablename__ = "upload_in"
    request:   Mapped[int] = mapped_column(ForeignKey("request.id"), primary_key=True)
    image_req: Mapped[int] = mapped_column(ForeignKey("request.id"), nullable=False)

class UploadOut(Base):
    """Outputs for image uploading."""
    __tablename__ = "upload_out"
    request: Mapped[int]   = mapped_column(ForeignKey("request.id"), primary_key=True)
    image_set: Mapped[int] = mapped_column(ForeignKey("image_set.id"), nullable=False)


# Plate quadrants
class Quadrant(Enum):
    A1 = "A1"
    A2 = "A2"
    B1 = "B1"
    B2 = "B2"
    
#
#  Assay workflow
#
class ExtractIn(Base):
    """Inputs  for supernatant exraction."""
    __tablename__ = "extract_in"
    request: Mapped[int] = mapped_column(ForeignKey("request.id"), primary_key=True)
    plate:   Mapped[int] = mapped_column(ForeignKey("plate.id"), nullable=False)

class ExtractOut(Base):
    """Outputs for supernatant extraction."""
    __tablename__ = "extract_out"
    request: Mapped[int] = mapped_column(ForeignKey("request.id"), primary_key=True)
    plate: Mapped[int]   = mapped_column(ForeignKey("supernatant.id"), nullable=False)


class DiluteIn(Base):
    """Inputs for dilution."""
    __tablename__ = "dilute_in"
    request:  Mapped[int] = mapped_column(ForeignKey("request.id"), primary_key=True)
    plate:    Mapped[int] = mapped_column(ForeignKey("plate.id"), nullable=False)
    dilution: Mapped[float] = mapped_column(Float, nullable=False)

class DiluteOut(Base):
    """Outputs for supernatant extraction."""
    __tablename__ = "dilute_out"
    request: Mapped[int] = mapped_column(ForeignKey("request.id"), primary_key=True)
    plate: Mapped[int]   = mapped_column(ForeignKey("dilution.id"), nullable=False)


class AssayIn(Base):
    """Inputs for assay."""
    __tablename__ = "assay_in"
    request:  Mapped[int] = mapped_column(ForeignKey("request.id"), primary_key=True)
    plate:    Mapped[int] = mapped_column(ForeignKey("plate.id"), nullable=False)
    quadrant: Mapped[str] = mapped_column(ForeignKey("quadrant.name"), nullable=False)
    assay:    Mapped[str] = mapped_column(ForeignKey("assay.name"), nullable=False)
    platemap: Mapped[int] = mapped_column(ForeignKey("plate_map.id"), nullable=False)


# We can create a PlateMap in the implementation of next fucntion 
class ScreenAssignmentIn(Base):
    """Inputs for screening"""
    __tablename__ = "screen_in"
    request:  Mapped[int] = mapped_column(ForeignKey("request.id"), primary_key=True)
    plate:    Mapped[int] = mapped_column(ForeignKey("plate.id"), nullable=False)
    quadrant: Mapped[str] = mapped_column(ForeignKey("quadrant.name"), nullable=False)
    platemap: Mapped[int] = mapped_column(ForeignKey("plate_map.id"), nullable=False)
    library_plate: Mapped[int] = mapped_column(ForeignKey("library_plate.id"), nullable=False)


# TODO the dose response map can have an arbitrary number of groups need to allow for a collection of treatments
class DoseResponseAssignmentIn(Base):
    """Inputs for dose response"""
    __tablename__ = "dose_response_in"
    request:  Mapped[int] = mapped_column(ForeignKey("request.id"), primary_key=True)
    plate:    Mapped[int] = mapped_column(ForeignKey("plate.id"), nullable=False)
    platemap: Mapped[int] = mapped_column(ForeignKey("plate_map.id"), nullable=False)


class DoseIn(Base):
    """Inputs for routine dosing of culture plate"""
    __tablename__ = "dose_in"
    request:  Mapped[int] = mapped_column(ForeignKey("request.id"), primary_key=True)
    plate:    Mapped[int] = mapped_column(ForeignKey("plate.id"), nullable=False)
    treatment_batch: Mapped[int] = mapped_column(ForeignKey("treatment_regiment.id"), nullable=False)

#
#  Pipeline workflow

class PipelineIn(Base):
    """Inputs for segmentation of images in image set"""
    __tablename__ = "pipeline_in"
    request: Mapped[int] = mapped_column(ForeignKey("request.id"), primary_key=True)
    image_set: Mapped[int] = mapped_column(ForeignKey("image_set.id"), nullable=False)
    pipeline_version: Mapped[int] = mapped_column(ForeignKey("pipeline_version.id"))
    
class PipelineOut(Base):
    """output for pipeline requests"""
    __tablename__ = "pipeline_out"
    request: Mapped[int] = mapped_column(ForeignKey("request.id"), primary_key=True)
    result_set: Mapped[int] = mapped_column(ForeignKey("result_set.id"), nullable=False)

class DifferentiationIn(Base):
    """Input for differentiation analysis step"""
    __tablename__ = "differentiation_in"
    request: Mapped[int] = mapped_column(ForeignKey("request.id"), primary_key=True)
    max_items: Mapped[int] = mapped_column(nullable=True, default=20)
    max_time: Mapped[int] = mapped_column(nullable=True, default=28800)  # 8 hours in seconds
