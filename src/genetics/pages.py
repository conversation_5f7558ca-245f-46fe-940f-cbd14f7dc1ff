from fastapi import APIRouter, Depends
from fastapi.responses import HTMLResponse

from base.logs import make_logger
from base.store import cache_store
from base.reps import rep
from app.table import table

from app.layouts import Context
from app.auth import context
from genetics.gtex import GtexAllAnalysis, SqtlAnalysis, SqtlSnpAnalysis
from genetics.gwas import GwasAnalysis, Hit
from genetics.plots import ManhattanPlot, SqtlPlot, VolcanoPlot
from app.reports import Figures, Tables

log = make_logger(__name__)

router = APIRouter(prefix="/genetics")

img_cache = cache_store()
        
# Some reps
hit_rep = rep(Hit)
log(f"Hit rep: {hit_rep}")

def get_analysis(endpoint: str):
    """Get the analysis for the given endpoint."""
    global _analyses
    if endpoint not in _analyses:
        log(f"Creating new analysis for endpoint {endpoint}")
        _analyses[endpoint] = GwasAnalysis(endpoint)
    return _analyses[endpoint]
_analyses = {}

def genecard(g: str):
    """Create a link for the gene."""
    return f"https://www.genecards.org/cgi-bin/carddisp.pl?gene={g}"

def dbsnp(s: str):
    """Create a link for the dbSNP."""
    return f"https://www.ncbi.nlm.nih.gov/snp/?term={s}"

@router.get("/{endpoint}/hits", response_class=HTMLResponse)
async def hit_list(endpoint: str, ht: Context = Depends(context)):
    gwas = get_analysis(endpoint)
    of_interest = [
        gwas.gene_loci[gn].hit for gn in ["MKNK1"]
        if gn not in set(h.gene for h in gwas.hits)
    ]
    hits = of_interest + gwas.hits
    title = f"GWAS hits for {endpoint}"
    rep = hit_rep.render("gene", lambda x: ht.a(href=genecard(x))(x))
    ret = ht.page(title,
        ht.h2(title),
        ht.h3(ht.a(href="https://pubmed.ncbi.nlm.nih.gov/30124842/")("From the GIANT Consortium")),
        ht.h3(f"{len(gwas.hits)} genes are significantly associated (p<{gwas.thresh:.5g}) in this study."),
        ht.p("""
            Click on the gene name link to see the Gene Card for that gene.
            Double-click on a row to see the GWAS locus for that gene.
        """),
        table(hits, rep)(id="hits", href=ht.url(f"genetics/{endpoint}")),
    )
    return ret.pretty()

@router.get("/{endpoint}/{gene}", response_class=HTMLResponse)
async def locus_hits(endpoint: str, gene: str, ht: Context = Depends(context)):
    log(f"Selected gene: {gene}")
    gwas = get_analysis(endpoint)
    locus = gwas.gene_loci[gene]

    fig = Figures(ht)
    tab = Tables(ht)
    
    title = f"GWAS locus for {gene} with {endpoint}"
    table_caption = f"GWAS data for {len(locus.data)} SNPs in or near the {locus.gene.name} locus"
    ret = ht.page(title,
        ht.h2(title),
        ht.a(href=genecard(gene))(f"Gene Card for {locus.gene.name}"),
        fig(VolcanoPlot(locus)),
        fig(ManhattanPlot(locus)),
        tab(locus.data, caption=table_caption,
            cols={"SNP":"SNP", "Position":"POS", "Chrom":"CHR", 
                  "Tested": "Tested_Allele", "Other": "Other_Allele",
                  "Freq": "Freq_Tested_Allele_in_HRS",
                  "*": None},
            id = "gwas-snps", href = ht.url(f"genetics/{endpoint}/{gene}")
        ),
    )
    return ret.pretty()

@router.get("/{endpoint}/{gene}/{snp}", response_class=HTMLResponse)
async def snp_hit(endpoint: str, gene: str, snp: str, ht: Context = Depends(context)):
    log(f"Selected snp: {gene}/{snp}")
    gwas = get_analysis(endpoint)
    locus = gwas.gene_loci[gene]
    gtex = GtexAllAnalysis(locus.gene)
    title = f"GWAS SNP {endpoint} {gene} {snp}"
    ret = ht.page(title,
        ht.h2(title),
        ht.a(href=dbsnp(snp))(f"dbSNP for {snp}"), ht.br,
        ht.a(href=genecard(gene))(f"Gene Card for {locus.gene.name}"),
        ht.h3(f"GTEx sQTLs for {gene}"),
        *[
            ht.a(href=ht.url(f"genetics/{endpoint}/{gene}/{snp}/{t}"))(t) + ht.br
            for t in gtex.tissues if t.startswith("Adipose")
        ]
    )
    return ret.pretty()

@router.get("/{endpoint}/{gene}/{rs_id}/{tissue}", response_class=HTMLResponse)
async def sqtl_snp(
    endpoint: str, gene: str, rs_id: str, tissue: str, 
    ht: Context = Depends(context)
):
    gwas = get_analysis(endpoint)
    locus = gwas.gene_loci[gene]
    sqtl = SqtlAnalysis(gwas, tissue, locus.hit)
    #gtex = GtexAllAnalysis(locus.gene)
    snp = sqtl.snp(rs_id)
    if snp is None:
        log(f"SNP {rs_id} not found in sQTL analysis for {tissue}")
        return ht.error("SNP Not Found", ht.h2(f"SNP {rs_id} not found in sQTL analysis for {tissue}"))
    analysis = SqtlSnpAnalysis(sqtl, snp)
    
    fig = Figures(ht)
    tab = Tables(ht)

    title = f"GTEX sQTL Analysis for {gene}:{snp}"
    ret = ht.page(title,
        ht.h2(title),
        ht.h3(f"Data from {tissue} tissue"),
        ht.p(f"{len(analysis.phenotypes)} phenotypes"),
        fig(SqtlPlot(analysis)),
        tab(analysis.pairs)
    )
    return ret.pretty()
