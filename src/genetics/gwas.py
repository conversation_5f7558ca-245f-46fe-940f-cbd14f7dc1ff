import re
import sys
import traceback
from typing import List, NamedTuple
import numpy as np
import pandas as pd
from tqdm import tqdm
from base.io import cached, open_url
from base.store import Store, cache_store
from base.util import lazy, lform, perc
from base.logs import make_logger
from genetics.schema import Locus, Snp

log = make_logger(__name__)

chr_map = {f"NC_{i:06d}.":f"{i}" for i in range(1, 23)} | \
    {"NC_000023.":"X", "NC_000024.":"Y", "NC_012920.":"M"} | \
    {"NT_187562.":"7"}   # Special case for NT_187562.1 which is a patch for chr7
#log(f"Chromosome map: {chr_map}")

class Hit(NamedTuple):
    """Represents a GWAS hit."""
    gene: str
    locus: str
    endpoint: str
    snps: int
    p_value: float
    score: float
    rank: int    

class GwasLocus():
    """Represents a GWAS locus, which is a gene and its associated SNPs."""
    gwas: 'GwasAnalysis'
    gene: Locus
    snps: list[str]
    
    def __init__(self, gwas: 'GwasAnalysis', gene: Locus, snps: List[str]):
        self.store = gwas.store / gene.name
        self.gwas = gwas
        self.gene = gene
        self.snps = snps
        self.pad = 50000  # Padding around the gene to include nearby SNPs

    def __str__(self):
        score = f"{self.hit.score:.0f}" if hasattr(self, 'hit') else "..."
        return f"{self.gene} ({len(self.snps)} SNPs), score={score}"
    
    @lazy
    def hit(self) -> Hit:
        """Get the hit information for this locus."""
        # compute the minimum p-value for the SNPs in this locus
        df = self.gwas.data
        df = df[df["SNP"].isin(self.snps)]
        if df.empty:
            return 1.0
        p = df["P"].min()
        if p == 0: p = sys.float_info.min
        try:
            rank = self.gwas.hit_loci.index(self) + 1
        except ValueError:
            log(f"Warning: {self.gene.name} not found in hit list, rank will be None")
            rank = None
        return Hit(
            gene=self.gene.name,
            locus=f"chr{self.gene.chr}:{self.gene.b}-{self.gene.e}",
            endpoint=self.gwas.endpoint,
            snps=len(self.snps),
            p_value=p,
            score=-np.log10(p),
            rank=rank
        )
    
    @lazy
    def data(self) -> pd.DataFrame:
        """Get the GWAS data for this locus."""
        df = self.gwas.mapped_data
        df = df[
            (df["CHR"].astype(str) == self.gene.chr) &
            (df["POS"] < self.gene.e+self.pad) &
            (df["POS"] > self.gene.b-self.pad)
        ]
        if df.empty:
            log(f"CHR: {self.gene.chr}, POS: {self.gene.b}-{self.gene.e}, pad: {self.pad}")
            log(f"  CHR: {df['CHR'].dtype}, POS: {df['POS'].dtype}")
            df2 = self.gwas.mapped_data
            df2 = df2[df2["SNP"].isin(self.snps)]
            print(df2.head())
            raise ValueError(f"No data found for locus {self.gene.name} ({len(self.snps)} SNPs)")
        return df
    
    @lazy
    def all_snps(self) -> List[Snp]:
        """Get the SNPs in this locus."""
        print(self.data)
        return [
            Snp(row.SNP, row.CHR, row.POS, row.Other_Allele, row.Tested_Allele) 
            for row in self.data.itertuples()
        ] 
    
    @lazy
    def other_loci(self) -> List[Locus]:
        """Get the other loci in the padded neighborhood."""
        loci = {}
        for rs_id in self.data["SNP"].values:
            loc = self.gwas.snp_loci.get(rs_id)
            if loc is not None and loc.gene.name != self.gene.name and loc.gene.name not in loci:
                if loc.gene.chr != self.gene.chr:
                    raise ValueError(f"Wrong chromosome {loc.gene.chr}!={self.gene.chr} for {rs_id} in {self.gene.name}")
                loci[loc.gene.name] = loc.gene
        return list(loci.values())

        
class GwasAnalysis():
    def __init__(self, endpoint: str = "bmi", store: Store = None):
        self.endpoint = endpoint
        supported_endpoints = ["bmi"]
        if endpoint not in supported_endpoints:
            raise ValueError(f"Endpoint {endpoint} not in {supported_endpoints}.")
        if store is None:
            store = cache_store()
        self.store = store / "gwas" / endpoint
        self.url = "https://portals.broadinstitute.org/collaboration/giant/images/c/c8/Meta-analysis_Locke_et_al%2BUKBiobank_2018_UPDATED.txt.gz"
        self.thresh = 5e-8  # Threshold for significant hits
        
    @cached("data.parquet")
    def data(self):
        """Download and return the GWAS data as a DataFrame."""
        stream = open_url(self.url, "rt")
        with stream.file as f:
            return pd.read_csv(f, sep="\t", low_memory=True)

    @cached("index-b38.parquet")
    def index_b38_full(self):
        """Load the build 38 positions for all covered SNPs from dbSNP."""
        snp_set = set(self.data["SNP"].dropna().unique())
        url = "https://ftp.ncbi.nih.gov/snp/latest_release/VCF/GCF_000001405.40.gz"  # 25 for b37
        return open_url(url, "rt").stream_csv(
            lambda row: row[2] in snp_set,
            expect_cols=[None, "POS", "ID"],
            use_cols=["ID", "POS", "CHROM", "INFO"], 
        )

    @cached("index-b38-min.parquet")
    def index_b38(self) -> pd.DataFrame:
        print(self.index_b38_full)
        df = self.index_b38_full[["ID", "POS", "CHROM", "REF", "ALT"]].copy(deep=False)
        rex = r"GENEINFO=([^:]+):"
        df["GENE"] = self.index_b38_full["INFO"].str.extract(rex)[0]
        df = df.rename(columns={"ID": "SNP"})
        # Remove duplicate Ids that are not on a full chromosome assembly
        def loc_chr(chroms):
            for c in chroms:
                if c[:10] in chr_map:
                    return c
            return None
        def same(xs):
            """Return the consensus if complete."""
            uxs = set(xs)
            if len(uxs) == 1: return uxs.pop()
            raise ValueError(f"Multiple values found: {lform(uxs, 6)}")
        def join(xs):
            """Join the values with a comma."""
            s = set()
            for x in xs:
                s |= set(x.split(","))
            return ",".join(sorted(s))
        snps = df.groupby("SNP").agg(
            CHROM=('CHROM', loc_chr),
            GENE=('GENE', same),
            REF=('REF', join),
            ALT=('ALT', join),
        ).reset_index()
        # Are any SNPs missing a chromosome?
        missing_chrom = snps.CHROM.isna().sum()
        if missing_chrom > 0:
            log(f"Warning: {missing_chrom} SNPs without a valid chromosome mapping")
            snps = snps[snps.CHROM.notna()]
        # position mapping
        pos_map = {row.SNP+"-"+row.CHROM: int(row.POS) for row in df.itertuples()}
        keys = snps["SNP"] + "-" + snps["CHROM"]
        # Are any SNPs missing a position?
        missing_pos = keys.map(pos_map).isna().sum()
        if missing_pos > 0:
            log(f"Warning: {missing_pos} SNPs without a valid position mapping")
            print(snps[keys.map(pos_map).isna()])
            raise ValueError(f"Some SNPs do not have a valid position mapping: {lform(snps[keys.map(pos_map).isna()]['SNP'], 6)}")
        snps["POS"] = keys.map(pos_map).astype("int")
        return snps

    @cached("mapped-data.parquet")
    def mapped_data(self) -> pd.DataFrame:
        """Map the SNP positions in the GWAS data to the b38 index."""
        pos_map = {row.SNP: int(row.POS) for row in self.index_b38.itertuples()}
        log(f"Mapped {len(pos_map)} SNP positions from GWAS data to b38 index")
        pos = self.data["SNP"].map(pos_map)
        log(f"  pos: {len(pos)}, missing: {pos.isna().sum()}, type: {pos.dtype}")
        log(f"  chr: {lform(self.data['CHR'].unique(), 25)}, missing: {perc(self.data['CHR'].isna().sum(), len(self.data))}")
        df = self.data[pos.notna()].copy(deep=False)
        df.loc[:,"POS"] = df["SNP"].map(pos_map).astype("int")
        return df

    @cached("gene_ids.parquet")
    def gene_ids(self) -> pd.DataFrame:
        url = "https://storage.googleapis.com/public-download-files/hgnc/tsv/tsv/hgnc_complete_set.txt"
        log(f"Downloading gene IDs from {url}")
        stream = open_url(url, "rt")
        with stream.file as f:
            df0 = pd.read_csv(f, sep="\t", usecols=['symbol', 'ensembl_gene_id'], low_memory=True)
        df = df0.dropna()
        log(f"Drop {perc(len(df0)-len(df), len(df0))} na values from gene IDs")
        return df
    
    @lazy
    def ensg_map(self) -> dict[str, str]:
        return dict(zip(self.gene_ids["symbol"], self.gene_ids["ensembl_gene_id"]))

    def snp(self, id: str) -> Snp:
        """Get a SNP by its id,, either RS or chr:pos format."""
        def cons(col):
            if len(col) == 0: return None
            if len(col) > 1: 
                if np.any(col!=col[0]):
                    raise ValueError(f"Multiple values found for {id}: {lform(col.dropna().unique(), 6)}")
            return col.iloc[0]

        if id.startswith("rs"):
            df = self.index_b38[self.index_b38["SNP"] == id]
            print(df)
            if df.empty:
                raise ValueError(f"SNP {id} not found in index")
            return Snp(rs_id=id, 
                        chr=chr_map[cons(df["CHROM"])[:10]],  # Map NC_000023.11 to chrX, etc.
                        pos=int(cons(df["POS"])), 
                        ref=cons(df["REF"]), 
                        alt=cons(df["ALT"])
            )
        elif id.startswith("chr"):
            match = re.match(r"chr(\d+|X|Y|M)_(\d+)_(\w+)_(\w+)_b38", id)
            snp = Snp(
                rs_id=id,
                chr=f"chr{match.group(1)}",
                pos=int(match.group(2)),
                ref=match.group(3),
                alt=match.group(4)
            )
            chrom = {v: k for k, v in chr_map.items()}[snp.chr]
            df = self.index_b38[
                self.index_b38["CHROM"].str.startswith(chrom) &
                (self.index_b38["POS"] == str(snp.pos))
            ]
            if df.empty:
                df = self.index_b38[
                    (self.index_b38["POS"] == str(snp.pos))
                ]
                print(df)
                print(chrom)
                raise ValueError(f"No match found for SNP {id}.")
            return snp._replace(rs_id=cons(df["SNP"]))
        else:
            raise ValueError(f"Invalid SNP ID: {id}")

    @lazy
    def gene_loci(self) -> dict[str, GwasLocus]:
        """Get a dictionary of all gene loci."""
        
        info = self.index_b38.copy(deep=False)
        gene_count = info["GENE"].notna().sum()
        log(f"Found {perc(gene_count, len(info))} rows with GENEINFO in index")
                
        # Count duplicates in info["SNP"]
        dup_counts = info["SNP"].value_counts()
        num_dups = (dup_counts > 1).sum()
        log(f"Found {num_dups} SNPs with duplicates in index_b38")
        if num_dups > 0:
            print(info[info["SNP"]=="rs2296371"])
            dup_counts_full = self.index_b38_full["ID"].value_counts()
            print(dup_counts_full[dup_counts_full > 1])

        # Group by GENE and CHR, and get the min and max POS for each group
        #   Also get list of `SNP`s
        log(f"POS type: {info['POS'].dtype}")
        loci = info.groupby(["GENE"]).agg(
            b=('POS', 'min'),
            e=('POS', 'max'),
            ids=('SNP', lambda x: list(x)),
            chrs=('CHROM', lambda xs: list(set(x for x in xs if x)))
        ).reset_index()
        loci.columns = ['gene', 'b', 'e', 'snps', 'chroms']
        multi_chrom_rows = loci[loci['chroms'].apply(lambda x: len(x) > 1)]
        log(f"Loci with more than one chromosome: {len(multi_chrom_rows)}")
        if not multi_chrom_rows.empty:
            print(multi_chrom_rows)
        def loc_chr(chroms):
            for c in chroms:
                if c[:10] in chr_map:
                    return chr_map[c[:10]]
            return None
        loci["chr"] = loci['chroms'].map(loc_chr)
        no_chr = loci[loci['chr'].isna()]
        if not no_chr.empty:
            log(f"Warning: {len(no_chr)} loci without a valid chromosome found")
            print(no_chr)
            raise ValueError("Some loci do not have a valid chromosome mapping.")
        print(loci.head())
        
        gene_loci = {}
        self.snp_loci = {} # Also index by SNP id
        for _, row in loci.iterrows():
            locus = GwasLocus(self,
                gene=Locus(
                    name=row.gene, chr=row.chr, 
                    b=int(row.b), e=int(row.e),
                ),
                snps=row.snps
            )
            gene_loci[locus.gene.name] = locus
            for snp in row['snps']:
                if snp in self.snp_loci:
                    raise ValueError(f"SNP {snp} found in multiple loci: {self.snp_loci[snp]} and {locus}")
                self.snp_loci[snp] = locus
        log(f"Found {len(gene_loci)} gene loci in GWAS data")
        return gene_loci
    
    @lazy
    def snp_loci(self) -> dict[str, GwasLocus]:
        """Get a dictionary of all Gene loci indexed by SNP id."""
        self.gene_loci  # This does the job as a side effect...
        return self.snp_loci

    @lazy
    def hit_loci(self) -> List[GwasLocus]:
        """Get the GWAS hits with p < self.thresh"""
        df = self.data
        df = df[pd.to_numeric(df["P"]) < self.thresh]
        log(f"Found {len(df)} SNP hits with p < self.thresh")
        # sort by column P
        df = df.sort_values(by="P", ascending=True)
        hits = []
        done = set()
        not_found = set()
        for row in df.itertuples():
            locus = self.snp_loci.get(row.SNP)
            if locus is None:
                not_found.add(row.SNP)
                continue
            if locus.gene.name in done:
                continue
            done.add(locus.gene.name)
            hits.append(locus)
        if not_found:
            log(f"Warning: {len(not_found)} SNPs not found in any locus: {lform(not_found, 10)}")
        log(f"Found {len(hits)} unique gene hits with p < {self.thresh}")
        return hits
    
    @cached("hits.parquet", Hit)
    def hits(self) -> List[Hit]:
        """Get the top GWAS hits."""
        hits = []
        for locus in tqdm(self.hit_loci, desc="Processing GWAS hits"):
            hits.append(locus.hit)
        return hits
