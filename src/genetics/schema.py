
from typing import NamedTuple

# This module defines data structures for representing genomic features
# such as loci, genes, and SNPs using NamedTuple for immutability and type safety.

class Locus(NamedTuple):
    """A genomic locus representing a region on a chromosome."""
    name: str  # Name or identifier for the locus
    chr: str   # Chromosome name or number
    b: int     # Start position (base pair)
    e: int     # End position (base pair)

    def __str__(self):
        # Return a string representation of the locus
        return f"{self.name} {self.chr}:{self.b}:{self.e}"

    
class Snp(NamedTuple):
    """
    Represents a single nucleotide polymorphism (SNP) in the genome.
    """
    rs_id: str   # Reference SNP cluster ID (e.g., 'rs123456')
    chr: str     # Chromosome name or number
    pos: int     # Position on the chromosome (base pair)
    ref: str     # Reference allele
    alt: str     # Alternate allele
    build: str = "b38"  # Genome build version (default: 'b38')
    
    def name(self):
        # Return a unique name for the SNP
        return f"chr{self.chr}_{self.pos}_{self.ref}_{self.alt}_{self.build}"
    
    def __str__(self):
        # Return a string representation of the SNP
        t = f" ({self.rs_id})" if self.rs_id else ""
        return f"{self.name()}{t}"
    

class GeneLocus(NamedTuple):
    """
    Represents a gene and its associated genomic locus.
    """
    name: str  # Name or identifier for the gene
    locus: Locus  # The genomic locus associated with the gene

    def __str__(self):
        # Return a string representation of the gene locus
        return f"{self.name}:{self.locus.chr}:{self.locus.b}-{self.locus.e}"
    
