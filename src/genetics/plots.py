import sys
import matplotlib.pyplot as plt
import numpy as np
from app.reports import Plot
from base.logs import make_logger

from genetics.gtex import SqtlSnpAnalysis
from genetics.gwas import GwasLocus
from genetics.schema import Locus

log = make_logger(__name__)

def set_styles(fig, ax):
    # ax.spines['right'].set_visible(False)
    # ax.spines['top'].set_visible(False)
    fig.set_size_inches(10, 4)

class VolcanoPlot(Plot):
    def __init__(self, locus: GwasLocus, labels: bool = False):
        """Create a volcano plot for a GWAS locus"""
        super().__init__(locus, "volcano")
        self.labels = labels
          
        # Title and caption information
        self.title = f"Volcano plot for associations of {locus.gene.name} with {locus.gwas.endpoint}"
        self.caption = f"""
            Association scores of SNPs in and around the {locus.gene.name} 
            gene locus for endpoint {locus.gwas.endpoint}. On the x-axis we show 
            the strength of the association (slope) and on the y-axis we show
            the significance score of the association (-log10 p-value). 
            Each point represents one of the {len(locus.data)} SNPs in the locus.
        """
        
    def plot(self):
        """Plot growth curves for one or more plates"""
        fig, ax = plt.subplots()
        slope = self.analysis.data['BETA'].values
        pval = self.analysis.data['P'].values
        # Set zeros to minimum positive float to avoid -inf in log10
        pval = np.where(pval == 0, sys.float_info.min, pval)
        
        ax.scatter(slope, -np.log10(pval), alpha=0.5, c="blue")
        ax.set_title(self.title)
        ax.set_xlabel("Slope")
        ax.set_ylabel("-log10(p-value)")
        ax.grid(True)

        # Label points above y=10
        if self.labels:
            id = self.analysis.data['SNP'].values
            sel = -np.log10(pval) > 10
            for i, s in enumerate(sel):
                if s:
                    ax.text(slope[i], -np.log10(pval[i]), id[i], fontsize=8)

        set_styles(fig, ax)
        plt.savefig(self.store.open(self.fn, "wb"))
        return fig

class ManhattanPlot(Plot):
    def __init__(self, locus: GwasLocus, labels: bool = False):
        """Create a volcano plot for a GWAS locus"""
        super().__init__(locus, "manhattan")
        self.labels = labels
        
        # Title and caption information
        self.title = f"Manhattan plot for associations of {locus.gene.name} with {locus.gwas.endpoint}"
        self.caption = f"""
            Association scores of SNPs in and around the {locus.gene.name} 
            gene locus for endpoint {locus.gwas.endpoint}. On the x-axis we show 
            the SNP location and on the y-axis we show
            the significance score of the association (-log10 p-value). 
            The location is shown relative to the first SNP annotated 
            to the gene in order of increasing basepair position.
            Each point represents one of the {len(locus.data)} SNPs in the locus.
            The shaded area indicate the range where SNPs are annotated as 
            belonging to a gene locus.
        """
        
    def plot(self):
        """Plot growth curves for one or more plates"""
        fig, ax = plt.subplots()
        tss = self.analysis.gene.b  # First annotated SNP position (lacking the TSS)
        pos = self.analysis.data['POS'].values - tss  # Adjust positions relative to TSS
        pval = self.analysis.data['P'].values
        # Set zeros to minimum positive float to avoid -inf in log10
        pval = np.where(pval == 0, sys.float_info.min, pval)
        score = -np.log10(pval)
        max_score = score.max() # Maximum score for y-axis
        log(f"MAX score: {max_score}")
        
        def gene_region(loc: Locus, color='lightgreen', alpha=0.2):
             # Don't let the regions expand the scale
            b = max(loc.b, self.analysis.gene.b - self.analysis.pad) 
            e = min(loc.e, self.analysis.gene.e + self.analysis.pad)
            ax.axvspan(b - tss, e - tss, color=color, alpha=alpha)
            ax.text((b + e - 2 * tss) / 2, max_score, loc.name, fontsize=6, ha='center', va='top', rotation=-90)
        gene_region(self.analysis.gene)
        for loc in self.analysis.other_loci: gene_region(loc, alpha=0.1)
        
        ax.scatter(pos, score, alpha=0.2, c="black")
        
        ax.set_title(self.title)
        ax.set_xlabel(f"Location (bp) relative to  chr{self.analysis.gene.chr}:{tss} (b38)")
        ax.set_ylabel("-log10(p-value)")
        ax.grid(True)

        # Label points above y=10
        if self.labels:
            id = self.analysis.data['SNP'].values
            sel = score > 10
            for i, s in enumerate(sel):
                if s:
                    ax.text(pos[i], score[i], id[i], fontsize=8)

        set_styles(fig, ax)
        plt.savefig(self.store.open(self.fn, "wb"))
        return fig

class SqtlPlot(Plot):
    def __init__(self, analysis: SqtlSnpAnalysis):
        """Create a volcano plot for a GWAS locus"""
        super().__init__(analysis, "sqtl")
        
        # Title and caption information
        gene = analysis.sqtl.locus.gene
        self.title = f"sQTL association plot for {gene.name}:{analysis.snp}"
        self.caption = f"""
            Association scores of variant {analysis.snp} with splice junction 
            phenotypes in and around the {gene.name} gene locus. 
            On the x-axis we show the genomic location of the SNP and
            splice junctions, and on the y-axis we show the significance score
            of the association (-log10 p-value).
            The location is shown relative to the transcription start site.
            Each horizontal bar represents a splice junction phenotype as the
            part of the genome that is removed in this phenotype. 
            Height and shade of the bar indicate the strength of the association.
            The green shaded areas indicate the range where SNPs are annotated as 
            belonging to a gene locus.
        """
        
    def plot(self):
        df = self.analysis.pairs
        snp = self.analysis.snp
        log(f"Plotting phenotypes for variant {snp} with {len(df)} entries")
        
        #def pos(v): return int(v.split("_")[1].split("_")[0])  # Extract position from variant ID
        
        variant_pos = snp.pos
        tss_pos = variant_pos - df["tss_distance"].values[0]    # Assuming all entries have the same TSS distance
        pval_threshold = 0.05 / len(df)
        
        dfs = df[df["pval_nominal"] < pval_threshold]
        print(dfs)
        
        # split the phenotype id and get the start and end positions
        x1 = df["phenotype_id"].str.split(":").str[1].astype(int) - tss_pos
        x2 = df["phenotype_id"].str.split(":").str[2].astype(int) - tss_pos
        y = -np.log10(df["pval_nominal"])

        # plot horizontal intervals, saturation-coded by p-score
        cols = plt.cm.Blues(y / y.max())  # Normalize y for color mapping
        
        fig, ax = plt.subplots()
        ax.hlines(y, x1, x2, colors=cols, linewidth=2)
        ax.set_xlabel("position (bp)")
        ax.set_ylabel("-log10(p-value)")
        ax.set_title(f"Splice phenotypes for variant {snp}")
        #ax.tight_layout()
        
        # plot red vertical line at the variant position
        def vline(v, color, tag):
            ax.axvline(x=v.pos-tss_pos, color=color, linestyle='-', label=f"Variant {v}")
        vline(snp, 'red', "ours")
        ax.axvline(x=0, color='green', linestyle=':', label=f"Transcription start site {tss_pos}")
        
        # plot gray horizontal line at p-value threshold of 0.05 divided by the number of phenotypes
        ax.axhline(y=-np.log10(pval_threshold), color='gray', linestyle=':', label=f"p-value threshold {pval_threshold:.2e}")
        ax.legend(loc='lower right')  
        
        set_styles(fig, ax)
        log(f"Saving sQTL plot to {self.store.tag}{self.fn}")
        plt.savefig(self.store.open(self.fn, "wb"))
        return fig

