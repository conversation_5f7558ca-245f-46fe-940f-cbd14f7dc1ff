import re
from typing import Callable, NamedTuple
import pandas as pd
from base.io import cached, stream_parquet
from base.store import GcsStore, Store, cache_store
from base.util import lazy, lform, mform
from base.logs import make_logger
from genetics.gwas import GwasAnalysis, Hit
from genetics.schema import Snp

log = make_logger(__name__)

class TissueAnalysis():
    def __init__(self, analysis, tissue):
        """Create a tissue analysis object"""
        self.analysis = analysis
        self.tissue = tissue
        self.sgenes = analysis.sgenes_df(tissue)
        self.spairs = analysis.spairs_df(tissue)

    def __repr__(self):
        return f"TissueAnalysis({self.tissue})"

    def __str__(self):
        return f"{self.tissue} ({len(self.sgenes)} sgenes, {len(self.spairs)} spairs)"


class TarPackage():
    """Represents a remote tar package of GTEx data"""
    def __init__(self, cache, key, url):
        self.cache = cache
        self.url = url
        self.key = key

    @lazy
    def fns(self):
        """Get the list of files in the tar file"""
        return self.cache.tar_manifest(self.url, self.key)

    def read_df(self, fn):
        """Read a dataframe from a file"""
        self.cache.download_tar(self.url, self.key, [fn])
        if fn.endswith(".parquet"):
            return pd.read_parquet(self.cache.open(fn, "rb"))
        elif fn.endswith(".gz"):
            return pd.read_csv(self.cache.decompress(fn), sep="\t")
        else:
            raise ValueError(f"Unsupported file type: {fn}")

    def preload(self, select: Callable[[str], bool] = None):
        """Preload the data"""
        self.cache.download_tar(self.url, self.key, select)

class GtexAnalysis():
    def __init__(self, version=10, store: Store = None):
        self.version = version
        if store is None:
            store = cache_store()
        self.cache = store / "gtex"
        self.url = f"https://storage.googleapis.com/adult-gtex/bulk-qtl/v{version}/single-tissue-cis-qtl/"
        self.sqtl = TarPackage(self.cache, f"v{version}-sqtl", self.url+f"GTEx_Analysis_v{version}_sQTL.tar")
        self.pheno = TarPackage(self.cache, f"v{version}-pheno", self.url+f"GTEx_Analysis_v{version}_sQTL_phenotype_matrices.tar")
        
        # Different versions have slightly different file names
        self.pref = f"GTEx_Analysis_v{version}_sQTL/"
        self.post_sgenes = "sgenes.txt.gz"
        self.post_spairs = "sqtl_signifpairs.txt.gz"
        if version == 10:
            self.pref = f"GTEx_Analysis_v{version}_sQTL_updated/"
            self.post_sgenes = "sGenes.txt.gz"
            self.post_spairs = "sQTLs.signif_pairs.parquet"
        
        # Generate and parse file names
        self.sgenes_fn = lambda t: f"{self.pref}{t}.v{version}.{self.post_sgenes}"
        self.spairs_fn = lambda t: f"{self.pref}{t}.v{version}.{self.post_spairs}"
        self.sgenes_pat = re.compile(rf'{self.pref}(?P<tissue>.*)\.v(?P<version>\d+).{self.post_sgenes}')

    @lazy
    def tissues(self):
        """Get the list of tissues"""
        tissues = []
        log(f"Pattern: {self.sgenes_pat}")
        for fn in self.sqtl.fns:
            m = self.sgenes_pat.match(fn)
            #log(f"  {fn} -> {m}")
            if m:
                tissues.append(m.group("tissue"))
        return tissues

    def sgenes_df(self, tissue):
        """Get the sgenes dataframe for a tissue"""
        return self.sqtl.read_df(self.sgenes_fn(tissue))

    def spairs_df(self, tissue):
        """Get the spairs dataframe for a tissue"""
        return self.sqtl.read_df(self.spairs_fn(tissue))
    
    def preload(self):
        """Preload the GTEx data"""
        log(f"Preloading GTEx v{self.version} data")
        log(f"  files that start with {self.pref+'Adipose_'}")
        self.sqtl.preload(lambda fn: fn.startswith(self.pref+"Adipose_"))

    def load_phenotypes(self):
        """Load the phenotype data"""
        log(f"Loading GTEx v{self.version} phenotype data")
        self.pheno.preload(lambda fn: fn.startswith("Adipose_"))

class Locus(NamedTuple):
    name: str
    chr: str
    b: int
    e: int

    def __str__(self):
        return f"{self.name} {self.chr}:{self.b}:{self.e}"
    
class GtexAllAnalysis():
    """Analyse all tissues in GTEx"""
    def __init__(self, locus: Locus, version=10, store: Store = None):
        self.version = version
        self.locus = locus
        if store is None:
            store = cache_store()
        self.store = store / "gtex" / f"{version}" / locus.name
        path = f"GTEx_Analysis_v{version}_QTLs/GTEx_Analysis_v{version}_sQTL_all_associations"
        self.source = GcsStore("gtex-resources", path=path, user_project="development-311316")
        self.man_pat = re.compile(
            r"(?P<tissue>[^.]+)\.v(?P<version>\d+)\.cis_sqtl\.allpairs\.chr(?P<chrom>.+)\.parquet"
        )
    
    @cached("manifest-all.lst")
    def manifest(self):
        """Get a list of all blobs with self.bucket as prefix"""
        return self.source.list()
    
    @lazy
    def tissues(self) -> list[str]:
        """Get the list of tissues from the manifest"""
        tissues = set()
        chromos = set()
        n = 0
        for fn in self.manifest:
            m = self.man_pat.match(fn)
            if m:
                n += 1
                tissues.add(m.group("tissue"))
                chromos.add(m.group("chrom"))
            else:
                log(f"Skipping {fn} as it does not match the pattern")
        log(f"Found {n} matches in manifest, {len(tissues)} tissues.")
        self.chromosomes = sorted(chromos)
        log(f"Chromosomes: {lform(self.chromosomes, n=25)}")
        if n<len(self.manifest):
            raise Exception(f"Unmatched files: {len(self.manifest) - n}")
        return sorted(tissues)
    
    @lazy
    def chromosomes(self):
        self.tissues             # This sets self.chromosomes
        return self.chromosomes  # this sets it again, but it's not a problem

    def tissue(self, tissue: str) -> 'TissueAllAnalysis':
        """Get the analysis for a single tissue"""
        return TissueAllAnalysis(self, tissue)
    
    def __repr__(self):
        return f"GtexAllAnalysis(v{self.version})"

    def __str__(self):
        return f"GTEx v{self.version} Analysis with {len(self.tissues)} tissues"
    

class TissueAllAnalysis:
    """Analysis for a single tissue in GTEx"""
    def __init__(self, analysis: GtexAllAnalysis, tissue: str):
        if tissue not in analysis.tissues:
            raise ValueError(f"Tissue {tissue} not found in GTEx v{self.version}")
        self.store = analysis.store / tissue
        self.analysis = analysis
        self.tissue = tissue

    @cached("pairs.parquet")
    def pairs_df(self):
        """Get the pairs dataframe for this tissue"""
        fn = f"{self.tissue}.v{self.analysis.version}.cis_sqtl.allpairs.chr{self.analysis.locus.chr}.parquet"
        size = self.analysis.source.size(fn)
        log(f"Reading {self.analysis.locus} pairs from {fn}")

        with self.analysis.source.open(fn, "rb") as fd:
            def select(df: pd.DataFrame):
                # Split the 'phenotype_id' column into multiple columns by ':' separator
                split = df["phenotype_id"].str.split(":", expand=True)
                split.columns = ["chr", "b", "e", "clu", "loc"]
                sel1 = (split["chr"] == f"chr{self.analysis.locus.chr}") & \
                      (split["b"].astype(int) <= self.analysis.locus.e) & \
                      (split["e"].astype(int) >= self.analysis.locus.b)
                sel2 = df["phenotype_id"].str.contains(self.analysis.locus.name)
                if sel1.any() or sel2.any():
                    only1 = sel1 & ~sel2
                    only2 = sel2 & ~sel1
                    if only1.any():
                        log(f"Only by range: {only1.sum()} pairs")
                        print(df[only1].head())
                    if only2.any():
                        log(f"Only by name: {only2.sum()} pairs")
                        print(df[only2].head())
                    sel = sel1 | sel2
                return df[sel2]
                #return df[df["phenotype_id"].str.contains(self.analysis.locus.name)]
            [df] = stream_parquet(fd, [select], size)
        return df

pair_fn = "pairs.parquet"

class SqtlAnalysis:
    
    """A hit analysis for a single gene in GTEx"""
    def __init__(self, gwas: GwasAnalysis, tissue: str, hit: Hit, version=10, store: Store = None):
        self.gwas = gwas
        self.tissue = tissue
        self.hit = hit
        self.version = version
        path = f"GTEx_Analysis_v{version}_QTLs/GTEx_Analysis_v{version}_sQTL_all_associations"
        self.source = GcsStore("gtex-resources", path=path, user_project="development-311316")
        if store is None:
            store = cache_store()
        self.store_up = store / "gtex" / "hits" / tissue
        self.store = self.store_up / hit.gene

    @cached(pair_fn)
    def pairs(self):
        """Get the data for all hits on the chromosome"""
        def hchr(h): return h.locus.split(":")[0][3:] # Extract chromosome from locus
        chr = hchr(self.hit)
        log(f"HIT {self.hit.gene} ENSG id: {self.gwas.ensg_map.get(self.hit.gene)}")
        log(f"HIT {self.hit.gene} ENSG id: {self.hit.gene not in self.gwas.ensg_map}")
        if self.hit.gene not in self.gwas.ensg_map:
            raise ValueError(f"Gene {self.hit.gene} has no ENSG id")
        fn = f"{self.tissue}.v{self.version}.cis_sqtl.allpairs.chr{chr}.parquet"
        
        # A select function for each hit
        selects = []
        genes = []
        hits = self.gwas.hits
        # Still load this hit, even if if it's not in the top hit list
        if self.hit.gene not in set(h.gene for h in hits):
            hits = [self.hit, *hits]  # Add the hit to the list if not present
        for h in hits:
            eid = self.gwas.ensg_map.get(h.gene)
            if not eid: continue # No ENSG id for this gene
            if hchr(h) != chr: continue # Different chromosome
            if (self.store_up / h.gene).exists(pair_fn): continue  # Already stored
            def select(df, eid=eid):
                pcol = df["phenotype_id"]
                return df[pcol.str.contains(eid)]
            selects.append(select)
            #selects.append(lambda df: df[df["phenotype_id"].str.contains(eid)])
            genes.append(h.gene)
        log(f"Reading pairs from {fn} for {len(genes)} genes.")
        log(f"  {lform(genes)}")
        if len(genes) == 0:
            log(f"  {self.hit.gene} is in top hits: {self.hit.gene in set(h.gene for h in self.gwas.hits)}")
            log(f"  {self.hit.gene} is in hits: {self.hit.gene in set(h.gene for h in hits)}")
            log(f"  {self.hit.gene} ENSG id: {self.gwas.ensg_map.get(self.hit.gene)}")
            log(f"  {self.hit.gene} ENSG id: {self.hit.gene not in self.gwas.ensg_map}")
            log(f"  {self.hit.gene} chromosome: {hchr(self.hit)}")
            log(f"  {self.hit.gene} file: {(self.store_up / h.gene).exists(pair_fn)}")
            raise ValueError(f"No hits found for {self.hit.gene} in {self.tissue} on chr{chr}")
            
        # Parse the file
        size = self.source.size(fn)
        with self.source.open(fn, "rb") as fd:
            dfs = stream_parquet(fd, selects, size, description=f"chr{chr}")
        
        # Store the results for the other genes
        ret = None
        for df, gene in zip(dfs, genes):
            if gene==self.hit.gene:
                ret = df   # Return this one, don't store it
                continue
            s = (self.store_up / gene)
            with s.open(pair_fn, "wb") as f:
                df.to_parquet(f)
            log(f"Stored {len(df)} pairs for {gene} in {s}, {mform(s.size(pair_fn))}b")
        return ret
        
    @lazy
    def locus(self):
        """Get the locus for the hit"""
        return self.gwas.gene_loci[self.hit.gene]

    @lazy
    def tissue_analysis(self):
        """Get the tissue analysis for this hit"""
        return TissueAnalysis(self.gwas, self.tissue)

    @lazy
    def snps(self):
        """Get the list of SNPs for this hit"""
        rs_map = {snp.pos: snp.rs_id for snp in self.locus.all_snps}
        pat = re.compile(r"chr(?P<chrom>[\dXYM]+)_(?P<pos>\d+)_(?P<ref>[ACGT]+)_(?P<alt>[ACGT]+)_b38")
        snps = []
        if self.pairs.empty:
            log(f"No pairs found for {self.hit.gene} in {self.tissue}, cannot find SNPs")
            return snps
        print(self.pairs)
        for s in self.pairs["variant_id"].unique():
            m = pat.match(s)
            pos = int(m.group("pos"))
            if pos in rs_map:
                snp = Snp(rs_map[pos], m.group("chrom"), pos, m.group("ref"), m.group("alt"), "b38")
                snps.append(snp)
        log(f"Found {len(snps)}/{len(self.locus.all_snps)} sQTL variants for {self.hit.gene} in {self.tissue}")
        return snps
    
    @lazy
    def rs_snp(self):
        """Get a map of SNPs for this hit"""
        return {s.rs_id: s for s in self.snps}
    
    @lazy
    def pos_snp(self):
        """Get a map of SNPs by position for this hit"""
        return {s.pos: s for s in self.snps}

    def snp(self, snp: str | int) -> Snp:
        if isinstance(snp, int):
            return self.pos_snp.get(snp)
        return self.rs_snp.get(snp)
    
class SqtlSnpAnalysis:
    """Analyse a single SNP in a tissue"""
    def __init__(self, sqtl: SqtlAnalysis, snp: Snp):
        self.sqtl = sqtl
        self.snp = snp
        self.store = sqtl.store / snp.name()

    @lazy
    def pairs(self):
        """Get the pairs for this SNP"""
        df = self.sqtl.pairs
        print(df)
        log(f"Filtering {len(df)} pairs for SNP {self.snp.name()}")
        df = df[df["variant_id"] == self.snp.name()]
        if df.empty:
            bypos = []
            for v in self.sqtl.pairs["variant_id"].unique():
                if str(self.snp.pos) in v:
                    log(f"  Found variant {v} in pairs")
            log(f"Filtering by position {self.snp.pos} found {len(bypos)} pairs")
            raise ValueError(f"No pairs found for SNP {self.snp.name()} in {self.sqtl.tissue}")
        return df
    
    @lazy
    def phenotypes(self):
        """Get the phenotypes for this SNP"""
        return self.pairs["phenotype_id"].unique()
    