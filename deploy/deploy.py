import subprocess
from base.logs import make_logger

log = make_logger(__name__)


class Deploy:
    def __init__(self, name, workdir: str):
        self.name = name
        self.workdir = workdir

    def exec(self, cmd: list[str]):
        log(f"Executing: {cmd}")
        print(' '.join(cmd).replace("--", "\\\n--"))
        ret = subprocess.run(cmd, cwd=self.workdir, timeout=7200)
        log(f"Return: {ret.returncode} {ret.stderr}")
        return ret.returncode == 0

    def deploy(self):
        return self.build() and self.upload() and self.start()
