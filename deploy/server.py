import sys
from base.appinfo import appinfo_file, root_path
from base.logs import make_logger
from app.config import settings
from deploy import Deploy

log = make_logger(__name__)


class AppDeploy(Deploy):
    """Deploy the web app to Google Cloud Run"""

    def __init__(self, name: str, dbhost: str):
        super().__init__(name, root_path)
        self.dbhost = dbhost
        self.image = f"us-east1-docker.pkg.dev/development-311316/dev-repo/{self.name}:latest"

    def build(self):
        with appinfo_file() as ai:
            log(f"Building {self.name} image in {self.workdir}")
            log("AppInfo:")
            print(ai.text)
            return self.exec(
                [
                    "docker",
                    "build",
                    "-f",
                    "dockerfile.app",
                    "-t",
                    self.image,
                    "--platform",
                    "linux/amd64",
                    ".",
                ]
            )

    def upload(self):
        log(f"Uploading {self.name} image from {self.workdir}")
        return self.exec(["docker", "push", self.image])

    def start(self):
        log(f"Deploying {self.name} image {self.image}")
        env_dict = settings.model_dump()
        env_dict.pop('PORT', None)  # Remove PORT as it is set by Cloud Run
        env_dict['DBHOST'] = self.dbhost
        env_dict['SECRETS'] = "/run/secrets"
        env = ','.join(f'{k}={v}' for k, v in env_dict.items())
        log(f"ENV: {env}")
        return self.exec(
            [
                "gcloud",
                "run",
                "deploy",
                self.name,
                f"--image={self.image}",
                f"--set-env-vars={env}",
                "--set-cloudsql-instances=development-311316:us-east1:mellitos-dev",
                "--set-secrets=/run/secrets/secrets.json=mellitos-secrets:latest",
                "--network=dev-core-api",
                "--subnet=us-east1-default",
                "--port=5001",
                "--region=us-east1",
                "--project=development-311316",
                "--allow-unauthenticated",
                "--cpu", "2", "--memory", "8Gi",
                "--timeout", "3600s",
                "--vpc-egress=all-traffic",  # See if this improves GS request delays
            ]
        ) and self.exec(["gcloud", "run", "services", "update-traffic", self.name, "--to-latest"])


# These are the current deployment configurations. Add 'prod' eventually...
dev = AppDeploy(name="mellitos-dev", dbhost="************")
test = AppDeploy(name="mellitos-test", dbhost="************")

if __name__ == "__main__":
    if len(sys.argv)<2 or sys.argv[1] == "deploy":
        test.deploy()
    elif sys.argv[1] == "build":
        test.build()
    else:
        print("Usage: deploy.py [deploy|build]")
        sys.exit(1)
