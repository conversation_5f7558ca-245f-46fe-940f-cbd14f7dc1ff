
from base.store import file_store
from base.util import make_logger
from genetics.gtex import GtexAllAnalysis, GtexAnalysis, SqtlAnalysis
from genetics.gwas import GwasAnalysis

log = make_logger(__name__)

store = file_store("cache", remote="cache")

gwas = GwasAnalysis(store=store)
log(f"Loading data for {gwas.store}")
gwas.hits
of_interest = [ gwas.gene_loci[gn] for gn in ["MKNK1"] ]

for locus in of_interest + gwas.hit_loci:
    if locus.gene.name not in gwas.ensg_map: continue
    gtex = GtexAllAnalysis(locus.gene, store=store)
    for tissue in gtex.tissues:
        if not tissue.startswith("Adipose"): continue
        log(f"\n{locus.gene.name} {tissue}")
        sqlt = SqtlAnalysis(gwas, tissue, locus.hit, store=store)
        sqlt.pairs
        log(f"Hit {locus.gene.name} with p-value: {locus.hit.p_value:.2g}")
log("All done.")

