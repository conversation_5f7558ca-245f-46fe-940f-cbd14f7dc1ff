from base.store import cache_store, file_store
from images.parse import VsiImage, java_vm

store = file_store("vsi-img")

def test_vsi(name, fn):
    vf = VsiImage(store, f"{fn}.vsi")
    vf.test_meta(name)
    # vf.test_format(name, "png")
    # vf.test_format(name, "tiff")
    # tiff with jpeg2000 compression is best...
    
with java_vm():
    test_vsi("test2", "P_Plate0207_Day15_D3_01")
    test_vsi("test1", "F_Plate0207_Day14_D3_01")
    test_vsi("test3", "B2/P_Plate0200_Day12_B2_01")

# d
# jb.start_vm(class_path=bf.JARS)
# init_logger() 
# print("VM started")

# Slide="vsi-img/F_Plate0207_Day14_D3_01.vsi"

# reader = bf.ImageReader(open(Slide, 'rb'))
# print("Reader")
# ns = reader.rdr.getSeriesCount()
# print("Done")

# # print(f"Reader: {type(reader)} {type(reader.rdr)}")
# # meta = reader.rdr.getSeriesMetadata()
# # md = jb.jutil.jdictionary_to_string_dictionary(meta)
# # print(f"Metadata: {type(meta)} {meta} {md}")

# # print("Done")
# # jb.kill_vm()
# # exit(0)



# resolution_info = []
# for level in range(ns):
#     reader.rdr.setSeries(level)
#     #print(f"Level {level}: {reader.rdr.__dict__}")
#     width = reader.rdr.getSizeX()
#     height = reader.rdr.getSizeY()
#     meta = jb.jutil.jdictionary_to_string_dictionary(reader.rdr.getSeriesMetadata())
#     keys = [k for k in meta.keys() if '#' not in k]
#     print(f"Metadata: {keys}")
#     res = meta.get('Calibration')
#     units = meta.get('Calibration units')
#     print(f"Resolution: {res} {units}")
#     created = meta.get('Creation time (UTC)')
#     print(f"Created: {type(created)} {created}")
#     resolution_info.append((level, width, height, res, units, created))
# print("Resolution info:", resolution_info)

# print("Done")
# jb.kill_vm()
# exit(0)

# # Get the metadata
# metadata = reader.rdr.getMetadataStore()
# print(f"Metadata: {type(metadata)} {metadata}")
# print(f"  {vars(metadata)}")

# # Access specific metadata
# print("Image width:", metadata.getPixelsSizeX(0))
# print("Image height:", metadata.getPixelsSizeY(0))
# print("Number of channels:", metadata.getChannelCount(0))
# print("Pixel type:", metadata.getPixelType(0))

# # Get original metadata
# omeMeta = metadata.getOMEXML()
# print(f"Metadata: {type(omeMeta)} {omeMeta}")


# # omeMeta = bf.metadatatools.createOMEXMLMetadata()
# # print(f"Metadata: {type(omeMeta)} {omeMeta}")

# # reader.rdr.setId(Slide)
# # reader.rdr.setSeries(0)

# # image=reader.rdr.openBytesXYWH(0,5000, 6000, 512, 512)
# # image.shape=(3, 512, 512)
# # image=image.transpose(1,2,0)
# # img = Image.fromarray(image,'RGB')
# # img.show(image)

# print("Done")
# jb.kill_vm()