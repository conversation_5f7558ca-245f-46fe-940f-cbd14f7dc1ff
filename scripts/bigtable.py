import pandas as pd
import re
from datetime import datetime
from google.cloud import bigquery
from images.repo import CytosmartRepo, PlateImageSets
from base.store import GcsStore
from base.logs import make_logger
log = make_logger(__name__)

# Construct a BigQuery client object.
client = bigquery.Client()

# TODO(developer): Set table_id to the ID of the table to create.
src = "development-311316.mellicell_image_data_v2.cytosmart_lipid_measurments"
dst = "development-311316.mellicell_image_data_v2.fast_lipids"

image_sets = CytosmartRepo().image_sets

schema = [
    bigquery.Schema<PERSON>ield("label", "INTEGER"),
    bigquery.SchemaField("area", "FLOAT"),
    bigquery.SchemaField("perimeter", "FLOAT"),
    bigquery.SchemaField("centroid_0", "FLOAT"),
    bigquery.SchemaField("centroid_1", "FLOAT"),
    bigquery.SchemaField("circularity", "FLOAT"),
    bigquery.SchemaField("cluster", "INTEGER"),
    bigquery.SchemaField("image", "STRING"),
    bigquery.SchemaField("plate", "STRING"),
    bigquery.SchemaField("well", "STRING"),
    bigquery.SchemaField("bt_key", "STRING"),
]

def create_table():
    table = bigquery.Table(dst, schema=schema)
    table.clustering_fields = ["bt_key"]
    table = client.create_table(table)  # Make an API request.
    log(
        "Created clustered table {}.{}.{}".format(
            table.project, table.dataset_id, table.table_id
        )
    )
    

def add_plate(plate_no: int):
    pnos = [plate_no]
    pis = PlateImageSets(image_sets, pnos)
    dfs = []
    for ims in pis.image_sets:
        df = client.query(
            f"""SELECT * FROM `{src}`
            WHERE image LIKE '%{ims.bt_key}%';"""
        ).result().to_dataframe()
        df['plate'] = plate_no
        df['well'] = df['image'].apply(lambda x: re.search(r'(?P<well>[A-P]\d+)_export.jpg', x).group('well'))
        df['bt_key'] = ims.bt_key
        dfs.append(df)
    df = pd.concat(dfs)
    log(df)
    
    store = GcsStore("mellitos-cache")
    df.to_csv(store.open(f"fast_lipids-{plate_no}.csv", "w"), index=False)
    #store.save_df(df, f"fast_lipids/{plate_no}.csv")
    df.to_csv(f"fast_lipids-{plate_no}.csv", index=False)
    
    job_config = bigquery.LoadJobConfig(
        schema=schema,
        skip_leading_rows=1,
        # The source format defaults to CSV, so the line below is optional.
        source_format=bigquery.SourceFormat.CSV,
    )
    uri = f"gs://mellitos-cache/fast_lipids/{plate_no}.csv"

    load_job = client.load_table_from_uri(
        uri, dst, job_config=job_config
    )  # Make an API request.

    load_job.result()  # Waits for the job to complete.

    destination_table = client.get_table(dst)  # Make an API request.
    log("Loaded {} rows.".format(destination_table.num_rows))

def add_image_set(ims):
    df = client.query(
        f"""SELECT * FROM `{src}`
        WHERE image LIKE '%{ims.bt_key}%';"""
    ).result().to_dataframe()
    df['plate'] = ims.plate_name
    df['well'] = df['image'].apply(lambda x: re.search(r'(?P<well>[A-P]\d+)_export.jpg', x).group('well'))
    df['bt_key'] = ims.bt_key
    log(df)
    
    store = GcsStore("mellitos-cache")
    fn = f"fast_lipids/{ims.bt_key}.csv"
    df.to_csv(store.open(fn, "w"), index=False)
    #store.save_df(df, fn)
    #df.to_csv(f"fast_lipids-ims.csv", index=False)
    
    job_config = bigquery.LoadJobConfig(
        schema=schema,
        skip_leading_rows=1,
        # The source format defaults to CSV, so the line below is optional.
        source_format=bigquery.SourceFormat.CSV,
    )
    uri = f"gs://mellitos-cache/{fn}"

    load_job = client.load_table_from_uri(
        uri, dst, job_config=job_config
    )  # Make an API request.

    load_job.result()  # Waits for the job to complete.

    destination_table = client.get_table(dst)  # Make an API request.
    log("Loaded {} rows.".format(destination_table.num_rows))

def ms(dt):
    return 1000*dt.seconds+dt.microseconds/1000

def benchmark(key, n: int = 10):
    def query1():
        return client.query(
            f"""SELECT * FROM `{src}`
            WHERE image LIKE '%{key}%';"""
        ).result().to_dataframe()
    
    def query2():
        return client.query(
            f"""SELECT * FROM `{dst}`
            WHERE bt_key = '{key}';"""
        ).result().to_dataframe()
    
    dt1 = 0
    dt2 = 0
    for i in range(n): 
        t0 = datetime.now()
        df1 = query1()
        t1 = datetime.now()
        df2 = query2()
        t2 = datetime.now()
        dt1 += ms(t1-t0)
        dt2 += ms(t2-t1)
    log(f"    Query 1: {df1.shape}, Query 2: {df2.shape}")
    #log(f"Query 1: {df1}")
    log(f"    Query1: {dt1/n}, Query2: {dt2/n}")
    with open("bench.log", "a") as f:
        log(f"{df1.shape[0]}\t{dt1/n:7.2f}\t{dt2/n:7.2f}", file=f)
        
        
        
#create_table()
def get_done():
    df = client.query(
        f"""SELECT distinct(bt_key) FROM `{dst}`"""
    ).result().to_dataframe()
    log(df)
    return set(df['bt_key'])
  
done = get_done()

# for ims in image_sets.image_sets.values():
#     if ims.bt_key in done:
#         log(f"Skipping {ims.bt_key} {ims.plate_name}")
#         continue
#     add_image_set(ims)
n = 20
for i in range(n):
    t0 = datetime.now()
    log(f"{t0} Benchmark {i}/{n}:")
    benchmark(list(done)[12+i], 10)
    t1 = datetime.now()
    log(f"  {t1} - {t0} = {t1-t0} = {ms(t1-t0):.3f} ms")
    log(f"{t1} Elapsed: {ms(t1-t0):.3f} ms")