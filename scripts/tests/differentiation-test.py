"""
Unit Tests for automatic differentiation analysis
"""
import pytest
import asyncio

from images.differentiation_utils import (
    should_run_differentiation,
    has_required_channels,
    differentiation_results_exist,
    get_differentiation_error_count,
    increment_differentiation_error_count,
    clear_differentiation_state,
    trigger_differentiation_analysis
)

# --- 1. Eligibility ---

def test_should_run_differentiation_olympus_fl():
    class Dummy:
        device = "olympus"
        lighting = "FL"
    assert should_run_differentiation(Dummy())

def test_should_not_run_differentiation_non_olympus():
    class Dummy:
        device = "cytosmart"
        lighting = "FL"
    assert not should_run_differentiation(Dummy())

def test_should_not_run_differentiation_non_fl():
    class Dummy:
        device = "olympus"
        lighting = "BF"
    assert not should_run_differentiation(Dummy())

# --- 2. Channel requirements ---

def test_has_required_channels_dapi_cy5(mocker):
    db = mocker.Mock()
    image_set = mocker.Mock()
    db.execute.return_value = [("DAPI",), ("Cy5",)]
    # Patch the code to use tuple[0] as in your code
    assert has_required_channels(db, image_set)

def test_has_required_channels_dapi_tritc(mocker):
    db = mocker.Mock()
    image_set = mocker.Mock()
    db.execute.return_value = [("DAPI",), ("TRITC",)]
    assert has_required_channels(db, image_set)

def test_has_required_channels_missing_dapi(mocker):
    db = mocker.Mock()
    image_set = mocker.Mock()
    db.execute.return_value = [("Cy5",)]
    assert not has_required_channels(db, image_set)

def test_has_required_channels_missing_lipid(mocker):
    db = mocker.Mock()
    image_set = mocker.Mock()
    db.execute.return_value = [("DAPI",)]
    assert not has_required_channels(db, image_set)

def test_has_required_channels_db_error(mocker):
    db = mocker.Mock()
    image_set = mocker.Mock()
    db.execute.side_effect = Exception("fail")
    assert not has_required_channels(db, image_set)

# --- 3. Results existence ---

def test_differentiation_results_exist_true(mocker):
    image_set = mocker.Mock()
    image_set.plate.name = "Plate0204"
    image_set.day = 20
    store = mocker.Mock()
    store.exists.return_value = True
    mocker.patch("images.differentiation_utils.diff_store", return_value=store)
    assert differentiation_results_exist(image_set)

def test_differentiation_results_exist_false(mocker):
    image_set = mocker.Mock()
    image_set.plate.name = "Plate0204"
    image_set.day = 20
    store = mocker.Mock()
    store.exists.return_value = False
    mocker.patch("images.differentiation_utils.diff_store", return_value=store)
    assert not differentiation_results_exist(image_set)

# --- 4. Error count logic ---

def test_get_differentiation_error_count_zero(mocker):
    image_set = mocker.Mock()
    image_set.plate.name = "Plate0204"
    image_set.day = 20
    store = mocker.Mock()
    store.exists.return_value = False
    mocker.patch("images.differentiation_utils.diff_store", return_value=store)
    assert get_differentiation_error_count(image_set) == 0

def test_get_differentiation_error_count_with_errors(mocker):
    image_set = mocker.Mock()
    image_set.plate.name = "Plate0204"
    image_set.day = 20
    store = mocker.Mock()
    store.exists.return_value = True
    # Simulate reading a file with error_count = 2
    mock_file = mocker.mock_open(read_data='{"error_count": 2}')
    store.open = mock_file
    mocker.patch("images.differentiation_utils.diff_store", return_value=store)
    assert get_differentiation_error_count(image_set) == 2

def test_increment_differentiation_error_count(mocker):
    image_set = mocker.Mock()
    image_set.plate.name = "Plate0204"
    image_set.day = 20
    store = mocker.Mock()
    store.exists.return_value = True
    # Simulate reading a file with error_count = 1
    mock_file = mocker.mock_open(read_data='{"error_count": 1}')
    store.open = mock_file
    mocker.patch("images.differentiation_utils.diff_store", return_value=store)
    # Patch json.dump to do nothing
    mocker.patch("json.dump")
    assert increment_differentiation_error_count(image_set) == 2

# --- 5. State clearing ---

def test_clear_differentiation_state(mocker):
    image_set = mocker.Mock()
    image_set.plate.name = "Plate0204"
    image_set.day = 20
    store = mocker.Mock()
    store.exists.return_value = True
    mocker.patch("images.differentiation_utils.diff_store", return_value=store)
    mocker.patch("images.differentiation_utils.cleanup_temp_files")
    clear_differentiation_state(image_set)
    # Should call delete for each file
    assert store.delete.call_count == 3

# --- 6. Async retry logic ---

import pytest_asyncio

@pytest.mark.asyncio
async def test_trigger_differentiation_analysis_success(mocker):
    image_set = mocker.Mock()
    image_set.plate.name = "Plate0204"
    image_set.day = 20
    mocker.patch("images.differentiation_utils.get_differentiation_error_count", return_value=0)
    mocker.patch("images.differentiation_utils.clear_differentiation_state")
    mock_run = mocker.patch("images.differentiation_utils.run_differentiation", return_value=True)
    await trigger_differentiation_analysis(image_set)
    mock_run.assert_called_once_with("Plate0204", 20, yield_progress=False)

@pytest.mark.asyncio
async def test_trigger_differentiation_analysis_already_failed_3_times(mocker):
    image_set = mocker.Mock()
    image_set.plate.name = "Plate0204"
    image_set.day = 20
    mocker.patch("images.differentiation_utils.get_differentiation_error_count", return_value=3)
    mock_run = mocker.patch("images.differentiation_utils.run_differentiation")
    mocker.patch("images.differentiation_utils.clear_differentiation_state")
    await trigger_differentiation_analysis(image_set)
    mock_run.assert_not_called()

@pytest.mark.asyncio
async def test_trigger_differentiation_analysis_failure_with_retry(mocker):
    image_set = mocker.Mock()
    image_set.plate.name = "Plate0204"
    image_set.day = 20
    mocker.patch("images.differentiation_utils.get_differentiation_error_count", return_value=1)
    mocker.patch("images.differentiation_utils.clear_differentiation_state")
    # This will return 2, then 3, so the retry will only happen once
    mocker.patch("images.differentiation_utils.increment_differentiation_error_count", side_effect=[2, 3])
    mock_run = mocker.patch("images.differentiation_utils.run_differentiation", side_effect=Exception("fail"))
    mocker.patch("asyncio.sleep", return_value=None)
    await trigger_differentiation_analysis(image_set)
    assert mock_run.call_count == 2  # One initial call, one retry

@pytest.mark.asyncio
async def test_trigger_differentiation_analysis_failure_final_attempt(mocker):
    image_set = mocker.Mock()
    image_set.plate.name = "Plate0204"
    image_set.day = 20
    mocker.patch("images.differentiation_utils.get_differentiation_error_count", return_value=2)
    mocker.patch("images.differentiation_utils.clear_differentiation_state")
    mocker.patch("images.differentiation_utils.increment_differentiation_error_count", return_value=3)
    mock_run = mocker.patch("images.differentiation_utils.run_differentiation", side_effect=Exception("fail"))
    mocker.patch("asyncio.sleep", return_value=None)
    await trigger_differentiation_analysis(image_set)
    mock_run.assert_called_once() 