#!/usr/bin/env python3
"""
Seed initial differentiation workflow request
"""
import os
from base.database import get_db, transact
from workflow.culture import DifferentiationStep
from workflow.orm.steps import DifferentiationIn
from base.logs import make_logger

log = make_logger(__name__)

def main():
    """Create initial differentiation request to start the cycle"""
    db = get_db()
    try:
        step = DifferentiationStep()
        with transact(db):
            req = step.new_request(db, "differentiation")
            diff_in = DifferentiationIn(
                request=req.id,
                max_items=20,
                max_time=28800
            )
            db.add(diff_in)
        log(f"Created initial differentiation request: {req.id}")
    finally:
        db.close()

# from sqlalchemy import text

# def main():
#     """Create initial differentiation request to start the cycle"""
#     db = get_db()
#     try:
#         # Check Action 1410
#         action = db.execute(text("SELECT * FROM action WHERE id = 1410")).fetchall()
#         print("Action 1410:", action)

#         # Check what requests should be linked to it
#         requests = db.execute(text("SELECT * FROM request WHERE action = 1410")).fetchall()
#         print("Linked requests:", requests)

#         # Check DifferentiationIn entries
#         diff_entries = db.execute(text("""
#             SELECT * FROM differentiation_in WHERE request IN (
#                 SELECT id FROM request WHERE action = 1410
#             )
#         """)).fetchall()
#         print("Differentiation entries:", diff_entries)
#     finally:
#         db.close()

if __name__ == "__main__":
    main()

