from base.database import get_db
from images.parse import java_vm
from plates.model import Plate, PlateMap
from images.model import ImageSet
from images.pipeline import Pipeline, negatives
from images.viability import Viability
from base.logs import make_logger
log = make_logger(__name__)
#
#  Test image processing
#

# Find an image set
db = get_db()
maps = db.query(PlateMap).all()
for m in maps:
    log(f"  {m}")
plate = db.query(Plate).filter(Plate.name == "Plate0212").first()
isets = db.query(ImageSet).\
    filter(ImageSet.plate_id == plate.id).\
    filter(ImageSet.lighting == "FL").\
all()
log(f"Found {len(isets)} image sets")
iset = None
for s in isets:
    log(f"  {s}")
    if s.day == 16 and s.lighting == "FL":
        iset = s
    log(f"  {s}")


log(f"Plate: {plate}")
log(f"ImageSet: {iset}")

# Get the controls (this would eventually come from the workflow)
cts = negatives(db)
# log(f"Controls: {cts}")
# log(f"  Groups: {[g.group_type for g in cts.map_groups]}")
log(f"Negatives: {[d.well_pos.well_name for d in cts.map_groups[0].doses]}")

# Process the images
pipe = Pipeline(Viability, iset, cts)
with java_vm():
    pipe.result
#log(f"Result: {pipe.result}")





