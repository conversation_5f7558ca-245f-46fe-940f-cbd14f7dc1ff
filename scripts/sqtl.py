import tarfile
import pandas as pd
import requests

from base.store import cache_store
from base.util import lform, df_stats
from base.logs import make_logger
from genetics.gtex import GtexAnalysis
import numpy as np
import matplotlib.pyplot as plt

log = make_logger(__name__)
#
# Analyse sQTL data for target discovery
#
version = 10
gene = "MKNK1"
tissue = "Adipose_Visceral_Omentum" # "Adipose_Subcutaneous"
variant = "chr1_46569971_T_C_b38"

ga = GtexAnalysis(version)
ga.preload()
# ga.load_phenotypes()
# exit(0)

log(f"{len(ga.tissues)} tissues in GTEx v{ga.version}")
sgenes = ga.sgenes_df(tissue)
df_stats(sgenes, log)
spairs = ga.spairs_df(tissue)
df_stats(spairs, log)

sg = sgenes[sgenes["gene_name"] == gene]
log(f"Genes for {gene}: {len(sg)} ({lform(sg['gene_id'].unique())})")
if len(sg) < 1:
    log(f"Gene {gene} not found in GTEx v{ga.version}")
if len(sg) > 1:
    log(f"Gene {gene} found multiple times in GTEx v{ga.version}")
phenotype_id = sg["phenotype_id"].values[0]

ps = spairs[spairs["phenotype_id"] == phenotype_id]
log(f"Pairs for {gene} ({phenotype_id}): {len(ps)}")
df_stats(ps, log)
if "af" not in ps.columns:
    ps = ps.rename(columns={'maf': 'af'})
    
df = ps[["variant_id", "af", "slope", "pval_nominal", "pval_beta"]]
# sort by pval_nominal
df = df.sort_values("pval_nominal")
print(df)

# Check for variant of interest
if variant not in df["variant_id"].values:
    log(f"Variant {variant} not found in GTEx v{ga.version}")
    log(f"  All variants: {sorted(df['variant_id'].unique())}")
if len(df[df["variant_id"] == variant]) > 1:
    log(f"Variant {variant} found multiple times in GTEx v{ga.version}")
if len(df[df["variant_id"] == variant]) == 1:
    log(f"Variant {variant} found in GTEx v{ga.version}")
    row = df[df["variant_id"] == variant].iloc[0]
    log(f"  {row['variant_id']} {row['af']} {row['slope']} {row['pval_nominal']} {row['pval_beta']}")

# Vibe coded plots...
labels = True

# Ensure required columns are present
if "tss_distance" not in ps.columns:
    log("tss_distance column not found in spairs dataframe.")
else:
    # Volcano plot
    plt.figure(figsize=(12, 6))

    plt.subplot(1, 2, 1)
    plt.scatter(df["slope"], -np.log10(df["pval_nominal"]), alpha=0.5, c="blue")
    plt.title("Volcano Plot")
    plt.xlabel("Slope")
    plt.ylabel("-log10(pval_nominal)")
    plt.grid(True)

    # Label points above y=10
    if labels:
        for i, row in df.iterrows():
            if -np.log10(row["pval_nominal"]) > 10:
                plt.text(row["slope"], -np.log10(row["pval_nominal"]), row["variant_id"], fontsize=8)

    # Manhattan plot
    plt.subplot(1, 2, 2)
    plt.scatter(ps["tss_distance"], -np.log10(ps["pval_nominal"]), alpha=0.5, c="green")
    plt.title("Manhattan Plot")
    plt.xlabel("TSS Distance")
    plt.ylabel("-log10(pval_nominal)")
    plt.grid(True)

    # Label points above y=10
    if labels:
        for i, row in ps.iterrows():
            if -np.log10(row["pval_nominal"]) > 10:
                plt.text(row["tss_distance"], -np.log10(row["pval_nominal"]), row["variant_id"], fontsize=8)

    plt.tight_layout()
    plt.show()


