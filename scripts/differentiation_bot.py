#!/usr/bin/env python3
"""
Differentiation analysis bot for automated background processing
"""
import os
import asyncio
from base.database import get_db
from bots.differentiation import DifferentiationBot
from images.parse import java_vm
from base.logs import make_logger

log = make_logger(__name__)

def main():
    """Entry point for differentiation bot"""
    log("Starting differentiation bot...")
    
    db = get_db()
    try:
        with java_vm():
            bot = DifferentiationBot(db)
            asyncio.run(bot.run(wait=True))  # Run continuously
    finally:
        db.close()

if __name__ == "__main__":
    main()