import matplotlib.pyplot as plt
import numpy as np
from base.util import lform, set_comp
from base.logs import make_logger
from genetics.gtex import GtexAllAnalysis, Locus, SqtlAnalysis, SqtlSnpAnalysis
from genetics.gwas import GwasAnalysis

log = make_logger(__name__)

tissue = "Adipose_Visceral_Omentum"  # Example tissue

gwas = GwasAnalysis()

locus = gwas.gene_loci["MKNK1"]  # Example locus around the MKNK1 gene
sqtl = SqtlAnalysis(gwas, tissue, locus.hit)

gw_snps = locus.all_snps  # Get the first SNP for demonstration
print(f"Found {len(gw_snps)} SNPs in locus {locus.hit.gene} ({locus.hit})")

sq_snps = sqtl.snps
print(f"Found {len(sq_snps)} SNPs in GTEx sQTL analysis for tissue {tissue}")
set_comp(
    "GWAS", set(s.pos for s in gw_snps),
    "sQTL", set(s.pos for s in sq_snps),
)

exit(0)

locus = Locus("ENSG00000079277.22", "1", 46370000, 46870000)  # Example locus around the MKNK1 gene
#locus = Locus("MKNK1", "1", 46370000, 46870000)  # Example locus around the MKNK1 gene
variant_our = "chr1_46569971_T_C_b38"
variant_strong = "chr1_46584661_T_C_b38"

a = GtexAllAnalysis(locus, version=10)
log(f"Tissues: {len(a.tissues)}")
for tissue in a.tissues:
    log(f"  {tissue}")
log(f"Chromosomes: {lform(a.chromosomes, n=25)}")
#df = a.tissue("Adipose_Subcutaneous").pairs_df
df = a.tissue("Adipose_Visceral_Omentum").pairs_df
log(f"Pairs dataframe shape: {df.shape}")

# Plot the phenotypes for a given variant as horizontal interval bars, with the 
# log-score of the p-value as the y coordinate.
def plot_phenotypes(df, variant):
    df = df[df["variant_id"] == variant]
    if df.empty:
        log(f"Variant {variant} not found in dataframe")
        return
    log(f"Plotting phenotypes for variant {variant} with {len(df)} entries")
    
    def pos(v): return int(v.split("_")[1].split("_")[0])  # Extract position from variant ID
    
    variant_pos = int(variant.split("_")[1].split("_")[0])  # Extract position from variant ID
    tss_pos = variant_pos - df["tss_distance"].values[0]    # Assuming all entries have the same TSS distance
    pval_threshold = 0.05 / len(df)
    
    dfs = df[df["pval_nominal"] < pval_threshold]
    print(dfs)
    
    # split the phenotype id and get the start and end positions
    x1 = df["phenotype_id"].str.split(":").str[1].astype(int) - tss_pos
    x2 = df["phenotype_id"].str.split(":").str[2].astype(int) - tss_pos
    y = -np.log10(df["pval_nominal"])

    # plot horizontal intervals, saturation-coded by p-score
    cols = plt.cm.Blues(y / y.max())  # Normalize y for color mapping
    plt.hlines(y, x1, x2, colors=cols, linewidth=2)
    plt.xlabel("position (bp)")
    plt.ylabel("-log10(p-value)")
    plt.title(f"Splice phenotypes for variant {variant}")
    plt.tight_layout()
    
    # plot red vertical line at the variant position
    def vline(v, color, tag):
        plt.axvline(x=pos(v)-tss_pos, color=color, linestyle='-', label=f"Variant {v} ({tag})")
    vline(variant_our, 'red', "ours")
    vline(variant_strong, 'green', "strong")
    plt.axvline(x=0, color='green', linestyle=':', label=f"Transcription start site {tss_pos}")
    
    # plot gray horizontal line at p-value threshold of 0.05 divided by the number of phenotypes
    plt.axhline(y=-np.log10(pval_threshold), color='gray', linestyle=':', label=f"p-value threshold {pval_threshold:.2e}")
    plt.legend(loc='lower right')   

plt.figure(figsize=(12, 6))
plt.subplot(1, 2, 1)
plot_phenotypes(df, variant_our)
plt.subplot(1, 2, 2)
plot_phenotypes(df, variant_strong)
plt.show()
