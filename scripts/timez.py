from datetime import datetime, timezone
import pytz
from base.logs import make_logger
log = make_logger(__name__)

ET = pytz.timezone('America/New_York')
def pt(tag: str, tz):
    log(f"{tag}: {datetime.now(tz)} -> {datetime.now(tz).strftime('%Y-%m-%d %H:%M:%S')}")
pt("UTC", timezone.utc)
pt(" ET", ET)

t = datetime.now(timezone.utc)
log(f"UTC: {t} -> {t.strftime('%Y-%m-%d %H:%M:%S')}")
lt = t.astimezone(ET)
log(f" ET: {lt} -> {lt.strftime('%Y-%m-%d %H:%M:%S')}")