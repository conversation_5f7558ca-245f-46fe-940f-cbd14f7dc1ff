#!/usr/bin/env python3
"""
Direct differentiation analysis script - for manual testing
"""

import sys
import os

# Set environment variables
DATABASE_ENV = {
    "DBHOST": "0.0.0.0",
    "DBPORT": "5432", 
    "DBUSER": "app",
    "DBPASS": "pass",
    "DBNAME": "app",
    "DRIVERNAME": "postgresql+pg8000",
    "PORT": "5001",
    "SECRETS": ".secrets"
}

for key, value in DATABASE_ENV.items():
    if key not in os.environ:
        os.environ[key] = value

from base.database import get_db
from images.parse import java_vm
from images.differentiation_utils import run_differentiation
from images.model import ImageSet, Plate
from base.logs import make_logger

log = make_logger(__name__)

def main():
    """Entry point for direct differentiation analysis"""
    
    # if len(sys.argv) > 2:
    #     plate_name = sys.argv[1] if sys.argv[1].startswith("Plate") else "Plate" + sys.argv[1]
    #     culture_day = int(sys.argv[2])
    # else:
    #     plate_input = input("Enter plate number (format: #### or Plate####): ")
    #     plate_name = plate_input if plate_input.startswith("Plate") else "Plate" + plate_input
    #     culture_day = int(input("Enter culture day: "))
    
    # log(f"Processing {plate_name} day {culture_day}")
    
    db = get_db()
    try:
        # # Find the imageset
        # plate = db.query(Plate).filter(Plate.name == plate_name).first()
        # if not plate:
        #     log(f"Plate {plate_name} not found")
        #     return
        
        # imageset = db.query(ImageSet).filter(
        #     ImageSet.plate_id == plate.id,
        #     ImageSet.day == culture_day,
        #     ImageSet.device == "olympus",
        #     ImageSet.lighting == "FL"
        # ).first()
        
        # if not imageset:
        #     log(f"No olympus FL imageset found for {plate_name} day {culture_day}")
        #     return
        
        # log(f"Found imageset {imageset.id}")
        
        test_ims = db.get(ImageSet, 1064)

        with java_vm():
            result = run_differentiation(db, test_ims, yield_progress=True)
            if result is not None:
                log(f"Result: {result}")    
                log(f"Analysis completed successfully")
            else:
                log(f"No results generated")
                
    finally:
        db.close()

if __name__ == "__main__":
    main()
