import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from base.util import lform, set_comp
from base.logs import make_logger
from genetics.gtex import HitAnalysis, Locus
from genetics.gwas import GwasAnalysis

log = make_logger(__name__)
#locus = Locus("ENSG00000079277.22", "1", 46370000, 46870000)  # Example locus around the MKNK1 gene
locus = Locus("ENSG00000079277.22", "1", 46557859, 46603005)  # Example locus around the MKNK1 gene

gwas = GwasAnalysis()
print(f"GWAS SNPs: {len(gwas.data)}")
print(gwas.data.head())
print(gwas.index_b38.head())
print(gwas.gene_ids)
gene = "MKNK1"
log(f"Gene IDs: {gene} -> {gwas.ensg_map[gene]}")
missing_genes = [hit.gene for hit in gwas.hits if hit.gene not in gwas.ensg_map]
print(f"Genes in hits not found in ensg_map: {len(missing_genes)}")
print(f"Missing genes: {lform(missing_genes, 8)}")

tissue = "Adipose_Subcutaneous"

# Show MKNK1
loc = gwas.gene_loci["MKNK1"]
ha = HitAnalysis(gwas, tissue, loc.hit)
log(f"Hit {loc.hit.gene} {ha.pairs.shape}")

# Show all the top hits
for hit in gwas.hits:
    if hit.gene not in gwas.ensg_map:
        log(f"Hit {hit.gene} not found in ensg_map")
        continue
    ha = HitAnalysis(gwas, tissue, hit)
    log(f"Hit {hit.gene} {ha.pairs.shape}")
# Also show MKNK1
loc = gwas.gene_loci["MKNK1"]
ha = HitAnalysis(gwas, "Adipose_Subcutaneous", loc.hit)
log(f"Hit {loc.hit.gene} {ha.pairs.shape}")

exit(0)

def show_locus(ln):
    loci = gwas.gene_loci
    ln = "ADCY3"
    print(f"GWAS locus {ln}: {loci[ln]}")

    for hit in gwas.hits[:50]:
        print(f"  {hit.gene} ({hit.snps} SNPs), p={hit.p_value:.2g}")
#    loci[ln].plots().show()
    
show_locus("MKNK1")    
exit(0)

def show(tag, col):
    dups = col[col.duplicated()]
    log(f"{tag} SNPs: {len(col.dropna().unique())}/{len(col.dropna())}/{len(col)}")
    log(f"  dups: {lform(dups, 8)}")


show("GWAS ", gwas.data["SNP"])
show("Index", gwas.index_b38["ID"])
set_comp("GWAS ", gwas.data["SNP"], "Index", gwas.index_b38["ID"])

snps = [
    gwas.snp('chr1_46569971_T_C_b38'),
    gwas.snp('chr1_46584661_T_C_b38'),
]
log(f"Our SNP: {snps[0]}")
log(f"Crazy SNP: {snps[1]}")

df = gwas.data[gwas.data["SNP"].isin([s.rs_id for s in snps])]
print(df)

