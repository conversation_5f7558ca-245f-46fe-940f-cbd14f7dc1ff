from pathlib import Path
import slideio

from base.logs import make_logger
log = make_logger(__name__)

def slide_info(slide: slideio.Slide, log=log):
    log(f"Slide image: {slide.file_path}")
    
    # Find the total file size
    size = 0
    path = Path(slide.file_path)
    dir = path.parent / ("_"+path.stem+"_")
    log(f"  Directory: {dir}")
    for sd in dir.glob('stack*'):
        log(f"    {sd}")
        for df in sd.glob('*.ets'):
            log(f"      {df} {df.stat().st_size}")
            size += df.stat().st_size
    log(f"  Total size: {size} bytes.")
    log(f"  Auxiliary images: {slide.num_aux_images}")
    log(f"  Scenes: {slide.num_scenes}")
    for i in range(slide.num_scenes):
        scene = slide.get_scene(i)
        log(f"    Scene {i}: {scene.num_t_frames}x{scene.num_z_slices}x{scene.num_channels} '{scene.name}' <- {scene.file_path}")
        log(f"      Compression: {scene.compression}, {size} bytes, {size/(scene.size[0]*scene.size[1]):.2f} bytes/pixel")
        log(f"      Magnification: {scene.magnification}")
        log(f"      Frame: {scene.origin} {scene.rect}, size: {scene.size}")
        log(f"      Resolution: {scene.resolution}")
        log(f"      Zoom levels: {scene.num_zoom_levels}")
        log(f"      Meta: {scene.get_raw_metadata()}")
        log(f"      Channels: {scene.num_channels}")
        for j in range(scene.num_channels):
            log(f"        {j}: {scene.get_channel_name(j)} {scene.get_channel_data_type(j)}")

log(f"Drivers: {slideio.get_driver_ids()}")

#path = "B2/P_Plate0200_Day12_B2_01.vsi"   # This one has no scenes when it should. TIFF exists.
#path = "D3/F_Plate0207_Day14_D3_01.vsi"
path = "B12/P_Plate0152_Day34_B12_01.vsi"
#path = "vsi-img/F_Plate0207_Day14_D3_01.vsi"

slide = slideio.open_slide(path, 'VSI')

slide_info(slide)