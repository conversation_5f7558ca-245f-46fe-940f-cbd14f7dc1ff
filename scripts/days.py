from collections import Counter
from base.util import lform
from base.database import get_db, transact, populate_enums, engine, Base
from plates.model import Plate, PlateMap
from workflow.orm.rar import Request, Action, Result
from workflow.orm.steps import SeedIn
from images.model import ImageSet, Lighting, Image
from images.repo import Repo
from qc.data import plate_days
from base.logs import make_logger
log = make_logger(__name__)

#
# Backfill the day and lighting for image sets
#

# Obtain a database connection
db = get_db()

imss = db.query(ImageSet).all()

def report_all():
    def distinct(f: str):
        s = set([getattr(ims, f) for ims in imss])
        log(f"  Distinct {f}: {lform(s)}")
        
    log(f"Found {len(imss)} image sets to backfill")
    for f in ['pix_per_mm', 'size_x', 'size_y']:
        distinct(f)
    log("  Counting images...")
    for ims in imss:
        imgs = db.query(Image).filter(Image.image_set == ims.id).all()
        count = 0
        minx = miny = maxx = maxy = None
        for img in imgs:
            count += 1
            if minx is None:
                minx = maxx = img.size_x
                miny = maxy = img.size_y
            else:
                minx = min(minx, img.size_x)
                miny = min(miny, img.size_y)
                maxx = max(maxx, img.size_x)
                maxy = max(maxy, img.size_y)
        log(f"  {ims.plate.name} {ims.day}: {count} images, min {minx}x{miny}, max {maxx}x{maxy}")

def check_well(plate_name, day, well):
    csr = Repo("cytosmart")
    ims = csr.image_set(plate_name, day)
    log(f"DB Image set: {ims}")

    repo = OlympusRepo()
    well = repo.find_well(*well)
    log(f"Olympus repo: {ims}")
    well.list_images()
    # well.load_image('jpg', 'bf')

    check_well('Plate0207', 14, 'D3')
exit(0)

# Populate enums, as `lighting` should have been newly created.
populate_enums(db)

exit(0)




