#
# Use this script to build and deply the app container. It ensures the appinfo is available.
#
python src/base/appinfo.py > src/base/_appinfo.py
cat src/base/_appinfo.py
if grep "wip = True" src/base/_appinfo.py; then
  echo "There is work in progress. Please finish and check it into git before deploying!"
else
    docker build -f dockerfile.app -t us-east1-docker.pkg.dev/development-311316/dev-repo/mellitos-dev:latest --platform linux/amd64 .
    docker push us-east1-docker.pkg.dev/development-311316/dev-repo/mellitos-dev:latest
fi
rm src/base/_appinfo.py  # Remove the temporary appinfo file.

