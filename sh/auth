if ! command -v gcloud &> /dev/null; then
    echo "Google Cloud CLI (gcloud) is not installed."
    echo "Please install it first: brew install google-cloud-sdk"
    exit 1
fi
if ! gcloud config list account --format "value(core.account)" &> /dev/null; then
    gcloud init
fi

gcloud config configurations activate mellicell
gcloud auth application-default login
gcloud auth configure-docker us-east1-docker.pkg.dev
mkdir -p .secrets

echo "Authentication setup complete!"
echo "Note: If you need to access Cloud SQL, run:"
echo "  ./cloud-sql-proxy PROJECT_ID:REGION:INSTANCE_NAME"
