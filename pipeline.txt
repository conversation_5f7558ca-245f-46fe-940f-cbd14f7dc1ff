https://docs.google.com/document/d/15_J5l-WQoBpZWsNLjkMjeqAZWvfMVzB3EJ_jdsMGDVw/edit 

Active pipelines and development
Connor Truex
Phone: ************
Email: <EMAIL>

You can also sift through all of my workstation files that are saved to the bucket: gs://mellicell_development_bucket/ConnorArchives 
Handling Analysis Requests

You will need…

Database connection details with the production database (for olympus image data)
https://console.cloud.google.com/sql/instances/main/overview?project=production-401712 
Connection details to put in a .env file
DBNAME=”main”
DBUSER=”readonly”
DBHOST=”*************” (cloud sql proxy is running on a VM in production project)
DBPORT=5432
DBPASS=”k3{mLz`o[rseo639 “
AW: Needed to remove quotes, change ip to localhost, change port to 5433, and remove trailing blank from password.
Used proxy: gcloud compute ssh sql-proxy --project production-401712 --zone us-east1-b -- -NL 5433:localhost:5432


Database connection details with mellitos-dev database (for treatment data)
https://console.cloud.google.com/sql/instances/mellitos-dev/overview?project=development-311316 
This is mostly only needed for mitotracker analysis
Run cloud sql proxy so psql client can access it if needs too
./cloud-sql-proxy development-311316:us-east1:mellitos-dev
Password is not included here because sensitive treatment data is in this database


Due to some unforeseen issues with apple macbooks, the password in a .env file may be misinterpreted and you will get database connection errors. You may just need to copy and paste the password into the code or figure out a way to have python-dotenv read it correctly. 

Additionally, there is another database user in use by Airflow to write to the database. If you need to write to this database, you may have to change the password to get access. You can change passwords at the following link: https://console.cloud.google.com/sql/instances/main/users?project=production-401712 

If you change this password for the following database user, be sure to update the connection detail in the cloud composer environment (this is what airflow uses to write to DB)

DBUSER: airflow
DBPASS: 

https://249d5561ea4e49bab948e92691b575d7-dot-us-east1.composer.googleusercontent.com/ 
Admin > connections > cloud_sql_postgres_main

These are the main analysis requests and a short cut to how to carry them out

Bright field size change analysis
Differentiation
Viability (there is an additional viability analysis option as well)
Mitochondrial activity (mitotracker)

To help track analysis requests, you can use the following spreadsheet:
https://docs.google.com/spreadsheets/d/1IQGOs3xI515f6zvEIo0NUaAFl-qcQ60QDjX1zUE0WZM/edit?gid=0#gid=0.  
Cytosmart Pipeline

Currently active pipelines or transfer programs that are in use at MelliCell
Cytosmart data transfer
Cytosmart provides their own software with the Omni imager being used in the lab. We worked with them to install custom code. There is a button that they included that will push the images to our bucket, cytosmart_brightfield_images_mellicell. Naming convention of the images is as follows: <experiment_uuid>/<scan_number>/<well_name>/<well_name>export.jpg. You will also find a json file with every group: <experiment_uuid>/<scan_number>/scan_metadata.json. The scan_metadata.json will have the following in it:
The experiment id
The scan number 
Experiment name (this has plate name and culture day)
Number of wells the plate has
Pixels per Mm 

There is no Code repository, but you can reach out about issues to the following
<EMAIL>
<EMAIL> 

Please note, if a set of images were improperly cropped (the wells are half of the image), you can delete or move those images from the bucket, fix the cropping inside the cytosmart software, and export them again. The set of new images will be put in the same spot as the old ones.
BF Cytosmart analysis pipeline
This is the segmentation pipeline for cytosmart (omni) images. Images are written to the melltios database found at this link: https://console.cloud.google.com/sql/instances/mellitos/overview?project=development-311316. 

The main worker is written in this repository:
https://github.com/MelliCell/mellitos_system_v1/tree/main/mask_requests_processor/dockerContainer 

This one write to two tables in BigQuery:
mellicell_image_data_v2/cytosmart_lipid_measurments
mellicell_image_data_v2/cytosmart_measurements

It is also saved into a docker container.

The code is deployed as a docker container that can then be called with ‘docker run’ in a startup script. (gcr.io/development-311316/cytosmart-segment-service-v2:two-tables is the docker image) .It helps to create a VM image with the docker image pre-downloaded so the workers are not constantly downloading the docker image every time. It drives up networking costs and has led to failures. 

The image used to create the instance is as follows
	projects/development-311316/global/images/cytosmart-image-analysis-image


To view the files inside a docker container you can run this command:
	docker run --rm -it --entrypoint=/bin/bash gcr.io/development-311316/cytosmart-segment-service-v2:two-tables

The deployment happens with every image that comes into the bucket. The next section describes the function that deploys this code.
Invoke Cytosmart analysis
A cloud function connected to the bucket creates a cytosmart analysis worker
https://github.com/MelliCell/create-omni-worker-function 

Currently there is a cloud function that is working everytime an image is uploaded into cytosmart_brightfield_images_mellicell.
 
https://console.cloud.google.com/functions/details/us-east1/create-cytosmart-analysis-instance?env=gen1&project=development-311316&tab=metrics 
Additional cloud functions 
There are two additional cloud functions that write to the mellitos database. (not the mellitos-dev)

register-cytosmart-image will write each every time an image is uploaded to the database.
Register-cytosmart-image-set will write everytime a scan_metadata.json file is created
Olympus Pipeline
Each section includes the components of the Olympus pipeline for bright field imaging in the cloud.
Most of the implementations are hosted in cloud project production-401712.
Warnings
If the olympus data transfer fails by only sending .vsi files and none of the supporting .ets files with them, the olympus image type conversion scales up and repeatedly fails, this can drive cloud costs up if not attended. keep the maximum number of instances low and check in on the pipeline occasionally. The easiest way to monitor for this is by looking for unacknowledged messages in the convert-ix83-image-sub subscription. https://console.cloud.google.com/cloudpubsub/subscription/detail/convert-ix83-image-sub?project=production-401712 is where you can look at these metrics.
Producton Database 
Separate from the Mellitos App, there is a ‘production’ database in the production-401712 project. It’s schema is a very old version of the Mellitos App schema and can be described by the ORM of an early commit of the core-sample-api: https://github.com/MelliCell/core-sample-api/blob/fa0dcc28b76b525ba15a9ee323800b1058c8703a/api/models.py. This production database didn’t change because pipelines were already writing to it. The Airflow DAGs write to this database.

Accessing its data from Bigquery
The completed data from this pipeline resides in CSV files in the bucket gs://ix83-brightfield-measurments/MCL_CP_005. You can access this data with SQL like queries in bigquery. There is already a template query you can use to find changes in multiple metrics between two days. You can find this pre-made query with this link: https://console.cloud.google.com/bigquery?sq=453397164617:e5e89dd1ca9245c9beef59557a429238  .

Only three lines need to be changed:
Line 15 needs to be changed to the plate you want to analyze (ex: 0174)
Line 16 needs to be changed to include the culture day at the start of treatment and end of treatment. (ex: “(81, 96)”)
Line 89 needs to be changed to the culture day at the end of treatment (ex: 96)

The data will include size change, count change, change of the average largest lipid per cluster

Olympus data transfer
Inside the lab, there is a windows service that listens for new images in a directory. Once a new image is detected, it will check its name and make sure it matches a naming convention. It is looking for the following patterns to match

"_Plate([PD]{0,2}[0-9]{4})_"
"_([A-Z][0-9]{1,2})_"
"_Day([0-9]{0,3})_"

If any of these fail to match, it will do nothing.

It then uploads the image to a bucket called ix83-raw-files (in the project production-401712) It then pushes a message to the pubsub topic: projects/production-401712/topics/ix83-image-uploaded.

Code: https://github.com/MelliCell/******************** 
This runs locally on the lab computer and is installed as a windows service
Deployment procedure can be found in the README of the repository
Olympus Image Type Conversion pipeline
Once the olympus images are uploaded and the messages have been pushed. They need to be converted to an open format for the rest of our image processing pipelines. This will listen to a pubsub message topic and process each image as they come into the message queue. It is listening to the following pubsub subscription: projects/production-401712/subscriptions/convert-ix83-image-sub. 


Code repository: https://github.com/MelliCell/ImageConvertWorker 

This code is hosted on a Managed Instance Group (MIG) in google cloud compute engine. The managed instance group will scale up or down based on how many messages are in the pubsub subscription topic. 
https://console.cloud.google.com/compute/instanceGroups/details/us-east1/bioformats-converter-group?project=production-401712 

Deployment procedure (in github read me)
Create instance image
Create instance template
Create managed instance group
BF Olympus Segmentation Pipeline
Segmentation worker

This is a worker that listens for new tiff images and creates segmentation masks. 

The code: https://github.com/MelliCell/ImageSegmentWorker 

Run configuration: this is hosted on google cloud as a managed instance group.
https://console.cloud.google.com/compute/instanceGroups/details/us-east1/cellpose-brightfield-segment-group?project=production-401712 


Deployment procedure
Create instance image
Create instance template
Create managed instance group
Measurement worker

The measurement workers are deployed very similarly to the other two managed instance groups. 
https://console.cloud.google.com/compute/instanceGroups/details/us-east1/measure-ix83-brightfield-mask-group?project=production-401712 

The workers in this group used to write to bigquery directly and rely on a redis instance to ensure that it doesn’t write to bigquery twice, but it has since been changed to write to cloud storage. Bigquery can still access the CSV files in the storage bucket (https://cloud.google.com/bigquery/docs/external-data-cloud-storage). The dependency on the redis instance can probably be removed without any consequences since double work will only result in overwriting a csv with the same data.

The code can be found at https://github.com/MelliCell/MeasureMaskWorker/tree/main 

The redis instance: https://console.cloud.google.com/memorystore/redis/locations/us-east1/instances/written-masks/details/overview?project=production-401712 


Deployment procedure
Create instance image
Create instance template
Create managed instance group

Airflow DAGs
The Airflow dags are what connect the workers of each step. They listen for messages sent upon a worker’s completion of a task, make an entry in the database, and send a message for the next worker pool to pick up. To upload new dags, all you need to do is upload the coded DAG to the dag folder: https://console.cloud.google.com/storage/browser/us-east1-airflow-main-0b3f49ec-bucket/dags?project=production-401712. 
Cloud Composer Configuration
Uploading new dags and accessing airflow dashboard 
To access the dashboard, view logs, or upload new dags to the dag folder, all the links are available from the composer cloud console: https://console.cloud.google.com/composer/environments?referrer=search&project=production-401712. 
Environment set up
Cloud composer is the google service that hosts our airflow instance. Under the hood, it is a Kubernetes cluster. Unfortunately, if you want to connect to cloud sql from cloud composer, you will need to tinker with the default deployment. Following the steps laid out in https://medium.com/nerd-for-tech/connecting-gcp-composer-to-cloud-sql-via-proxy-305743a388a can help you connect to cloud sql for any future cloud composer deployments.

When you deploy a cloud composer instance, you can see the underlying cluster it deployed in kubernetes engine. you will need to know this clusters name in order to modify it. 

In essence you are deploying a service in the kubernetes cluster that is running cloud-sql-proxy. NOTE: make sure cloud-sql-proxy is set to bind to “0.0.0.0” by using the “-a” flag, otherwise other services outside will not be able to find it 


Key 
Val 
convert_olympus_topic
convert-ix83-image
measure_mask_topic
measure-ix83-lipid-mask
measurments_written_subscription
ix83-lipid-morphology-written-sub
new_mask_subscription
ix83-lipid-mask-uploaded-sub
new_nuceli_mask_subscription
ix83-nuclei-mask-created-sub
new_olympus_file_subscription
ix83-image-uploaded-sub
new_tiff_subscription
ix83-tiff-image-uploaded-sub
PROJECT_ID
production-401712
segment_tiff_topic
segment-ix83-tiff




Create tables dag
This dag is only executed once. It creates the tables required in the database if they do not exist.

https://github.com/MelliCell/airflow-dags/blob/main/create_tables.py 

IX83 Image Conversion dag
This is the first dag to run after the uploader notifies of a new image. It relies on the new_olympus_file_subscription variable in cloud composer environment to know where to listen for messages. It is currently set to ix83-image-uploaded-sub. It also relies on convert_olympus_topic environment variable to know where to send the message. The current setting is convert-ix83-images.

DAG code can be found at https://github.com/MelliCell/airflow-dags/blob/main/ix83_convertion.py 

The pubsub subscription Ix83-image-upload-sub has messages of the following form:

“gs://bucket_name/path/to/object.vsi”

The DAG sends to convert-ix83-images with the following message:

{
	“data”: None
 	 "attributes": {"object_uri": object_uri}
 }

*the dag puts data in message attributes because of difficulty of passing strings between steps
Segmentation dag
Once the messages have been converted to tiff format, the image conversion worker will publish a message. The dag will use the new_tiff_subscription variable to know where to listen for these messages. It is currently set to ix83-tiff-image-uploaded-sub. This dag will read these messages and insert the newly created images into the database. There are also special steps in place to insert new plates and wells into the database. If the plate is already present, it will just get the ID, if it is not present, it will make the new insert and retrieve the ID. The same goes for each well the images represent. Once the id is retrieved, it is included in the message it sends out using the topic in the variable segment_tiff_topic that is currently set to segment-ix83-tiff.


The code for this dag can be found at https://github.com/MelliCell/airflow-dags/blob/main/segmentation.py 

Receives pubsub message from ix83-tiff-image-uploaded-sub (sent in message data as base64 encoded string)

{
“originalUri": original uri of the vsi image in ix83-raw-images,
            "objectUri": URI for the new tiff image,
            "plate": plate name,
            "cultureDay": culture day image was taken,
            "wellName": well name such as ‘A1’,
            "channelName": channel name such as ‘DAPI’, ‘TRITC’, ‘FITC’, ‘Cy5’, or ‘BF’,
            "channelWaveLength": the wavelength that the channel is in,
            "waveLengthUnits": the unites of the wave length described such as ‘um’,
           "pixelCalibration": number of units per pixel such as micrometers per pixel,
"calibrationUnits": measurement unit such as nanometers or micrometers,
            "dimensionX": pixel width of image,
            "dimensionY": pixel height of image,
            "creationTime": UTC time of image creation
}


Sends message to segment-ix83-tiff (this are sent as attributes in the message, not in message data)

{
“originalUri": original uri of the vsi image in ix83-raw-images,
            "objectUri": URI for the new tiff image,
            "plate": plate name,
            "cultureDay": culture day image was taken,
            "wellName": well name such as ‘A1’,
            "channelName": channel name such as ‘DAPI’, ‘TRITC’, ‘FITC’, ‘Cy5’, or ‘BF’,
            "channelWaveLength": the wavelength that the channel is in,
            "waveLengthUnits": the unites of the wave length described such as ‘um’,
           "pixelCalibration": number of units per pixel such as micrometers per pixel,
"calibrationUnits": measurement unit such as nanometers or micrometers,
            "dimensionX": pixel width of image,
            "dimensionY": pixel height of image,
            "creationTime": UTC time of image creation,
	“plateId”: the database id of the plate,
	“wellId”: the database id of the well,
	“imageId”: the database id of the image
}

Measure masks dag
Once the mask is created, the segmentation worker will post a message. The dag will listen for new messages in the subscription saved in the environment variable new_mask_subscription which is currently set to ix83-lipid-mask-uploaded-sub. The new masks will be inserted into the database and the information will be sends it along to the topic saved in the environment variable measure_mask_topic which is set to measure-ix83-lipid-mask.

Dag code is https://github.com/MelliCell/airflow-dags/blob/main/write_measurments.py 

Receives pubsub message from ix83-lipid-mask-uploaded-sub (base64 json string in message data)

{
 "image_id":  image id ,
        		"Plate_id":  plate id,
        		"plate":  plate name,
        		"well_id":  well id,
        		"well": well name,
        		"Culture_day": culture day of image taken,
        		"mask_uri": location of the mask in google cloud storage,
        		"Image_uri": location of the tiff image in google cloud storage,
        		"scale": ratio the image was scaled down for segmentation algorithm,
        		"model": model name used,
        		"channel_name": channel name of the image (this should be BF),
        		"original_um_per_pixel":  original micrometers per pixel of unscaled image,
        		"Size_x": pixel width of the mask,
        		"Size_y": pixel height of the mask
    	}


Sends pubsub message to measure-ix83-lipid-mask (attributes in the message, not in the data)

{
 "image_id":  image id ,
        		"Plate_id":  plate id,
        		"plate":  plate name,
        		"well_id":  well id,
        		"well": well name,
        		"culture_day": culture day of image taken,
        		"mask_uri": location of the mask in google cloud storage,
        		"Image_uri": location of the tiff image in google cloud storage,
        		"scale": ratio the image was scaled down for segmentation algorithm,
        		"model": model name used,
        		"channel_name": channel name of the image (this should be BF),
        		"original_um_per_pixel":  original micrometers per pixel of unscaled image,
        		"size_x": pixel width of the mask,
        		"size_y": pixel height of the mask
		“mask_id”: database ID of the mask
    	}
Confirmation of write dag

Once the mask measurement worker is done, and the measurements have been written to bucket, it will post a message confirming completion of the task. The dag listens to the subscription found in the environment variable measurments_written_subscription whose current values is ix83-lipid-morphology-write-sub. At the moment, all it will do is print them and nothing else.

The code can be found at https://github.com/MelliCell/airflow-dags/blob/main/confirm_masks_written.py 
It is of the same form as the previous message sent from the last dag.

Local Fluorescence Image Processing code

You need the production database for these to work. It relies heavily on the Image table in it. You will end up with JPG images for most of these and will need to check the quality of these images. To know what imaging analyses that need to be done, check out this spreadsheet: https://docs.google.com/spreadsheets/d/1IQGOs3xI515f6zvEIo0NUaAFl-qcQ60QDjX1zUE0WZM/edit?gid=0#gid=0. 
Helpful query for downstream analysis
The data you receive from these fluorescence analysis can be combined with treatment data to provide easier insight for screening. Below is an sql query for the mellitos-dev database you can use and then merge with the results either with a pandas dataframe or other means.

select 
  dose.concentration__unit,
  dose.concentration__value,
  treatment.name, 
  well_pos.well_name, 
  plate.name 
from 
  plate_map_assignment pma 
join 
  map_group on map_group.id = pma.map_group_id 
join 
  plate_map on plate_map.id = map_group.plate_map_id 
join 
  dose on dose.map_group_id = pma.map_group_id 
join 
  treatment on pma.treatment_id = treatment.id 
join 
  well_pos on well_pos.id = dose.well_pos_id 
join 
  treatment_regiment on treatment_regiment.id = pma.treatment_regiment_id 
join 
  plate on treatment_regiment.plate_id = plate.id 
where 
  plate.name = ‘Plate0174’;

You can save this as an SQL file and execute with one command

export PGPASSWORD=pass; psql -h localhost -U app -f query.sql --csv > test.csv
Fluorescents Viability pipeline (PI, Hoechst)
When Madhimita asks for viability analysis, this is the default method. You will need to know what wells are “PI positive wells” (i.e. dead wells) for the plate because the threshold used to calculate alive/dead cells is based on them. 

https://github.com/MelliCell/viability 

All you have to do is set up your environment and run the code with a command like the following

Python main.py –plate 0200 –day 13 –positive-wells A1 B1 C1 E1

NOTE: for some reason, apple treats environment variables differently. Password authentication will fail when trying to connect to the db and you may need to copy and paste the password into main.py. (don’t push the password to the git repository)

The output has the following folders
Filterd_image: images with only nuclei area with non-zero pixels
final : contains a csv with data filtered on size and a mask
Images: contains dapi images 
Intensity_image: contains images of other channels
Mask: contains skimage label arrays saved as tif
Measures: contains intensity measurements for each channel
report : contains the final analysis on viability
Fluorescent Viability pipeline (Calcein stain, Hoechst, Lipid Stain)
For additional confirmation, you can use this to measure viability.

https://github.com/MelliCell/viability-calcein 

This can be run exactly like the PI version

NOTE: for some reason, apple treats environment variables differently. Password authentication will fail when trying to connect to the db and you may need to copy and paste the password into main.py. (don’t push the password to the git repository)

The output has the following folders
Filterd_image: images with only nuclei area with non-zero pixels
final : contains a csv with data filtered on size and a mask
Images: contains dapi images 
Intensity_image: contains images of other channels
Mask: contains skimage label arrays saved as tif
Measures: contains intensity measurements for each channel
report : contains the final analysis on viability
Fluorescents Mitotracker pipeline (Hoechst, Lipid stain, mitotracker green)

When asked to perform mitotracker analysis, this is the default method.

Make sure cloud-sql-proxy is running in background for mellitos-dev db

https://github.com/MelliCell/mitotracker-analysis 

All you need to do is create a .env file that contains the connection details for the production-401712 database

	DBPASS
	DBUSER
	DBHOST
	DBNAME
	DBPORT

Then, you will want to set up your environment

	Python3 -m venv venv
	. venv/bin/activate && pip install -r requirements.txt

To run, all you need to do is set up cloud-sql-proxy for development-311316:us-east1:mellitos-dev and run the script

	./run.sh <plate number> <culture day>

It will download, segment, and measure the cells in the image. It will prompt you for a database password for development-311316:us-east1:mellitos-dev this is because it will try to pull the treatment data and combine it with the results. After it is finished, look for results.csv in the directory.

NOTE: for some reason, apple treats environment variables differently. Password authentication will fail when trying to connect to the db and you may need to copy and paste the password into main.py. (don’t push the password to the git repository)

Some other folders are also created
Filtered contains csv files after being filtered based on criteria 
Graphs 
Image: contains the jpg images
Masks: cell masks of images
Measurements: csv measurements of intensities
Fluorescents Differentiation pipeline (Hoechst, Lipid stain)
When asked to do differentiation analysis, this is the default method.
https://github.com/MelliCell/melli-image/tree/main 

This is  constructed as a python package. You can install it directly onto your computer (or virtual environment) with pip.

	‘pip install -e .’

You will need to add a .env file to the src directory in order for it to connect to the database with the following variables defined. This is for the database in ‘production-401712’

	DBPASS
	DBUSER
	DBHOST
	DBNAME
	DBPORT

After each use, it will create a ‘state.json’ to keep track of where it is in the event that it fails halfway through. It is super helpful for picking up where it last was and not repeating work, but should be deleted between uses. 

The code was set up to rely on the lipid channel to be in the TRITC channel. If the images you are working with don’t use TRITC for the lipids channel, you’ll need to change line 205 and 107 in src/image.py. On those lines, you’ll need to change “TRITC” to “Cy5”. 

NOTE: for some reason, apple treats environment variables differently. Password authentication will fail when trying to connect to the db and you may need to copy and paste the password into main.py. (don’t push the password to the git repository)

The following folders and files will be made:
<Plate name>: this will have the dapi image
Masks: will have the dapi nuclear segmentation mask
State.json: is the saved state of the pipeline (delete often)
<plate name>_differentation.csv: this is the results
New Pipeline Development

The current Mellitos App is set up to interact with Google’s DataFlow service, after an image set is uploaded and the upload step is completed, a pipeline bot will sift through the image sets and get what channels are present in them. For each image set, it will try to match it to a pipeline that requires the same channels to run. For example: if an image set has DAPI, TRTIC, and FITC channels; it will match it to a mitotracker pipeline that requires the same channels to work. Pipelines can be marked as active or in active to limit what is automatically queued. 

The pipelines are dataflow templates and are invoked using the dataflow service API. to learn more about how to build dataflow templates you can look at this overview: https://cloud.google.com/dataflow/docs/concepts/dataflow-templates. 

Invoking DataFlow pipelines from MellitOS
Dataflow pipelines can be invoked from anywhere. The following links are examples of how dataflow pipelines are called from mellitos or the upload service. 

From C#: https://github.com/MelliCell/OlympusListener/blob/main/FileSystemMonitor.cs (line 77)
Manual form: https://github.com/MelliCell/core-sample-api/blob/main/src/imaging/router.py (line 192)
With Bot: https://github.com/MelliCell/core-sample-api/blob/main/src/bots/pipeline.py 
New and Improved Olympus data import 
https://github.com/MelliCell/OlympusListener 

This new windows service will save files locally in an sqlite database file. When a new file is created, it is indexed in its local database. 

After a set amount of time, it will check if it is within a certain time of day (this time window is also in settings). If it is, it will then begin to upload all the images in its database that have not been uploaded.

Once gone through the uploading process, it will check for groups of files that are all uploaded (these are image sets). For each completely uploaded image set, it sends a dataflow pipeline request to convert and enter them into the MellitOS application. it calls the olympus conversion pipeline in dataflow with some parameters, but importantly, it tells the pipeline where the image set is located on google cloud. 

If the image set has files that failed to upload, it will not send a request for that image set. 
New Olympus image type conversion
https://github.com/MelliCell/cellsens-beam-pipeline 

Deployed as a dataflow flex template. This pipeline template can be requested to run from many locations such as the uploading service, gcloud CLI, or even the MellitOS app itself. The only real input needed is the parameter “prefix”: the location of the olympus vsi images for a given image set.

It will convert the images into tiff format and insert the image set into the database of mellitos-dev. It will also create an uploading step so that the upload bot can use it to process the next steps. It will fail if it cannot find an unassigned image request near the creation time of the images. 
New olympus Bright-field processing
https://github.com/MelliCell/cellpose-olympus-beam-pipeline 

Also deployed as a dataflow flex template, this pipeline will be called mostly from the upload bot in MellitOS. It will take the following parameters from a pipeline request.

Image_set_prefix: the directory for all images in a bucket (ex: gs://mellicell_developmnet_bucket/plate0186/day89/session1)
Secret_version: secret version in secret manager with database connection details
Image_request_id: pipeline request ID in mellitos DB
Pipeline_version_id: pipeline version ID in mellitos DB
Guidance for implementing new pipelines
The pipeline should be set up to accept four things:

Image_set_prefix: the directory for all images in a bucket (ex: gs://mellicell_developmnet_bucket/plate0186/day89/session1)
Secret_version: secret version in secret manager with database connection details
Image_request_id: pipeline request ID in mellitos DB
Pipeline_version_id: pipeline version ID in mellitos DB

For each pipeline type, make sure to save csv files to there own prefix, for example:

	For a mitotracker pipeline, maybe save all the csv files under 
gs://pipeline_results/mitotracker_versionXXX/.

this will be useful for using bigquery down the line. For example, if all csv files are saved in a folder gs://random_bucket/pipelines/specific_pipeline_version and share the same schema, bigquery can access it with SQL like queries. https://cloud.google.com/bigquery/docs/external-data-cloud-storage 






