FROM python:3.12

WORKDIR /code

RUN apt-get update
RUN apt install default-jdk -y

COPY requirements.txt /code/requirements.txt

RUN --mount=type=secret,id=secrets.json,target=/code/secrets.json
RUN pip install --upgrade pip
RUN pip install --upgrade -r /code/requirements.txt

# ENV PYTHONPATH=.

COPY ./src /code/src/
COPY ./pyproject.toml /code/
COPY ./static/css /code/static/css/
COPY ./static/js /code/static/js/
COPY ./static/img /code/static/img/

RUN pip install /code

CMD ["uvicorn", "app.main:app", "--host=0.0.0.0", "--port", "5001", "--workers", "3"]
# ENTRYPOINT ["python3", "api/main.py"]
