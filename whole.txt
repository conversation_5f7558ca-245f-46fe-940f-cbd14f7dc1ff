//Compared to v5.3, this version: 
// 1. incorporates a background filter that finds the average background signal in each image and adjusts accordingly
// 2. Does the multiplication after 16 bit conversion by 260 instead of 256 to max out the range
// 3. Increases the number of measurements captured within each nucleus, adding in more pixel statistics
// 4. Adds a seaprate file for tracking the statistics for the red masks in parallel with those for the raw red channel
// 5. File names are more specific

//--> for next version, 
// 1. explore breaking image into smaller windows to achieve more finite estimate of differentiation efficiency as function of cell density
// 2. Add plate names and days to exported excel files


//This function imports raw fluorescent images in a directory of choosing, 
//splits the channels, conducts a particle identification on the blue channel
//looking for nuclei and then uses those nuclei to compute the average
//red and green signals in each nucleus.
//
// * Macro template to process multiple images in a folder
// */

// Sets micron to pixel ratio (based on microscope optics)
MICRONSperPIX = 0.647948;
PIXperMICRONS = 1/MICRONSperPIX;
version = "6";

//Pop-up asking the location of the image files
#@ File (label = "Input directory", style = "directory") input

//Pop-up asking the desired save destination folders for nuclei masks,
//nuclei outlines, and spreadsheets
#@ File (label = "Blue Mask directory", style = "directory") BlueMaskDir
#@ File (label = "Outline directory", style = "directory") OutlineDir
#@ File (label = "Red Mask directory", style = "directory") RedMaskDir
#@ File (label = "Results directory", style = "directory") outputs

//Indicates the file type that will be processed
//Change the suffix to ".vsi" to process .vsi files instead
#@ String (label = "File suffix", value = ".tif") suffix



//This is the main function that runs all of the analysis
processFolder(input);

//Renaming the summary that comes after the function is run
IJ.renameResults("Summary","Well_Summary");
//Saves overall nuclear results per image
saveAs("Results", outputs+ "/Nuclei_Summary.csv");

//This line runs after everyhting else and will close all windows if desired
//run("Close All")


//Function definition (referenced in the function call above)
// function to scan folders/subfolders/files to find files with correct suffix
function processFolder(input) {
	list = getFileList(input);
	list = Array.sort(list);
	for (i = 0; i < list.length; i++) {
		if(File.isDirectory(input + File.separator + list[i]))
			processFolder(input + File.separator + list[i]);
		if(endsWith(list[i], suffix))
			processFile(input, BlueMaskDir, OutlineDir, outputs, list[i], i);
	}
}

//Function definintion (referenced in the function call above)
// Main function that analyzes a given file
function processFile(input, BlueMaskDir, OutlineDir, outputs, file, idx) {
print("processing"+file);
run("Bio-Formats", "open=" + input + File.separator + file + " autoscale color_mode=Default rois_import=[ROI manager] view=Hyperstack stack_order=XYCZT series_1");
//open(input + File.separator + file);
title = getTitle();
run("Set Scale...", "distance=1 known="+MICRONSperPIX+" unit=micron global"); 

//Can optionally decide to crop certain regions of a file, though 
//knowing the pixel dimensions is necessry
//if(getHeight() == 9421){
//makeRectangle(1744, 2432, 4624, 5102); (used for fluorescent analysis of PD0020 images)
//makeRectangle(1824, 2368, 5296, 4640); //(used for fluorescent analysis of PD0021 images from day 20)
//makeRectangle(1248, 1776, 4668, 4764); //(used for fluorescent analysis of PD0021 images from day 53)
//makeRectangle(1368, 1812, 4452, 4908); //(used for fluorescent analysis of PD0021 images from day 57)
//makeRectangle(1428, 1476, 4728, 4824); //(used for fluorescent analysis of PD0022 images from day 26)
//makeRectangle(1136, 1920, 6400, 5888); //(used for fluorescent analysis and cropping of PD0036 images from day 21)
//makeRectangle(4883, 3973, 570, 570); (used for fluorescent analysis and cropping of PD0043 images from day 21)
//makeRectangle(5263, 5136, 488, 460);
//makeRectangle(5232, 5926, 496, 488);
//makeRectangle(4394, 6453, 423, 396);
//makeRectangle(6854, 3012, 552, 516);
//makeRectangle(3798, 3325, 544, 508);
//makeRectangle(864, 2472, 488, 476);
//makeRectangle(4818, 932, 420, 336);
//makeRectangle(3695, 5516, 516, 468);
//makeRectangle(3313, 8355, 536, 460);
//makeRectangle(1872, 2568, 3996, 4092);//(used for zoomed in cropping of BF and fluorescent images of PD0061 for Day 13)
//makeRectangle(816, 1656, 6792, 6336);; //PD0053 Day 14 updating imageJ code with Gaussian Filters
//makeRectangle(976, 2064, 5664, 4640); //PD0061 Day 13
//makeRectangle(752, 1552, 5856, 4512); //PD0063 Day13
//makeRectangle(848, 1440, 5472, 4800);// PD00063 Day13 A3 only
//makeRectangle(1500, 1632, 4716, 4596); // (used for fluorescent analysis and cropping of PD0064 images from day 21)
makeRectangle(1392, 1728, 6024, 6024); // (used for fluorescent analysis and cropping of PD0053 images from day 14)
//makeRectangle(1464, 1776, 4776, 4452); // (used for fluorescent analysis and cropping of PD0064 images from day 13)
run("Crop");
//}

// Splits channels
run("Split Channels");

// Assigns C2 to green
//selectImage("C2" + "-" + title);
//Green_Image = getImageID();
//Green_filename = getTitle();
//Green_filename = replace(Green_filename, ".tif", "_tif");
//Green_filename = replace(Green_filename, ".vsi", "_vsi");  


// Assigns C1 to blue
selectImage("C1" + "-" + title);
Blue_Image = getImageID();
Blue_filename = getTitle();
Blue_filename = replace(Blue_filename, ".tif", "_tif");
Blue_filename = replace(Blue_filename, ".vsi", "_vsi"); 
rename(Blue_filename);
run("Duplicate...", "duplicate");
rename(Blue_filename + "_2");
Blue_filename_2 = getTitle();


// Assigns C3 to red
//selectImage("C3" + "-" + title);
//Red_Image = getImageID();
//Red_filename = getTitle();
//Red_filename = replace(Red_filename, ".tif", "_tif");
//Red_filename = replace(Red_filename, ".vsi", "_vsi");  

// Assigns C2 to deep red
selectImage("C2" + "-" + title);
Deep_Red_Image = getImageID();
Deep_Red_filename = getTitle();
Deep_Red_filename = replace(Deep_Red_filename, ".tif", "_tif");
Deep_Red_filename = replace(Deep_Red_filename, ".vsi", "_vsi");
rename(Deep_Red_filename);
run("Duplicate...", "duplicate");
rename(Deep_Red_filename + "_2");
Deep_Red_filename_2 = getTitle();




// Measures average intensity of green channel for each nucleus
//selectImage(Green_Image);
//roiManager("Measure");

//Saves all roi results from all images in separate spreadsheets
//saveAs("Results", Green_filename + "_Green_raw_results.csv");

//Saves all roi results from all images in one spreadsheet
//run("Read and Write Excel", "stack_results file=["+outputs+"/Green_nuclei_overlap.xlsx]");
//**Be careful because running the code multiple times will add to the same
//Excel file
//run("Clear Results");


// Measures average intensity of red channel for each nucleus
//selectImage(Red_Image);
//roiManager("Measure");

//Saves all roi results from all images in separate spreadsheets
//saveAs("Results", Red_filename + "_Red_raw_results.csv");

//Saves all roi results from all images in one spreadsheet
//run("Read and Write Excel", "stack_results file=["+outputs+"/Red__nuclei_overlap.xlsx]");
//**Be careful because running the code multiple times will add to the same
//Excel file
//close("Results");

// Measures average intensity of deep red channel for each nucleus
selectImage(Deep_Red_Image);

run("Duplicate...", "use");

run("Gaussian Blur...", "sigma=5 scaled");

Deep_Red_Image_Gauss = getImageID();
imageCalculator("Divide create", Deep_Red_Image,Deep_Red_Image_Gauss);
	
//setOption("ScaleConversions", true);


run("Multiply...", "value=20");
run("8-bit");
run("Convert to Mask");
//run("Adjustable Watershed", "tolerance=500");
run("Options...", "iterations=1 count=1 black do=Erode");
run("Options...", "iterations=1 count=1 black do=Dilate");
run("Options...", "iterations=1 count=1 black do=Dilate");
run("Options...", "iterations=1 count=1 black do=Close");
//run("Options...", "iterations=1 count=1 black do=Dilate");
//run("Options...", "iterations=1 count=1 black do=Erode");
run("Options...", "iterations=1 count=1 black do=Erode");
//run("Watershed"); //watershed in this case made seperations more clunckly- like large cobblestone pieces
//run("Adjustable Watershed", "tolerance=500");
run("Options...", "iterations=1 count=1 black do=[Fill Holes]");
//run("Adjustable Watershed", "tolerance=1000");
//run("Watershed");
Deep_Red_Positive_Mask = getImageID();

//Creates background mask to use for determining the background signal to subtract in the image
run("Duplicate...", "duplicate");
Deep_Red_Negative_Mask = getImageID();

run("Duplicate...", "duplicate");
run("Set...", "value=256");
White_mask = getImageID();

imageCalculator("Difference create", Deep_Red_Negative_Mask,White_mask);
run("16-bit");
run("Multiply...", "value=260");
Deep_Red_Negative_Mask_2 = getImageID();

imageCalculator("Min create", Deep_Red_Negative_Mask_2,Deep_Red_Image);
Deep_Red_Background_Mask = getImageID();
setThreshold(1, 65535);
run("Set Measurements...", "mean limit");
run("Measure");
background_signal = getValue("Mean");
//Probably still need to select only the values that are in the background, not the blank space...

//Switches back to computing the final red mask
selectImage(Deep_Red_Positive_Mask);
run("16-bit");
run("Multiply...", "value=260");
Deep_Red_Image_Mask = getImageID();


imageCalculator("Min create", Deep_Red_Image_Mask,Deep_Red_Image);
run("Subtract...", "value="+background_signal+"");

Deep_Red_final_filename = getTitle();
Deep_Red_final = getImageID();

run("Duplicate...", "duplicate");
saveAs("Tiff", RedMaskDir +  "/" + Deep_Red_final_filename + "_Red_Mask.tif");

//selectImage(Deep_Red_final);


// Find Nuclei from the blue channel by thresholding, creating a binary
//mask, filling holes, removing small pixels, and splitting joined objects

selectImage(Blue_Image);

run("Duplicate...", "use");
run("Gaussian Blur...", "sigma=5 scaled");
Blue_Image_Gauss = getImageID();
imageCalculator("Divide create", Blue_Image,Blue_Image_Gauss);
run("Multiply...", "value=100");
//run("Auto Threshold", "method=Moments white");
run("8-bit");
run("Convert to Mask");


//run("Subtract Background...", "rolling=50");
//run("8-bit");
//run("Auto Threshold", "method=Moments white");
//run("Convert to Mask");
//run("Despeckle");
run("Options...", "iterations=1 count=1 black do=Erode");
run("Options...", "iterations=1 count=1 black do=Dilate");
run("Options...", "iterations=1 count=1 black do=Dilate");
run("Options...", "iterations=1 count=1 black do=Close");
run("Options...", "iterations=1 count=1 black do=[Fill Holes]");
run("Options...", "iterations=1 count=1 black do=Erode");
run("Despeckle");
run("Watershed");
run("16-bit");
run("Multiply...", "value=256");

//Finds nuclei
Blue_Image_Mask = getImageID();


imageCalculator("Min create", Blue_Image_Mask,Blue_Image);
Blue_final_filename = getTitle();

run("Duplicate...", "duplicate");
run("Convert to Mask");
run("Fill Holes");

saveAs("Tiff", BlueMaskDir + "/" + Blue_filename + "_Hoechst_mask.tif");
//has to be binary image
//Collects an initial count of the nuceli in the image
MIN_DIAM = 5; //minimum nuclear diameter in um
MAX_DIAM = 25; //maximum nuclear diameter in um
PIXEL_AREA_MIN = 3.1415926*((MIN_DIAM/MICRONSperPIX)/2)*((MIN_DIAM/MICRONSperPIX)/2);
PIXEL_AREA_MAX = 3.1415926*((MAX_DIAM/MICRONSperPIX)/2)*((MAX_DIAM/MICRONSperPIX)/2);
setThreshold(PIXEL_AREA_MIN, PIXEL_AREA_MAX);
//run("Analyze Particles...", "size=65-600 show=Outlines display exclude clear include add");
//run("Set Measurements...", "area mean centroid shape display redirect=None decimal=2");

//setThreshold(0, 65535);
run("Set Measurements...", "area mean standard modal min centroid shape median area_fraction display redirect=None decimal=2");
//run("Set Measurements...", "area mean limit standard centroid shape display redirect=None decimal=2");

run("Analyze Particles...", "size="+PIXEL_AREA_MIN+"-"+PIXEL_AREA_MAX+" pixel circularity=0.25-1.00 show=Outlines display exclude clear summarize add slice");
saveAs("Tiff", OutlineDir +  "/" + Blue_filename + "_Outline.tif");
run("Read and Write Excel", "stack_results file=["+outputs+"/Nuclei_version_"+version+".xlsx]");
//**Be careful because running the code multiple times will add to the same
//Excel file
run("Clear Results");

//Measures nuclear signal in the red mask
//setThreshold(0, 65535);
//run("Set Measurements...", "area mean limit standard modal min centroid shape median area_fraction display redirect=None decimal=2");
selectImage(Deep_Red_final);
roiManager("Measure");

//Saves all roi results from all images in separate spreadsheets
//saveAs("Results", Deep_Red_filename + "_Deep_Red_raw_results.csv");

//Saves all roi results from all images in one spreadsheet
run("Read and Write Excel", "stack_results file=["+outputs+"/Deep_Red_overlap_per_nucleus_version_"+version+".xlsx]");
//**Be careful because running the code multiple times will add to the same
//Excel file
close("Results");


//Measures general pixel attributes in Green Channel
//selectImage(Green_Image);
//run("Set Measurements...", "area mean standard modal integrated median display redirect=None decimal=2");
//run("Measure");
//Saves all pixel attribute results from all images in one spreadsheet
//run("Read and Write Excel", "stack_results file=["+outputs+"/Green_pixel_values.xlsx]");
//close("Results");


//Measures general pixel attributes in Red Channel
//selectImage(Red_Image);
//run("Set Measurements...", "area mean standard modal integrated median display redirect=None decimal=2");
//run("Measure");
//Saves all pixel attribute results from all images in one spreadsheet
//run("Read and Write Excel", "stack_results file=["+outputs+"/Red_pixel_values.xlsx]");
//close("Results");

//Measures general pixel attributes in Deep Red Channel
selectImage(Deep_Red_Image);
setThreshold(0, 65535);
run("Set Measurements...", "area mean limit min standard modal integrated median display redirect=None decimal=2");
run("Measure");
//Saves all pixel attribute results from all images in one spreadsheet
setResult("height", 0, getHeight());
setResult("width", 0, getWidth());
setResult("Background_level",0,background_signal);
run("Read and Write Excel", "stack_results file=["+outputs+"/Deep_Red_pixel_values_raw_summary_version_"+version+".xlsx]");
close("Results");

//Measures general pixel attributes in Deep Red Channel
selectImage(Deep_Red_final);
setThreshold(0, 65535);
run("Set Measurements...", "area mean limit min standard modal integrated median display redirect=None decimal=2");
run("Measure");
//Saves all pixel attribute results from all images in one spreadsheet
setResult("height", 0, getHeight());
setResult("width", 0, getWidth());
setResult("Background_level",0,background_signal);
run("Read and Write Excel", "stack_results file=["+outputs+"/Deep_Red_pixel_values_mask_summary_version_"+version+".xlsx]");
close("Results");


// Merges raw red and blue channels

run("Merge Channels...", "c1=["+Deep_Red_filename_2+"] c3=["+Blue_filename_2+"] create keep");
run("Stack to RGB"); 


scale = 100; //Sets the size of the scalebar in um

// Adds 200 um scale bar

run("Scale Bar...", "width="+scale+" height=50 thickness=20 bold hide overlay");

// Saves cropped image in selected directory

saveAs("PNG", outputs + "/" + Blue_filename + "_raw_composite_cropped_"+scale+"um_scale_version_"+version+".png");



// Merges filtered red and blue channels

run("Merge Channels...", "c1=["+Deep_Red_final_filename+"] c3=["+Blue_final_filename+"] create keep");
run("Stack to RGB"); 


scale = 100; //Sets the size of the scalebar in um

// Adds 200 um scale bar

run("Scale Bar...", "width="+scale+" height=50 thickness=20 bold hide overlay");

// Saves cropped image in selected directory

saveAs("PNG", outputs + "/" + Blue_filename + "_filtered_composite_cropped_"+scale+"um_scale_version_"+version+".png");

//Closes all windows
//run("Close All");

//Closes open images only
close("*");

// Optional timestamp for timing how long processing of images takes
//getDateAndTime(year, month, dayOfWeek, dayOfMonth, hour, minute, second, msec);
//print(Green_filename + " - " + hour + ":" + minute + ":" + second);
}
