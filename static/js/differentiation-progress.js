// Differentiation analysis progress tracking
function initializeDifferentiationProgress(isetId) {
    const eventSource = new EventSource(`/imaging/iset/${isetId}/differentiation/progress`);
    const processedWells = new Set();
    let totalWells = 0;
    let hasError = false;
    
    function updateProgress(percent) {
        const bar = document.getElementById('progress-bar');
        bar.style.width = percent + '%';
        bar.innerHTML = percent + '%';
    }
    
    function updateStatus(message) {
        document.getElementById('status').innerText = message;
    }
    
    function updateWellStatus(well, status, message) {
        let wellDiv = document.getElementById(`well-${well}`);
        if (!wellDiv) {
            wellDiv = document.createElement('div');
            wellDiv.id = `well-${well}`;
            wellDiv.innerHTML = `<strong>Well ${well}:</strong> <span class="status"></span>`;
            document.getElementById('wells-container').appendChild(wellDiv);
        }
        
        const statusSpan = wellDiv.querySelector('.status');
        statusSpan.textContent = message;
        statusSpan.className = `status ${status}`;
    }
    
    eventSource.onmessage = function(event) {
        const data = JSON.parse(event.data);
        
        if (data.type === 'start') {
            totalWells = data.total;
            updateStatus(`Processing ${data.total} wells...`);
        }
        else if (data.type === 'processing') {
            updateProgress(data.progress);
            updateStatus(`Processing well ${data.well}...`);
            updateWellStatus(data.well, 'processing', 'Processing...');
        }
        else if (data.type === 'complete') {
            updateProgress(data.progress);
            processedWells.add(data.well);
            const pos = data.data.Positive || 0;
            const neg = data.data.Negative || 0;
            updateWellStatus(data.well, 'complete', `Complete: ${pos} positive, ${neg} negative`);
        }
        else if (data.type === 'error') {
            hasError = true;
            updateStatus(`Error: ${data.error}`);
            updateWellStatus(data.well, 'error', `Error: ${data.error}`);
        }
        else if (data.type === 'finished') {
            eventSource.close();
            updateStatus('Analysis complete!');
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        }
    };
    
    eventSource.onerror = function(event) {
        console.error('SSE error:', event);
        updateStatus('Connection error. Please refresh the page.');
    };
}