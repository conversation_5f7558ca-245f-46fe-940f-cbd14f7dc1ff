// Core functionality - rename from scripts.js
// Initialize DataTable
window.onload = function () {
    var table = new DataTable('table.stripe', {
        paging: false,
        select: true,
        dom: 'lrt'
    });
  
    var tabs = document.getElementsByClassName("tab");
    for (i = 0; i < tabs.length; i++) {
        tabs[i].style.display = "none";
    }

    table.on('select', function (e, dt, type, indexes) {
        if (type === 'row') {
            let inp = dt.table().node().id
            let ids = dt.rows({ selected: true }).data().map(function(row) { return row[0] }).join(',');
            console.log('Input: '+ inp + ' IDs: '+ids);

            // Set the value of the input field of all target forms
            var tag = '.submit-'+inp;
            var forms = document.querySelectorAll(tag);
            if (forms.length == 0) {
                tag = '.submit-form';
                forms = document.querySelectorAll(tag);
            }
            console.log('Forms for '+tag+': '+ forms+" "+forms.length);
            for (i = 0; i < forms.length; i++) {
                var form = forms[i];
                console.log('Form'+i+': '+ form);
                fe = form.elements[inp+'_id'];
                if (fe === undefined) {
                    fe = form.elements[inp];
                }
                console.log('Form element: '+ fe);
                fe.value = ids;
            }

            // Select the tab in a tab list
            var notab = document.getElementById('notab');
            var tabs = document.getElementsByClassName('tab');
            var tab = document.getElementById('tab-'+ids);
            if (tab) {
                console.log('Tab: '+ tab);
                if (notab) notab.style.display = 'none';
                for (i = 0; i < tabs.length; i++) {
                    tabs[i].style.display = 'none';
                }
                tab.style.display = 'block';
            }
        }
    });

    table.on('dblclick', function (e) {
        //let tid = this.id;
        var row = e.target.closest("tr");                          
        let id = row.children[0].innerText;                       
        let href = $(this).attr('href');   
        console.log('Double click! '+id+' '+href);
        if (href !== undefined) location.href = href+id
        else {
            var tabCtrl = document.getElementById('tabs');
            console.log('Tab control: '+tabCtrl);
            var pageToActivate = document.getElementById('tab-'+id);
            if (pageToActivate && tabCtrl) {
                console.log('Activate tab '+id);
                for (var i = 0; i < tabCtrl.childNodes.length; i++) {
                    var node = tabCtrl.childNodes[i];
                    if (node.nodeType == 1) { /* Element */
                        node.style.display = (node == pageToActivate) ? 'block' : 'none';
                    }
                }
            }
        }
    });

    // Plate map selection
    var plate = document.getElementById('plate-select');
    if (plate) {
        var group_form = document.querySelector('form.submit-form');
        var group_select = document.getElementById('plate-select-group');
        var reset_button = document.getElementById('plate-select-reset');
        var cell = undefined;
        function well(event) {
            var target = event.target;
            if (!target.hasAttribute('pos')) { return undefined; }
            if (target == cell) return undefined; 
            cell = target;
            return target;
        }
        function select(cell, g) {
            cell.setAttribute('group', g);
            cell.style.backgroundColor = 'gray';
            cell.innerText = `${g}`;
        }
        function deselect(cell) {
            cell.removeAttribute('group');
            cell.style.backgroundColor = 'lightgray';
            cell.innerText = '';
        }
        function coorX(w) {
            var pos = w.getAttribute('pos');
            return (parseInt(pos.slice(1))-1);
        }
        function coorY(w) {
            var pos = w.getAttribute('pos');
            return "ABCDEFGHIJKLMNOPQRSTUVXYZ".indexOf(pos.slice(0, 1));
        }
        var x0 = undefined;
        var y0 = undefined;
        function select_block(w1) {
            if (x0===undefined || y0===undefined) return;
            const x1 = coorX(w1);
            const y1 = coorY(w1);
            const g = group_select.value;
            console.log(`Select block: ${x0} ${y0} ${x1} ${y1}`);
            for (var x = x0; x <= x1; x++) {
                for (var y = y0; y <= y1; y++) {
                    const pos = "ABCDEFGHIJKLMNOPQRSTUVXYZ"[y] + (x + 1);
                    w = document.querySelector(`#plate-select div[pos="${pos}"]`);
                    if (w) select(w, g);
                }
            }
        };
        function update_form() {
            const inps = group_form.querySelectorAll('input[name^="group_"]');
            console.log(`Update form: ${inps.length}`);
            for (const inp of inps) {
                const g = parseInt(inp.name.split('_')[1]);
                var wells = document.querySelectorAll(`#plate-select div[group="${g}"]`);
                var pos = [];
                for (var i = 0; i < wells.length; i++) {
                    pos.push(wells[i].getAttribute('pos'));
                }
                inp.value = pos.join(',');
            }
        }

        // Event listeners for the buttons
        reset_button.addEventListener('click', (event) => {
            console.log('Reset');
            var wells = document.querySelectorAll('#plate-select div[pos]');
            for (var i = 0; i < wells.length; i++) {
                deselect(wells[i]);
            }
            update_form();
        });
        group_select.onchange = (event) => {
            console.log('Group change');
            var wells = document.querySelectorAll('#plate-select div[pos]');
            for (var i = 0; i < wells.length; i++) {
                g = wells[i].getAttribute('group');
                if (g && g != group_select.value) {
                    wells[i].style.backgroundColor = 'darkgray';
                }
            }
        };

        // Event listeners for the plate
        plate.addEventListener('mousemove', (event) => {
            event.preventDefault();
            event.stopPropagation();
            if (event.buttons != 1) { return; }   // only when the left mouse button is pressed
            var cell = well(event);
            if (!cell) return;
            var pos = cell.getAttribute('pos');
            console.log(`Mouse move: ${cell} ${pos}`);
            select_block(cell)
        });
        plate.addEventListener('mousedown', (event) => {
            event.preventDefault();
            event.stopPropagation();
            console.log(`Mouse down ${event.buttons}`);
            var w = well(event);
            if (!w) return;
            pos = w.getAttribute('pos');
            x0 = coorX(w);
            y0 = coorY(w);
            console.log(`Mouse down: ${pos} ${x0} ${y0}`);
        });
        plate.addEventListener('mouseup', (event) => {
            event.preventDefault();
            event.stopPropagation();
            if (x0!==undefined || y0!==undefined) {
                x0 = undefined;
                y0 = undefined;
                update_form();
            }
        });
        plate.addEventListener('click', (event) => {
            event.preventDefault();
            event.stopPropagation();
            console.log(`Mouse click ${event.buttons}`);
            cell = undefined;   // not dragging any more
            var w = well(event);
            if (w) {
                var pos = w.getAttribute('pos');
                console.log(`Mouse click: ${cell} ${pos}`);

                if (w.hasAttribute('group')) {
                    deselect(cell);
                } else {
                    select(cell, group_select.value);
                }
            } else {
                if (event.target.classList.contains("label")) {
                    var coor = event.target.innerText
                    var row = plate.querySelectorAll(`div[pos^="${coor}"]`);
                    var col = plate.querySelectorAll(`div[pos$="${coor}"]`);
                    var all = [...row, ...col];
                    var g = group_select.value;
                    var n = 0;
                    var ns = 0;
                    for (const w of all) {
                        var pos = w.getAttribute('pos');
                        if (row.length>0 || pos.slice(1)==coor) {
                            n++;
                            if (w.hasAttribute('group') && w.getAttribute('group') == g) {
                                ns++;
                            }
                            //console.log(`  Row/Col: ${pos}`);
                        }
                    }
                    for (const w of all) {
                        var pos = w.getAttribute('pos');
                        if (row.length>0 || pos.slice(1)==coor) {
                            if (n == ns) {
                                deselect(w);
                            } else {
                                select(w, g);
                            }
                        }
                    }
                }
            }
            update_form();
        });
    }
}