// Well differentiation image viewer with cropping
function initializeDifferentiationViewer(config) {
    const { cropWidth, cropHeight, boundsEnabled, imageTypes, imageLabels, plate, day, well, isetId } = config;
    
    // Add scale bar toggle to controls
    const controlsContainer = document.querySelector('.controls') || document.body;
    const scaleBarToggle = document.createElement('label');
    scaleBarToggle.innerHTML = `
        <input type="checkbox" id="scaleBarEnabled" checked> Show scale bars
    `;
    controlsContainer.appendChild(scaleBarToggle);
    
    // Update crop loading to include scale bar parameter
    function loadCrop(x, y, width, height, imageType) {
        const scaleBarEnabled = document.getElementById('scaleBarEnabled').checked;
        const url = `/imaging/differentiation/crop/${plate}/${day}/${well}/${imageType}?` +
            `iset_id=${isetId}&x=${x}&y=${y}&width=${width}&height=${height}&scale_bar=${scaleBarEnabled}`;
        
        return fetch(url)
            .then(response => response.blob())
            .then(blob => URL.createObjectURL(blob));
    }
    
    const mainImg = document.getElementById('main-image');
    const gridCanvas = document.getElementById('grid-overlay');
    const cropsContainer = document.getElementById('crops-container');
    const cropInfo = document.getElementById('crop-info');

    let scaleX = 1;
    let scaleY = 1;
    let originalWidth = 0;
    let originalHeight = 0;
    let cropBounds = null;
    let currentCrop = null;

    const imageDescriptions = {
        "main_image": "Composite fluorescence image showing DAPI (blue) and lipid channels (red)",
        "visualization": "Color segmentation mask with lipid droplets (red), nuclei (blue) and their overlap (magenta)",
        "nuclei_mask.png": "Binary segmentation mask identifying individual cell nuclei",
        "nuclei_intensity_mask.png": "16-bit signal-enhanced nuclei mask with background correction and robust thresholding",
        "lipid_mask.png": "Binary segmentation mask identifying lipid droplet regions",
        "lipid_intensity_mask.png": "16-bit signal-enhanced lipid mask with background correction and robust thresholding"
    };

    const hoverDescriptions = {
        "main_image": "Composite fluorescence microscopy image displaying the raw DAPI channel (blue, excitation 358nm/emission 461nm) for nuclear visualization and the lipid-specific channel (red, Cy5 647/665nm or TRITC 550/570nm) for adipogenic differentiation assessment. This represents the unprocessed 16-bit fluorescence data acquired at high resolution, serving as the foundation for all subsequent computational image analysis workflows.",
        "visualization": "Computational segmentation overlay displaying the results of automated image analysis pipelines. Red regions indicate segmented lipid droplets identified through Gaussian smoothing (σ=1) followed by Otsu thresholding and morphological dilation operations. Blue regions represent segmented cell nuclei detected via difference-of-Gaussians filtering and watershed segmentation algorithms. Magenta overlap regions indicate differentiated cells containing both nuclear and lipid components.",
        "nuclei_mask.png": "Binary segmentation mask generated through automated nuclei detection using difference-of-Gaussians filtering (σ=1, σ=12) followed by multi-Otsu thresholding and watershed segmentation. Individual nuclei are labeled with unique integer values enabling quantitative analysis of cellular differentiation status. Edge cropping (15% border exclusion) eliminates well boundary artifacts. This mask serves as the foundation for single-cell analysis and differentiation quantification.",
        "nuclei_intensity_mask.png": "Signal-enhanced nuclei detection mask created through Gaussian background correction (σ=5) and 100x signal amplification, followed by adaptive thresholding with multiple fallback methods (triangle/Otsu, local adaptive, outlier-robust). Uses plate-specific parameters and morphological operations optimized for different cell types. Eliminates cross-channel contamination while preserving quantitative intensity gradients within detected nuclear regions.",
        "lipid_mask.png": "Binary lipid droplet segmentation mask generated using Gaussian smoothing (σ=1) and Otsu thresholding, followed by morphological dilation to connect fragmented droplet regions. Edge regions (15% border) are systematically excluded to eliminate well boundary artifacts that interfere with automated thresholding algorithms. This mask identifies adipogenic differentiation markers and enables quantitative lipid accumulation analysis.",
        "lipid_intensity_mask.png": "Signal-enhanced lipid detection mask created through Gaussian background correction (σ=5) and signal amplification, followed by adaptive thresholding with robust fallback methods (Otsu, local adaptive, percentile-based). Uses plate-specific morphological operations and handles extreme contrast variations that break global thresholding. Preserves original fluorescence intensity values within segmented lipid regions while eliminating imaging artifacts."
    };

    function updateGridDimensions() {
        const newWidth = document.getElementById('cropWidth').value;
        const newHeight = document.getElementById('cropHeight').value;
        const boundsEnabled = document.getElementById('boundsEnabled').checked;
        const currentUrl = new URL(window.location);
        currentUrl.searchParams.set('crop_width', newWidth);
        currentUrl.searchParams.set('crop_height', newHeight);
        currentUrl.searchParams.set('bounds_enabled', boundsEnabled);
        window.location.href = currentUrl.toString();
    }

    function calculateCropBounds(clickX, clickY, cropW, cropH, imgW, imgH) {
        if (!boundsEnabled) {
            let x = Math.max(0, clickX - Math.floor(cropW / 2));
            let y = Math.max(0, clickY - Math.floor(cropH / 2));
            
            x = Math.min(x, imgW - cropW);
            y = Math.min(y, imgH - cropH);
            
            return { x, y, width: cropW, height: cropH };
        }
        
        const margin = 0.15;
        const validLeft = Math.floor(imgW * margin);
        const validTop = Math.floor(imgH * margin);
        const validRight = Math.floor(imgW * (1 - margin));
        const validBottom = Math.floor(imgH * (1 - margin));
        
        if (clickX < validLeft || clickX > validRight || clickY < validTop || clickY > validBottom) {
            return null;
        }
        
        let x = Math.max(validLeft, clickX - Math.floor(cropW / 2));
        let y = Math.max(validTop, clickY - Math.floor(cropH / 2));
        
        x = Math.min(x, validRight - cropW);
        y = Math.min(y, validBottom - cropH);
        
        return { 
            x, y, 
            width: Math.min(cropW, validRight - x), 
            height: Math.min(cropH, validBottom - y) 
        };
    }

    function showCrops(x, y, width, height) {
        // Store current crop for persistence
        currentCrop = { x, y, width, height };
        
        cropInfo.innerHTML = `Crop: x=${x}, y=${y}, width=${width}, height=${height}`;
        cropsContainer.innerHTML = '';
        
        // Draw crop rectangle
        drawCropRectangle(x, y, width, height);
        
        imageTypes.forEach((row, rowIndex) => {
            const rowDiv = document.createElement('div');
            rowDiv.style = "display: flex; gap: 15px; margin-bottom: 25px;";
            
            row.forEach((type, colIndex) => {
                const container = document.createElement('div');
                container.style = "text-align: center; flex: 1; position: relative;";
                const title = document.createElement('h5');
                title.textContent = imageLabels[rowIndex][colIndex];
                title.style = "margin: 5px 0; font-size: 16px; font-weight: bold;";
                const description = document.createElement('p');
                description.textContent = imageDescriptions[type] || "Image analysis result";
                description.style = "margin: 5px 0 10px 0; font-size: 14px; color: #666; font-style: italic; line-height: 1.3;";
                
                const img = document.createElement('img');
                img.style = "width: 300px; height: 300px; object-fit: contain; display: block; margin: 0 auto; cursor: pointer; transition: transform 0.2s ease;";
                img.onerror = function() { container.style.display = 'none'; };
                
                // Use the loadCrop function instead of inline URL construction
                loadCrop(x, y, width, height, type).then(url => {
                    img.src = url;
                    img.alt = type;
                });
                
                //Hover effects
                const tooltip = document.createElement('div');
                tooltip.style = `
                    position: absolute;
                    background: rgba(0, 0, 0, 0.92);
                    color: white;
                    padding: 16px;
                    border-radius: 8px;
                    font-size: 12px;
                    line-height: 1.5;
                    max-width: 420px;
                    z-index: 1000;
                    display: none;
                    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
                    border: 1px solid #444;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                `;
                tooltip.textContent = hoverDescriptions[type] || "Detailed image analysis information";
                
                // Hover effects
                img.addEventListener('mouseenter', function(e) {
                    img.style.transform = 'scale(1.05)';
                    tooltip.style.display = 'block';
                    tooltip.style.left = '-30%';
                    tooltip.style.top = '0px';
                    tooltip.style.transform = 'translateY(-50%)';
                });
                
                img.addEventListener('mouseleave', function() {
                    img.style.transform = 'scale(1)';
                    tooltip.style.display = 'none';
                });
                
                container.appendChild(title);
                container.appendChild(description);
                container.appendChild(img);
                container.appendChild(tooltip);
                rowDiv.appendChild(container);
            });
            
            cropsContainer.appendChild(rowDiv);
        });
    }

    function drawCropRectangle(x, y, width, height) {
        const ctx = gridCanvas.getContext('2d');
        ctx.clearRect(0, 0, gridCanvas.width, gridCanvas.height);
        
        // Draw boundary if bounds are enabled
        if (boundsEnabled && cropBounds) {
            const { validLeft, validTop, validWidth, validHeight } = cropBounds;
            ctx.strokeStyle = 'rgba(0, 255, 0, 0.5)';
            ctx.lineWidth = 1;
            ctx.strokeRect(validLeft, validTop, validWidth, validHeight);
            ctx.fillStyle = 'rgba(0, 255, 0, 0.05)';
            ctx.fillRect(validLeft, validTop, validWidth, validHeight);
        }
        
        // Draw current crop rectangle (always visible once set)
        if (x !== undefined && y !== undefined) {
            const displayX = x / scaleX;
            const displayY = y / scaleY;
            const displayWidth = width / scaleX;
            const displayHeight = height / scaleY;
            
            ctx.strokeStyle = '#ffffffff';
            ctx.lineWidth = 2;
            ctx.strokeRect(displayX, displayY, displayWidth, displayHeight);
            ctx.fillStyle = 'rgba(255, 0, 0, 0.1)';
            ctx.fillRect(displayX, displayY, displayWidth, displayHeight);
        }
    }

    function redrawCanvas() {
        // Redraw with current crop if it exists
        if (currentCrop) {
            drawCropRectangle(currentCrop.x, currentCrop.y, currentCrop.width, currentCrop.height);
        } else if (boundsEnabled && cropBounds) {
            // Just draw bounds if no crop selected
            const ctx = gridCanvas.getContext('2d');
            ctx.clearRect(0, 0, gridCanvas.width, gridCanvas.height);
            const { validLeft, validTop, validWidth, validHeight } = cropBounds;
            ctx.strokeStyle = 'rgba(0, 255, 0, 0.3)';
            ctx.lineWidth = 1;
            ctx.strokeRect(validLeft, validTop, validWidth, validHeight);
        }
    }

    mainImg.onload = function() {
        fetch(`/imaging/differentiation/dimensions/${plate}/${day}/${well}?iset_id=${isetId}`)
            .then(response => response.json())
            .then(data => {
                originalWidth = data.width;
                originalHeight = data.height;
                scaleX = originalWidth / mainImg.width;
                scaleY = originalHeight / mainImg.height;
                
                if (boundsEnabled) {
                    const margin = 0.15;
                    const validLeft = Math.floor(mainImg.width * margin);
                    const validTop = Math.floor(mainImg.height * margin);
                    const validWidth = Math.floor(mainImg.width * (1 - 2 * margin));
                    const validHeight = Math.floor(mainImg.height * (1 - 2 * margin));
                    
                    cropBounds = { validLeft, validTop, validWidth, validHeight };
                } else {
                    cropBounds = null;
                }
                
                gridCanvas.width = mainImg.width;
                gridCanvas.height = mainImg.height;
                gridCanvas.style.pointerEvents = 'auto';
                
                // Initial draw
                redrawCanvas();
            });
    };

    gridCanvas.addEventListener('click', function(e) {
        const rect = gridCanvas.getBoundingClientRect();
        const displayX = e.clientX - rect.left;
        const displayY = e.clientY - rect.top;
        
        const originalClickX = Math.floor(displayX * scaleX);
        const originalClickY = Math.floor(displayY * scaleY);
        
        const bounds = calculateCropBounds(
            originalClickX, 
            originalClickY, 
            cropWidth, 
            cropHeight, 
            originalWidth, 
            originalHeight
        );
        
        // Only show crops if click was in valid area
        if (bounds) {
            showCrops(bounds.x, bounds.y, bounds.width, bounds.height);
        }
    });

    if (boundsEnabled) {
        gridCanvas.addEventListener('mouseenter', function() {
            if (cropBounds) {
                const ctx = gridCanvas.getContext('2d');
                const { validLeft, validTop, validWidth, validHeight } = cropBounds;
                ctx.clearRect(0, 0, gridCanvas.width, gridCanvas.height);    
                ctx.strokeStyle = 'rgba(156, 154, 154, 0.8)';
                ctx.lineWidth = 2;
                ctx.strokeRect(validLeft, validTop, validWidth, validHeight);
                ctx.fillStyle = 'rgba(0, 255, 0, 0.05)';
                ctx.fillRect(validLeft, validTop, validWidth, validHeight);
                
                // Redraw current crop if it exists
                if (currentCrop) {
                    const { x, y, width, height } = currentCrop;
                    const displayX = x / scaleX;
                    const displayY = y / scaleY;
                    const displayWidth = width / scaleX;
                    const displayHeight = height / scaleY;
                    
                    ctx.strokeStyle = '#ffffffff';
                    ctx.lineWidth = 2;
                    ctx.strokeRect(displayX, displayY, displayWidth, displayHeight);
                    ctx.fillStyle = 'rgba(255, 0, 0, 0.1)';
                    ctx.fillRect(displayX, displayY, displayWidth, displayHeight);
                }
            }
        });

        gridCanvas.addEventListener('mouseleave', function() {
            // Redraw normal state (keeps crop visible)
            redrawCanvas();
        });
    }

    window.updateGridDimensions = updateGridDimensions;
}
