from images.parse import java_vm
from images.repo import Repo
from common import fill_image_sets, fill_images
from base.logs import make_logger
log = make_logger("pd_plates")

doit = True
with java_vm() as vm:
    for repo_name in ["cytosmart", "olympus"]:
        repo = Repo(repo_name)
        plates = repo.image_sets.all_plates()
        todo = [plate 
            for plate in plates 
            if plate.plate_names and plate.plate_names[0].startswith("PD")
        ]
        log(f"Found {len(todo)} plates to backfill")

        for pis in todo:
            fill_image_sets(repo.name, pis, doit=doit)
            fill_images(repo.name, pis, doit=doit)
log("Done with backfill.")